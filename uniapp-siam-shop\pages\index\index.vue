<template>
	<view class="content">
		<uni-status-bar></uni-status-bar>
		<view class="handerTitle">
			<view class="handerTitle-box flex flex-align-center">
				<view class="icon-menu-pic" @click.stop="handclick">
					<image class="icon-menu" v-if="leftIcon" src="./static/menu.png" mode="widthFix"></image>
				</view>
				<view class="handerTitle-name flex flex-align-center justify-space-center" @click="meetOrder">
					<text>{{ UserInfo.isOperating == true ? '营业' : '打烊' }}</text>
					<uni-icons class="handerTitle-name-icon" :class="{ 'handerTitle-name-icon-rotate': downShow }" type="arrowright"></uni-icons>
				</view>
				<view class="icon-menu-pic"></view>
			</view>
			<view class="downMenu" v-if="downShow">
				<view class="downMenu-ls flex flex-align-center" @click="UpLine(1, true)">
					<view class="downMenu-ls-status" :style="{ background: UserInfo.isOperating == '1' ? '#1bb93a' : 'transparent' }"></view>
					<text class="downMenu-ls-name">营业</text>
				</view>
				<view class="downMenu-ls flex flex-align-center" @click="UpLine(2, true)">
					<view class="downMenu-ls-status" :style="{ background: UserInfo.isOperating == '2' ? '#f11616' : 'transparent' }"></view>
					<text class="downMenu-ls-name">打烊</text>
				</view>
			</view>
		</view>
		<view class="content-box" @click="downShow = false">
			<!--  :style="{ top: fileList.length ? '254rpx' : '74rpx' }" -->
			<custom-tabs-swiper ref="tabsSwiper" :list="menuList" active-color="#fa7c25" inactive-color="#333" bg-color="#fa7c25"
				:show-line="true" @changePage="changePage">
<!--				<swiper class="swiper" v-if="fileList.length" :indicator-dots="true" :circular="true" :autoplay="true"-->
<!--					:interval="5000" :duration="500" indicator-active-color="#fff">-->
<!--					<template v-for="item of fileList">-->
<!--						<swiper-item :key="item.id">-->
<!--							<image style="width: 100%;" :src="item.icon" mode="widthFix"></image>-->
<!--						</swiper-item>-->
<!--					</template>-->
<!--				</swiper>-->
				<view class="tips-info" v-if="list.length">
					<u-notice-bar mode="vertical" color="#999" font-size="24" :list="list" @click="noticeIndex">
					</u-notice-bar>
				</view>
				<!-- <template slot="swiper-item-1">
					<order-list v-if="this.changePageNum == 0" ref="oreder_0" status="2" @openRobOrder="openRobOrder" @setDial="setDial"></order-list>
				</template> -->
				<template slot="swiper-item-1">
					<order-list v-if="this.changePageNum == 0" ref="oreder_0" status="4" @openRobOrder="openRobOrder" @setDial="setDial"></order-list>
				</template>
				<template slot="swiper-item-2">
					<order-list v-if="this.changePageNum == 1" ref="oreder_1" status="5" @openRobOrder="openRobOrder" @setDial="setDial"></order-list>
				</template>
				<template slot="swiper-item-3">
					<order-list v-if="this.changePageNum == 2" ref="oreder_2" status="6" @openRobOrder="openRobOrder" @setDial="setDial"></order-list>
				</template>
				<template slot="swiper-item-4">
					<order-list v-if="this.changePageNum == 3" ref="oreder_3" status="-1" @openRobOrder="openRobOrder" @setDial="setDial"></order-list>
				</template>
				<!-- <template slot="swiper-item-6">
					<order-list ref="oreder_5" status="6" @openRobOrder="openRobOrder" @setDial="setDial"></order-list>
				</template> -->
			</custom-tabs-swiper>
		</view>
		<view class="kong"></view>
		<!-- 底部样式 -->
		<view class="footerBtn" @click="UpLine(UserInfo.isOperating == '1' ? 2 : 1, false)" v-if="UserInfo.isOperating == '2'">
			<text>{{ UserInfo.isOperating == '1' ? '立即打烊' : '立即营业' }}</text>
			<uni-icons :type="UserInfo.isOperating == '1' ? 'arrowthindown' : 'arrowthinup'" color="#fff"></uni-icons>
		</view>
		<view class="footerBtn-t flex flex-align-center" v-else>
			<view class="footerBtn-t-box flex flex-direction-column flex-align-center" style="background: azure;" @click="openRiderSet">
				<view class="icon-t">
					<u-icon name="car" size="40" color="#666"></u-icon>
				</view>
				<text class="r-text">外卖订单</text>
			</view>
			<view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="tabChange('/pages/pickUpIndex/index')">
				<view class="icon-t">
					<u-icon name="order" color="#666" size="40"></u-icon>
				</view>
				<text class="r-text">自提订单</text>
			</view>
      <view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="tabChange(`/pages/goods/list?shopId=${UserInfo.shopId}`)">
				<view class="icon-t">
					<u-icon name="shopping-cart" color="#666" size="40"></u-icon>
				</view>
				<text class="r-text">商品管理</text>
			</view>
			<!-- <view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="openShopSet">
				<view class="icon-t">
					<uni-icons type="shop" size="40" color="#666"></uni-icons>
				</view>
				<text class="r-text">门店管理</text>
			</view> -->
			<view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="openMineSet">
				<view class="icon-t">
					<uni-icons type="person" size="40" color="#666"></uni-icons>
				</view>
				<text class="r-text">我的</text>
			</view>
			<!-- <view class="footerBtn-t-cent" @click="handClickRefresh">
				<uni-icons type="loop" size="30" color="#ee8131"></uni-icons>
				<text class="c-text">刷新列表</text>
			</view>
			<view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="change(1)">
				<view class="icon-t">
					<u-icon name="order" color="#666" size="40"></u-icon>
				</view>
				<text class="r-text">自提订单</text>
			</view> -->
		</view>

		<!-- 当前页面弹窗区域 -----↓------- -->

		<!-- 打开认证 -->
		<button class="bot-btn" type="default" @click="handClickoOpen" v-if="UserInfo.auditStatus != 2">获取接单资格</button>
		<!-- 滑动验证 -->
		<u-popup v-model="slideBox" mode="bottom" height="50%" :closeable="true">
			<view class="slideBox-title">确人成功送达</view>
			<view class="slideBox-cont">狮城·风华新都(10栋楼底)</view>
			<view class="slideBox-info">王先生 尾号4332</view>
			<move-verify @result="verifyResult" ref="verifyElement"></move-verify>
		</u-popup>
		<!-- 左菜单弹窗 -->
		<pop-page ref="popPage" :UserInfo="UserInfo"></pop-page>
		<!-- 认证弹窗 -->
		<popup-one ref="PopupOne"></popup-one>
		<!-- 打开设置弹窗 -->
		<seting-pop ref="openSet"></seting-pop>
		<!-- 抢单弹窗 -->
		<custom-modal ref="orderShow" :config="config" @confirm="confirmRobOrder"></custom-modal>
		<u-modal v-model="showModal"
			title="温馨提示"
			confirm-text="立即认证"
			content="为保证配送服务安全监督，请您先进行实名认证以及完善资料!"
			:show-cancel-button="true"
			confirm-color="#f40000"
			@confirm="handClickoOpen">
		</u-modal>
		<u-modal v-model="telShow"
			:title="setDialInfo.title"
			:show-cancel-button="true"
			confirm-text="确认拨打"
			:content="'拨号给：' + (setDialInfo.tel || '')"
			@confirm="$common.dialFun(setDialInfo.tel)">
		</u-modal>
<!-- 		<zy-upg @close="DataUp = false" v-if="DataUp"></zy-upg> -->
	</view>
</template>

<script>
// import ZyUpg from "@/components/zy-upgrade/zy-upgrade.vue"
import { mapState } from 'vuex';
import CustomTabsSwiper from '../../components/custom-tabs-page-linkge/tab-page-linkage.vue'; // 全屏联动
import PopPage from './components/popup.vue'; // 左菜单弹窗
import SetingPop from './components/setingPop.vue'; // 骑手设置弹窗
import CustomModal from '@/components/custom-modal/custom-modal.vue'; // 抢单弹窗
import OrderList from './component/order-list.vue'; // 新订单
import PopupOne from './components/PopupOne.vue'; // 底部认证
import MoveVerify from '@/components/helang-moveVerify/helang-moveVerify.vue'; // 滑动验证

// import ToUpdate from '@/components/zy-upgrade/zy-upgrade.vue';

export default {
	components: {
		CustomTabsSwiper,
		OrderList,
		PopPage,
		SetingPop,
		PopupOne,
		MoveVerify,
		// ToUpdate,
		// ZyUpg,
		CustomModal
	},
	data() {
		return {
			leftIcon: './static/menu.png',
			orderOpObj: null,
			telShow: false,
			tel: '',
			showModal: false,
			downShow: false,
			slideBox: false,
			x: '9rpx',
			y: '9rpx',
			fileList: [],
			DataUp: true,
			menuList: [
				// { name: '待接单' },
				{ name: '待配送' },
				{ name: '配送中' },
				{ name: '已完成' },
				// { name: '已取消/退款' }
				{ name: '退款' }
			],
			list: [],
			noticeInfo: [],
			changePageNum: 0,
			// 订单操作弹窗参数配置
			config: {
				icon: '',
				title: '',
				topBtnText: '确认送达',
				topBtnBgColor: 'linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))',
				botBtnText: '取消',
				botBtnBgColor: 'transparent'
			},
			// 拨打电话弹窗配置
			setDialInfo: { title: '是否拨打电话', tel: '' }
		};
	},
	computed: {
		...mapState(['UserInfo', 'address', 'configInfo']),
		topNumShow() {
			const show = this.list.length ? true : false;
			return show;
		}
	},
	onLoad() {},
	onShow() {
		console.log('this.UserInfo:', this.UserInfo)
		// 判断是否认证弹窗
		// this.$nextTick(() => {
		// 	if (this.UserInfo.auditStatus == 0) {
		// 		this.$interactive.ShowToast({ title: '请先进行实名认证！' }).then(() => {
		// 			this.$refs.PopupOne.open(true);
		// 		});
		// 	}
		// });
		this.getBannerList(); // 获取轮播图列表
		/*	this.getNoticeInfo(); // 获取公告信息，绑定全局方法*/
		uni.$emit('RefreshOrderlist', 1);

	},
	// onUnload() {
	// 	const RunKey = uni.getStorageSync('RunKey');
	// 	if (RunKey) {
	// 		this.$refs.PopupOne.open(false);
	// 	}
	// },
	methods: {
		openRobOrder(msg) { // 打开抢单提示弹窗
			console.log(msg)
			this.orderOpObj = msg.obj;
			if (msg.obj.status == 1) {
				//==1 表示抢单，动态修改弹窗内容
				this.config.topBtnText = '确认抢单';
				this.config.title = msg.title;
				this.config.topBtnBgColor = '#ee8131';
				this.config.icon = '../../static/icon/qd.png';
				this.orderShowOpen();
			} else if (msg.obj.status == 2) {
				//==2，表示待配送，需要计算自身当前位置距离商家距离是否符合设定标准
				const end = {
					endLng: this.orderOpObj.startLng,
					endLat: this.orderOpObj.startLat
				};
				this.$common.DistanceCalc(end).then(parms => {
					this.config.icon = '../../static/icon/ddcg.png';
					if (parseFloat(parms) <= this.configInfo.arrivalDistanceLimit) {
						this.config.topBtnText = '确认到店';
						this.config.topBtnBgColor = '#48B36B';
						this.config.title = '确认到店！';
					} else {
						this.config.topBtnText = '强制到店';
						this.config.topBtnBgColor = '#fa4000';
						this.config.title = `系统检测到你离商家${(parms / 1000).toFixed(2)}km,超出取货范围，确认强制到店操作吗？`;
					}
					this.orderShowOpen();
				});
			} else if (msg.obj.status == 3) {
				// ==3 表示待取货
				this.config.topBtnText = '确认取货';
				this.config.topBtnBgColor = '#48B36B';
				this.config.title = msg.title;
				this.config.icon = '../../static/icon/qhcg.png';
				this.orderShowOpen();
			} else if (msg.obj.status == 4) {
				this.config.topBtnText = '确认送达';
				this.config.topBtnBgColor = '#2a75ed';
				this.config.title = msg.title;
				this.config.icon = '../../static/icon/sdwc.png';
				this.orderShowOpen();
			} else if (msg.obj.status == 5) {
				this.config.topBtnText = '确认送达';
				this.config.topBtnBgColor = '#2a75ed';
				this.config.title = msg.title;
				this.config.icon = '../../static/icon/sdwc.png';
				this.orderShowOpen();
			} else if (msg.obj.status == 6) {
				this.config.topBtnText = '确认送达';
				this.config.topBtnBgColor = '#2a75ed';
				this.config.title = msg.title;
				this.config.icon = '../../static/icon/sdwc.png';
				this.orderShowOpen();
			}
		},
		orderShowOpen() { // 打开弹窗
			this.$refs.orderShow.open();
		},
		confirmRobOrder() {
			
		},
		confirmRobOrderTwo() {},
		changePage(e) { // 页面切换
			this.changePageNum = e;
			this.handClickRefresh(e)
		},
		handClickRefresh(e) { // 刷新列表
			const pagenum = 'oreder_' + this.changePageNum;
			this.$refs[pagenum].refresh();
		},
		getBannerList(flag) { // 获取广告信息
			/*this.$request({ url: '/config/advert-list', method: 'GET' }).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.fileList = res.data;
				}
			});*/
			this.fileList = [
				{
					id: 1,
					icon: '../../static/image/logotwo.png'
				}, {
					id: 2,
					icon: '../../static/image/logotwo.png'
				}, {
					id: 3,
					icon: '../../static/image/logotwo.png'
				}
			];
		},
		getNoticeInfo() { // 获取公告信息
			this.$request({ url: '/config/notice', data: { type: 1 }, method: 'GET' }).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.list = [];
					this.noticeInfo = res.data;
					this.noticeInfo.forEach(item => {
						this.list.push(item.title);
					});
				}
			});
		},
		noticeIndex(index) { // 前往公告详情
			uni.setStorage({
				key: 'content',
				data: this.noticeInfo[index],
				success: () => {
					uni.navigateTo({ url: '/pages/agreeRule/notice-info' });
				}
			});
		},
		meetOrder() { // 上下线
			uni.$emit('RefreshUserInfo');
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				this.downShow = !this.downShow;
			} else {
				
				this.$interactive.ShowToast({ title: '请先进行实名认证！' }, false );
			}
		},
		handclick() { // 打开左侧栏
			uni.$emit('RefreshUserInfo');
			this.$refs.popPage.open(true); // 无论通过审核与否都可以弹窗侧栏
			// if (parseInt(this.UserInfo.auditStatus) == 2) {
			// 	this.$refs.popPage.open(true);
			// } else {
			// 	this.showModal = true;
			// }
		},
		handClickoOpen() { // 认证
			uni.$emit('RefreshUserInfo');
			this.$refs.PopupOne.open(true);
		},
		openRiderSet() { //
			// this.$refs.openSet.init();
			uni.reLaunch({ url: '/pages/index/index' });
		},
		openShopSet() { //
			uni.$emit('RefreshUserInfo');
			this.$refs.openSet.init();
		},
		openMineSet() { //
      uni.reLaunch({ url: '/pages/persCenter/personalCenter' });
			// uni.$emit('RefreshUserInfo');
			// this.$refs.popPage.open(true); // 无论通过审核与否都可以弹窗侧栏
			// if (parseInt(this.UserInfo.auditStatus) == 2) {
			// 	this.$refs.popPage.open(true);
			// } else {
			// 	this.showModal = true;
			// }
		},
		verifyResult(msg) { // 滑动验证结果
			console.log(msg);
		},
		UpLine(type, flag) { // 店铺营业/打烊
			uni.$emit('RefreshUserInfo');
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				let isOperating = null;
				switch (type) {
					case 1:
						isOperating = true;
						break;
					case 2:
						isOperating = false;
						break;
				}
				let id = this.UserInfo.shopId;
				this.$request({ url: '/api-merchant/rest/merchant/shop/update', data: { "id": id, "isOperating": isOperating } }).then(res => {
					// #ifdef MP-WEIXIN
					res = JSON.parse(res)
					// #endif
					if (res.code === 200) {
						if (flag) {
							this.downShow = !this.downShow;
						}
						uni.$emit('RefreshUserInfo');
						this.$interactive.ShowToast({ title: type == 1 ? '已成功营业！' : '已成功打烊！' }, false );
					} else {
						this.$interactive.ShowToast({ title: res.message }, false );
					}
				});
			} else {
				this.$interactive.ShowToast({ title: '请先进行实名认证！' }, false );
			}
		},
		tabChange(url) { // 页面切换
			// this.$refs.tabsSwiper.change(tab);
			// uni.$emit('RefreshOrderlist', 1);
			// uni.reLaunch({ url });
      uni.navigateTo({url});
		},
		setDial(tel, type) { // 拨号
			if (type == 1) {
				this.setDialInfo.title = '是否联系用户';
			} else if (type == 2) {
				this.setDialInfo.title = '是否联系骑手';
			}
			this.setDialInfo.tel = tel;
			this.telShow = true;
		}
	}
};
</script>

<style lang="scss" scoped>
	$borderColor: rgba(0, 0, 0, 0.8);

	.content {
		position: relative;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		height: 100vh;
		overflow-x: hidden;
		background-color: $uni-bg-color-grey;

		// 头部导航栏
		.handerTitle {
			position: relative;
			top: 0rpx;
			left: 0rpx;
			background: #ffffff;

			.handerTitle-box {
				.handerTitle-name {
					flex: 1;
					text-align: center;
					font-size: 36rpx;
					font-weight: bold;
					transition: color 0.3s ease;

					.handerTitle-name-icon {
						width: 30rpx;
						height: 30rpx;
						line-height: 30rpx;
						font-size: 22rpx;
						/* #ifdef APP-PLUS */
						font-size: 18rpx;
						/* #endif */
						transition: transform 0.2s ease;
						transform-origin: center center;
						transform: rotate(0deg);
						margin-left: 10rpx;
					}

					.handerTitle-name-icon-rotate {
						transform: rotate(90deg);
					}
				}

				.icon-menu {
					width: 32rpx;
					height: 32rpx;
					text-align: center;
				}

				.handerTitle-name-color {
					color: #2979ff;
				}

				.icon-menu-pic {
					padding: 20rpx 0;
					min-width: 100rpx;
					text-align: center;
				}
			}

			.downMenu {
				position: absolute;
				top: 100rpx;
				left: 50%;
				transform: translateX(-50%);
				background-color: $borderColor;
				width: 200rpx;
				z-index: 4;
				border-radius: 10rpx;
				transition: all 0.3s ease;
				padding-bottom: 20rpx;

				&::before {
					content: '';
					display: block;
					border-color: $borderColor transparent transparent $borderColor;
					border-style: solid;
					border-width: 10rpx;
					transform: translateY(-10rpx) rotate(45deg);
					margin: 0 auto;
					width: 0rpx;
					height: 0rpx;
				}

				.downMenu-ls {
					color: #fff;
					padding: 10rpx 32rpx;
					font-size: 30rpx;
					text-align: center;

					.downMenu-ls-status {
						width: 18rpx;
						height: 18rpx;
						border-radius: 50%;
						background-color: $font-my-color-c;
					}

					.downMenu-ls-name {
						flex: 1;
						text-align: center;
					}
				}
			}
		}

		// 轮播图
		/deep/.swiper {
			height: 180rpx;
			background: #ffffff;

			.uni-swiper-dot {
				width: 10rpx !important;
				height: 10rpx !important;
				background-color: rgba(0, 0, 0, 0.1);
			}
		}

		.content-box {
			position: relative;
			flex: 1;
			display: flex;
			flex-direction: column;
		}

		.tips-info {
			min-height: 50rpx;
			z-index: 1;
			padding: 16rpx 32rpx;
			border-radius: 28rpx;
			margin: 20rpx 20rpx 0rpx;
			background: #ffffff;
			box-shadow: 1rpx 1rpx 10rpx 0rpx rgba(0, 0, 0, 0.1);

			/deep/.u-notice-bar {
				padding: 0rpx !important;

				.u-icon__icon {
					color: $uni-color-primary !important;
				}
			}

			/deep/.u-type-warning-light-bg {
				background-color: transparent !important;
			}

			/deep/.u-news-item {
				width: 100%;
			}

			.tips-info-bg {
				.tips-info-bg-text {
					/deep/span {
						background: linear-gradient(45deg, #fa7c25, #ee2e21);
						-webkit-background-clip: text !important;
						color: transparent;
					}
				}
			}

			.tips-info-p {
				margin-left: 20rpx;
				font-size: 24rpx;
				color: #333;
				margin-bottom: 8rpx;
			}
		}

		.bot-btn {
			position: absolute;
			right: 20rpx;
			bottom: 320rpx;
			background: linear-gradient(15deg, #fa7c25, #ee2e21);
			font-size: 24rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			border-radius: 20rpx;
			border: 6rpx solid #fff;
			box-shadow: 2rpx 2rpx 10rpx 0 rgba(0, 0, 0, 0.3);

			&::after {
				border: none;
				outline: none;
			}
		}

		.slideBox-title {
			font-size: 36rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #000000;
			text-align: center;
			padding: 24rpx 0;

			&::after {
				display: block;
				content: '';
				background: #eee;
				height: 1rpx;
				margin-top: 24rpx;
			}
		}

		.slideBox-cont {
			font-size: 42rpx;
			font-family: Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			padding: 40rpx 50rpx 20rpx;
		}

		.slideBox-info {
			font-size: 38rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #666666;
			padding: 0rpx 50rpx 50rpx;
		}

		// 滑动认证
		/deep/.pathway {
			width: 650rpx;
			margin: 0 auto;
			height: 100rpx;
			border-radius: 100rpx;

			.tips {
				line-height: 100rpx;
			}

			.track {
				border-radius: 10000rpx;
			}

			.on-track {
				border-radius: 50%;
			}
		}

		.footerBtn {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 120rpx;
			font-size: 30rpx;
			line-height: 120rpx;
			letter-spacing: 2rpx;
			text-align: center;
			background: linear-gradient(45deg, #fa7c25, #ee2e21);
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #fff;
		}

		.kong {
			height: 150rpx;
		}

		.footerBtn-t {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 750rpx;
			height: 120rpx;
			background-color: #ffffff;
			border-top: 1rpx solid #f1f1f1;

			.footerBtn-t-box {
				min-width: 190rpx;
				padding-top: 11px;
				padding-bottom: 11px;
				
				.icon-t {
					min-height: 44rpx;
					max-height: 44rpx;
				}

				.r-text {
					color: $font-my-color-6;
					font-size: 24rpx;
				}
			}

			.footerBtn-t-cent {
				flex: 1;
				color: #ee8131;
				font-weight: bold;
				text-align: center;
				border-radius: 40rpx;
				overflow: hidden;
				border: 1rpx solid rgb(238, 129, 49);
				padding: 16rpx 0rpx;

				.c-text {
					margin-left: 10rpx;
					font-size: $font-my-size-32;
				}
			}
		}

		/deep/.u-model__title {
			font-weight: bold;
		}
	}
</style>
