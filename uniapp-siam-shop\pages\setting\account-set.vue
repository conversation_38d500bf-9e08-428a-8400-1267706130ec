<template>
	<view class="account-set">
		<view class="setting-cont">
			<view class="setting-cont-list line flex flex-align-center" @click="handclick(userInfo.tel, 3, 1)">
				<view class="setting-cont-list-title flex flex-direction-column flex-align-start"><text>修改手机号</text></view>
				<text class="setting-cont-list-val">{{ userInfo.tel }}</text>
				<view class="setting-cont-list-icon"><uni-icons type="forward"></uni-icons></view>
			</view>
			<view class="setting-cont-list line flex flex-align-center justify-space-between" @click="handclick(userInfo.tel, 2, 2,'set')">
				<view class="setting-cont-list-title flex flex-direction-column flex-align-start"><text>修改密码</text></view>
				<view class="setting-cont-list-icon"><uni-icons type="forward"></uni-icons></view>
			</view>
		</view>
		<!-- <view class="setting-cont">
			<view class="setting-cont-list line flex flex-align-center justify-space-between">
				<view class="setting-cont-list-title flex flex-direction-column flex-align-start"><text>注销账号</text></view>
				<view class="setting-cont-list-icon"><uni-icons type="forward"></uni-icons></view>
			</view>
		</view> -->
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {}
		};
	},
	onLoad() {
		this.getUserInfo();
	},
	methods: {
		getUserInfo() {
			this.$request({
				url: '/login/rider-info',
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.userInfo = res.data;
				} else {
					this.$interactive.ShowToast({
						title: res.message
					});
				}
			});
		},
		handclick(msg, type, PageType,callback) {
			this.$request({
				url: '/login/get-code',
				data: { tel: msg, type: type }
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					uni.navigateTo({
						url: '/pages/login/register-code?tel=' + msg + '&type=' + type + '&PageType=' + PageType + '&callback=' + callback
					});
				} else {
					this.$interactive.ShowToast(
						{
							title: res.message
						},
						false
					);
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.account-set {
	background-color: $uni-bg-color-grey;
	height: 100vh;

	.setting-cont {
		padding-top: 20rpx;

		.setting-cont-list {
			background-color: $uni-bg-color;
			padding: 30rpx 32rpx 31rpx;

			.setting-cont-list-title {
				font-size: $font-my-size-32;
				color: $font-my-color-3;
				font-weight: 400;

				.title-label {
					font-size: $font-my-size-24;
				}
			}

			.setting-cont-list-val {
				flex: 1;
				text-align: right;
			}

			.setting-cont-list-icon {
				width: 50rpx;
				text-align: right;

				/deep/.uni-switch-input-checked {
					background: #f94b08 !important;
					border-color: #f94b08 !important;
				}
			}
		}

		.line {
			position: relative;
			top: 0rpx;
			left: 0rpx;

			&::after {
				position: absolute;
				left: 32rpx;
				right: 32rpx;
				bottom: 0rpx;
				height: 1rpx;
				background: $uni-bg-color-grey;
				content: '';
			}
		}
	}
}
</style>
