<template>
	<view class="agreeRule">
		<view class="agree-cont">
			<view class="agree-cont-title">
				<my-icon size="30">&#xe71e;</my-icon>
				<text class="agree-cont-title-text">协议</text>
			</view>
			<template v-for="(item, index) of agreeRule[1]">
				<view class="agree-cont-info" :key="item.id" @click="handTransfer(item, 1)">
					<u-section :title="item.title" sub-title=" " :bold="false" :show-line="false"></u-section>
				</view>
			</template>
		</view>
		<view class="agree-cont">
			<view class="agree-cont-title" v-if="agreeRule[2].length">
				<my-icon size="30">&#xe7ac;</my-icon>
				<text class="agree-cont-title-text">规则说明</text>
			</view>
			<template v-for="(item, index) of agreeRule[2]">
				<view class="agree-cont-info" :key="item.id" @click="handTransfer(item, 2)">
					<u-section :title="item.title" sub-title=" " :bold="false" :show-line="false"></u-section>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			agreeRule: {
				1: [],
				2: []
			},
			type: 2
		};
	},
	onLoad() {
		this.getAgreeRule(2);
		this.getAgreeRule(3);
	},
	methods: {
		getAgreeRule(msg) {
			this.$request({
				url: '/config/notice',
				method: 'GET',
				data: {
					type: msg
				}
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.agreeRule[msg - 1] = res.data;
				} else {
					this.$interactive.ShowToast(
						{
							title: res.message
						},
						false
					);
				}
			});
		},
		handTransfer(msg, type) {
			uni.setStorage({
				key: 'content',
				data: msg,
				success: () => {
					uni.navigateTo({
						url: '/pages/agreeRule/notice-info?type=' + type
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.agreeRule {
	padding: 0rpx 0 0;

	.agree-cont {
		padding: 0 40rpx;

		.agree-cont-title {
			padding: 60rpx 0 30rpx;
			font-size: 36rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: $font-my-color-3;

			.agree-cont-title-text {
				margin-left: 15rpx;
			}
		}

		.agree-cont-info {
			background: $uni-bg-color-grey;
			padding: 30rpx 32rpx;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
		}
	}
}
</style>
