<template>
	<view class="studyTrain">
		<custom-tab-swiper :list="list" :pageLength="2" active-color="#ee8131">
			<template slot="swiper-item-1">
				<study-train-one></study-train-one>
			</template>
			<template slot="swiper-item-2">
				<study-train-two></study-train-two>
			</template>
		</custom-tab-swiper>
		<u-popup v-model="show" mode="bottom" height="40%" border-radius="20">
			<view class="studyTrain-title">填写个人信息</view>
			<view class="phone flex flex-align-center">
				<text class="name-text">地址ID</text>
				<input class="phone-input" type="text" v-model="formData.addressId" disabled placeholder="请选择地址ID"
					placeholder-class="placeholderStyl" />
			</view>
			<view class="phone flex flex-align-center">
				<text class="name-text">预约时间</text>
				<input class="phone-input" type="text" maxlength="11" v-model="formData.time" placeholder="请选择预约时间"
					placeholder-class="placeholderStyl" />
			</view>
			<view class="phone flex flex-align-center">
				<text class="name-text">预约时间段</text>
				<input class="phone-input" type="text" v-model="formData.timeSlot" placeholder="请选择预约时间"
					placeholder-class="placeholderStyl" />
			</view>
		</u-popup>
	</view>
</template>

<script>
	import CustomTabSwiper from '@/components/custom-tabs-page-linkge/tab-page-linkage.vue';
	import StudyTrainOne from './componnts/studyTrainOne.vue';
	import StudyTrainTwo from './componnts/studyTrainTwo.vue';

	export default {
		components: {
			CustomTabSwiper,
			StudyTrainOne,
			StudyTrainTwo
		},
		data() {
			return {
				show: false,
				list: [{
						name: '培训点报名'
					},
					{
						name: '我的报名'
					}
				],
				formData: {
					addressId: '',
					time: '',
					timeSlot: ''
				},
				current: 0,
				swiperCurrent: 0
			};
		},
		onUnload() {
			this.$off('cancelEnrol');
			this.$off('cancelEnrolOne');
		},
		methods: {}
	};
</script>

<style lang="scss" scoped>
	.studyTrain {
		display: flex;
		flex-direction: column;
		height: 100%;
		//#ifndef H5
		height: 100vh;
		//#endif
		overflow-y: hidden;

		/deep/.studyTrain-footer {
			position: relative;
			border: 1rpx solid #dddddd;
			border-radius: 10rpx;
			padding: 40rpx 32rpx;
			margin-bottom: 32rpx;

			.studyTrain-footer-title {
				font-size: 28rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;

				.tetle-type {
					color: #de4644;
				}
			}

			.tetle-name {
				.tetle-name-text {
					font-weight: bold;
					margin-right: 20rpx;
				}

			}

			.studyTrain-footer-btn {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}

		.studyTrain-title {
			font-size: $font-my-size-32;
			color: $font-my-color-3;
			font-weight: bold;
			padding: 20rpx 32rpx;
		}

		.phone {
			margin: 0 52rpx;
			border-bottom: 1rpx solid $uni-bg-color-grey;

			.name-text {
				font-size: $font-my-size-32;
				color: $font-my-color-3;
				margin-right: 20rpx;
				min-width: 160rpx;
			}

			.phone-input {
				padding: 32rpx 0;
				flex: 1;

				.placeholderStyl {
					font-size: $font-my-size-32;
				}
			}

			.phone-input-code {
				padding: 32rpx 0;
			}
		}
	}
</style>
