<template>
  <view class="perfectInfo">
    <uni-status-bar></uni-status-bar>
    <u-form :model="formData" ref="uForm" :error-type="['message']" :label-width="150">
      <!-- 状态选择器 -->
      <u-select v-model="statusSelectShow" mode="single-column" :list="statusSelectList" @confirm="onStatusConfirm"></u-select>
      <!-- 打印机选择器 -->
      <u-select v-model="printerSelectShow" mode="single-column" :list="printerSelectList" @confirm="onPrinterConfirm"></u-select>
      <!-- 分类多选组件 -->
      <category-multi-select 
        :show="categorySelectShow" 
        :category-list="categoryList" 
        :value="formData.menuIds"
        @confirm="onCategoryConfirm"
        @close="categorySelectShow = false"
      />
      <view class="phone-box">
        <!-- 商品图片 -->
        <u-form-item label="商品图片" prop="subImages" :required="true">
          <view class="image-upload-container">
            
            <u-image v-for="item in formData.subImages" class="addHeadPic"  width="120rpx" height="120rpx"  :src="getImageUrl(item)">
            </u-image>
            <view class="addHeadPic" @click="chooseImage">
              <uni-icons class="addHeadPic-icon" type="plusempty"></uni-icons>
            </view>
          </view>
        </u-form-item>

        <!-- 商品名称 -->
        <u-form-item label="商品名称" prop="name" :required="true">
          <uni-easyinput class="phone-input" :inputBorder="false" v-model="formData.name"
            placeholder="请输入商品名称"></uni-easyinput>
        </u-form-item>

        <!-- 商品类别 -->
        <u-form-item label="商品类别" prop="menuIds" :required="true">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showCategorySelect">
            <text>{{ getCategoryText() || '请选择商品类别' }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item>

        <!-- 商品状态 -->
        <u-form-item label="商品状态" prop="status" :required="true">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showStatusSelect">
            <text>{{ getStatusText(formData.status) }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item>

        <u-form-item label="商品数量" prop="stock" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.stock" placeholder="请输入商品数量"></uni-easyinput>
        </u-form-item>

        <!-- 商品价格 -->
        <u-form-item label="商品价格" prop="price" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.price"
            placeholder="请输入商品价格"></uni-easyinput>
        </u-form-item>

        <!-- 包装费 -->
        <!-- <u-form-item label="包装费" prop="packingCharges" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.packingCharges"
            placeholder="请输入包装费"></uni-easyinput>
        </u-form-item> -->

        <!-- 商品描述 -->
        <u-form-item label="商品描述" prop="detail">
          <uni-easyinput class="phone-input" :inputBorder="false" v-model="formData.detail" placeholder="请输入商品描述"
            maxlength="200"></uni-easyinput>
        </u-form-item>

        <!-- 后厨打印机 -->
        <!-- <u-form-item label="后厨打印机" prop="printerId">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showPrinterSelect">
            <text>{{ getPrinterText() }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item> -->
      </view>
    </u-form>

    <my-button margin-top="60" :bold="true" color="#fff" font-size="32" @confirm="saveGoods">保存</my-button>
    
    <!-- 删除按钮 -->
    <my-button v-if="isEdit" margin-top="30" :bold="true" color="rgb(236, 22, 19)" borderColor="rgb(236, 22, 19)" :border="true" background="#fa353400" font-size="32" @confirm="deleteGoods">删除商品</my-button>
  </view>
</template>

<script>
import CategoryMultiSelect from './components/category-multi-select.vue';

export default {
  components: {
    CategoryMultiSelect
  },
  data () {
    return {
      isEdit: false,
      goodsId: null,
      formData: {
        name: '',
        price: '',
        menuNames: '',
        menuIds: [], // 修改为数组
        subImages: [],
        status: 0,
        printerId: '',
        stock: ''
      },
      // u-select相关数据
      categorySelectShow: false,
      statusSelectShow: false,
      printerSelectShow: false,
      categorySelectList: [],
      statusSelectList: [],
      printerSelectList: [],
      categoryList: [],
      categoryIndex: 0,
      statusList: [
        { name: '待上架', value: 1 },
        { name: '已上架', value: 2 },
        { name: '已下架', value: 3 },
        { name: '售罄', value: 4 }
      ],
      statusIndex: 0,
      printerList: [],
      selectedPrinters: [],
      // 表单验证规则
      rules: {
        subImages: [
          {
            required: true,
            message: '请上传商品图片',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              // 检查数组是否存在且长度大于0
              return Array.isArray(value) && value.length > 0;
            }
          }
        ],
        name: [
          {
            required: true,
            message: '请输入商品名称',
            trigger: ['change', 'blur'],
          },
          {
            min: 1,
            max: 50,
            message: '商品名称长度在1到50个字符',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return !!value;
            }
          }
        ],
        menuIds: [
          {
            required: true,
            message: '请选择商品类别',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return Array.isArray(value) && value.length > 0;
            }
          }
        ],
        status: [
          {
            required: true,
            message: '请选择商品状态',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return !!value;
            }
          }
        ],
        price: [
          {
            required: true,
            message: '请输入商品价格',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return !!value;
            }
          },
          {
            type: 'number',
            message: '商品价格必须为数字',
            trigger: ['change', 'blur'],
            transform(value) {
              return Number(value);
            }
          },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                return false;
              }
              return true;
            },
            message: '商品价格必须大于0',
            trigger: ['change', 'blur']
          }
        ],
        // packingCharges: [
        //   {
        //     required: true,
        //     message: '请输入包装费',
        //     trigger: ['change', 'blur'],
        //     validator: (rule, value, callback) => {
        //       return !!value;
        //     }
        //   },
        //   {
        //     type: 'number',
        //     message: '包装费必须为数字',
        //     trigger: ['change', 'blur'],
        //     transform(value) {
        //       return value ? Number(value) : 0;
        //     }
        //   }
        // ],
        stock: [
          {
            type: 'number',
            message: '商品数量必须为数字',
            trigger: ['change', 'blur'],
            transform(value) {
              return value ? Number(value) : 0;
            }
          }
        ]
      }
    }
  },
  onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
  onLoad (options) {
    if (options.id) {
      this.isEdit = true;
      this.goodsId = options.id;
      this.loadGoodsDetail();
    }
    // 准备状态选择器数据
    this.statusSelectList = this.statusList.map(item => ({
      label: item.name,
      value: item.value
    }));
    this.loadCategoryList();
    this.loadPrinterList();
  },
  methods: {
    // u-select显示方法
    showCategorySelect() {
      this.categorySelectShow = true;
    },
    showStatusSelect() {
      // 准备状态选择器数据
      this.statusSelectList = this.statusList.map(item => ({
        label: item.name,
        value: item.value
      }));
      this.statusSelectShow = true;
    },
    showPrinterSelect() {
      // 准备打印机选择器数据
      this.printerSelectList = this.printerList.map(item => ({
        label: item.name,
        value: item.id
      }));
      this.printerSelectShow = true;
    },
    
    // u-select确认方法
    onCategoryConfirm(selectedIds) {
      // 根据选中的ID获取对应的名称
      const selectedCategories = this.categoryList.filter(item => selectedIds.includes(item.id));
      this.formData.menuIds = selectedIds;
      this.formData.menuNames = selectedCategories.map(item => item.name).join(',');
      this.categorySelectShow = false;
    },
    
    onStatusConfirm(e) {
      const item = e[0];
      if (item) {
        this.formData.status = item.value;
      }
      this.statusSelectShow = false;
    },
    
    onPrinterConfirm(e) {
      const item = e[0];
      if (item) {
        this.formData.printerId = item.value;
      }
      this.printerSelectShow = false;
    },
    
    // 获取完整图片URL
    getImageUrl (imageUrl) {
      if (!imageUrl) {
        return '/static/icon/empty.png';
      }
      return 'https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/' + imageUrl;
    },

    // 获取状态文本
    getStatusText (status) {
      const statusMap = {
        1: '待上架',
        2: '已上架',
        3: '已下架',
        4: '售罄'
      };
      return statusMap[status] || '请选择';
    },

    // 获取分类文本
    getCategoryText () {
      if (!this.formData.menuIds || this.formData.menuIds.length === 0) {
        return '';
      }
      // 通过接口返回的分类列表数据解析选中分类的名称
      const selectedCategories = this.categoryList.filter(item => 
        this.formData.menuIds.includes(item.id.toString())
      );
      console.log('==========', selectedCategories, this.formData.menuIds, this.categoryList);
      return selectedCategories.map(item => item.name).join(',');
    },

    // 加载商品详情
    async loadGoodsDetail () {
      try {
        const res = await this.$request({
          url: `/api-goods/rest/merchant/goods/getById`,
          method: 'POST',
          data: {
            id: this.goodsId
          }
        });
        if (res.code === 200) {
          this.formData = { ...res.data };
          this.formData.subImages = res.data.subImages.split(',');
          
          // 处理分类数据
          if (res.data.menuId) {
            // 兼容旧数据，单个分类ID转为数组
            this.formData.menuIds = [res.data.menuId];
            this.formData.menuNames = res.data.menuName;
          } else if (res.data.menuIds) {
            // 新数据格式，已经是数组
            this.formData.menuIds = res.data.menuIds;
            this.formData.menuNames = res.data.menuName;
          } else {
            this.formData.menuIds = [];
            this.formData.menuNames = '';
          }
          
          // 准备状态选择器数据
          this.statusSelectList = this.statusList.map(item => ({
            label: item.name,
            value: item.value
          }));
          
          // 设置打印机选择
          this.setPrinterSelection();
        }
      } catch (error) {
        console.error('加载商品详情失败:', error);
        uni.showToast({ title: '加载商品详情失败', icon: 'none' });
      }
    },

    // 加载分类列表
    async loadCategoryList () {
      try {
        const res = await this.$request({
          url: '/api-goods/rest/merchant/menu/list',
          method: 'POST',
          data: {
            pageNo:-1,
            pageSize:10,
            typestatus:0
          }
        });
        if (res.code === 200) {
          this.categoryList = res.data.records || [];
        }
      } catch (error) {
        console.error('加载分类列表失败:', error);
      }
    },

    // 选择图片
    chooseImage () {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadImage(res.tempFilePaths);
        }
      });
    },

    // 上传图片
    async uploadImage (filePath) {
      uni.showLoading({ title: '上传中...' });
      try {
        this.$common.UpLoadFile(filePath).then(uploadRes => {
							console.log(uploadRes)
							if (uploadRes && uploadRes.length >= 1) {
								this.formData.subImages.push(uploadRes[0]);
							} else {
								this.$interactive.ShowToast({ title: '上传失败' }, false );
							}
						})
 
      } finally {
        uni.hideLoading();
      }
    },



    // 这些方法已被u-select的确认方法替代

    // 加载打印机列表
    async loadPrinterList () {
      try {
        const res = await this.$request({
          url: '/api-util/rest/merchant/printer/list',
          method: 'POST',
          data: {}
        });
        if (res.code === 200) {
           this.printerList = res.data.records || [];
           // 准备打印机选择器数据
           this.printerSelectList = this.printerList.map(item => ({
             label: item.name,
             value: item.id
           }));
           // 如果是编辑模式，设置打印机选择
           if (this.isEdit) {
             this.setPrinterSelection();
           }
         }
      } catch (error) {
        console.error('加载打印机列表失败:', error);
      }
    },

    // 这些方法已被u-select的确认方法替代

    // 获取打印机显示文本
    getPrinterText() {
      if (!this.formData.printerId) {
        return '请选择打印机';
      }
      console.log(this.formData?.printerId)
      //debugger
      return this.printerList.find(item => this.formData?.printerId == item.id)?.name;
    },

     // 设置打印机选择（编辑模式回显）
     setPrinterSelection () {
       
       if (this.formData.printerId.length > 0 && this.printerList.length > 0) {
         // 准备打印机选择器数据
         this.printerSelectList = this.printerList.map(item => ({
           label: item.name,
           value: item.id
         }));
       }
     },

    // 保存商品
    async saveGoods () {
      // 使用u-form进行表单验证
      try {
        const valid = await this.$refs.uForm.validate();
        if (!valid) {
          return;
        }
        
      } catch (error) {
        console.error('表单验证失败:', error);
        return;
      }

      uni.showLoading({ title: '保存中...' });
      try {
        const url = this.isEdit
          ? `/api-goods/rest/merchant/goods/update`
          : '/api-goods/rest/merchant/goods/insert';

        const res = await this.$request({
          url,
          method: 'POST',
          data: {
            ...this.formData,
            menuId: null,
            subImages: this.formData.subImages.join(),
            // menuId: this.formData.menuIds.length > 0 ? this.formData.menuIds[0] : null, // 兼容旧字段
            menuName: this.formData.menuNames // 兼容旧字段
          }
        });

        if (res.code === 200) {
          uni.showToast({ title: '保存成功', icon: 'success' });
          // 设置全局标记，用于返回列表页时刷新数据
          uni.setStorageSync('goodsListNeedRefresh', true);
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({ title: res.message || '保存失败', icon: 'none' });
        }
      } catch (error) {
        console.error('保存商品失败:', error);
        uni.showToast({ title: '保存失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 删除商品
    deleteGoods() {
      uni.showModal({
        title: '提示',
        content: '确定要删除该商品吗？',
        success: (res) => {
          if (res.confirm) {
            this.performDelete();
          }
        }
      });
    },
    
    // 执行删除操作
    async performDelete() {
      uni.showLoading({ title: '删除中...' });
      try {
        const res = await this.$request({
          url: "/api-goods/rest/merchant/goods/delete",
          method: "POST",
          data: { id: this.goodsId }
        });
        
        if (res.code === 200) {
          uni.showToast({
            title: "删除成功",
            icon: "success"
          });
          
          // 设置全局标记，用于返回列表页时刷新数据
          uni.setStorageSync('goodsListNeedRefresh', true);
          
          // 删除成功后返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.message || "删除失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error('删除商品失败:', error);
        uni.showToast({
          title: "删除失败",
          icon: "none"
        });
      } finally {
        uni.hideLoading();
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.perfectInfo {
  padding: 0 32rpx;

  .phone-box {
    padding-top: 20rpx;
  }

  // u-form-item 样式调整
  /deep/ .u-form-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid $uni-bg-color-grey;
    
    .u-form-item__body {
      display: flex;
      align-items: center;
    }
    
    .u-form-item__body__left__content__label {
      font-size: $font-my-size-32;
      color: $font-my-color-3;
      min-width: 150rpx;
    }
  }

  .phone-input {
    padding: 0rpx 0;
    flex: 1;
  }

  .image-upload-container {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .addHeadPic {
    position: relative;
   border: 1rpx solid #ddd;
    width: 120rpx;
    height: 120rpx;
    border-radius: 6rpx;
    text-align: center;
    margin-right: 10rpx;
    .addHeadPic-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>