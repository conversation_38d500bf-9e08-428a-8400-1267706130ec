<template>
	<view class="orderStatis">
		<view class="today-title">订单统计</view>
		<u-cell-group :border="false">
			<view class="orderStatis-cont-tab">
				<u-tabs-swiper ref="tabSwiper" :list="list" :is-scroll="false" :duration="300" active-color="#FF6320" :current="tabNum" @change="handClickChange"></u-tabs-swiper>
			</view>
		</u-cell-group>
		<swiper class="swiper-box" :duration="300" :current="swiperNum" @transition="transition" @animationfinish="animationfinish">
			<swiper-item><to-day></to-day></swiper-item>
			<swiper-item><month-day @openTime="openTime"></month-day></swiper-item>
		</swiper>
		<u-calendar v-model="show" @change="changeTime" :safe-area-inset-bottom="true"></u-calendar>
	</view>
</template>

<script>
import ToDay from './components/today.vue';
import MonthDay from './components/month.vue';

export default {
	components: {
		MonthDay,
		ToDay
	},
	data() {
		return {
			show: false,
			tabNum: 0,
			checkValue: '',
			swiperNum: 0,
			time: '2020年10月',
			list: [
				{
					name: '今日订单'
				},
				{
					name: '月订单量'
				}
			]
		};
	},
	methods: {
		handClickChange(msg) {
			this.swiperNum = msg;
		},
		transition(res) {
			this.$refs.tabSwiper.setDx(res.detail.dx);
		},
		animationfinish(msg) {
			this.tabNum = msg.detail.current;
			this.swiperNum = msg.detail.current;
		},
		openTime(msg) {
			this.show = msg;
		},
		changeTime(msg) {
			this.time = `${msg.year}年${msg.month}月`;
		}
	}
};
</script>

<style lang="scss" scoped>
.orderStatis {
	display: flex;
	flex-direction: column;
	background: $uni-bg-color-grey;
	height: 100%;
	overflow: hidden;
	
	.today-title {
		font-size: $font-my-size-36;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		padding: 20rpx 32rpx;
		background-color: #FFFFFF;
	}

	.orderStatis-title {
		background: #ffffff;
		padding: 20rpx 32rpx 0rpx;
		font-size: $font-my-size-36;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
	}

	.orderStatis-cont-tab {
		width: 400rpx;
	}

	.swiper-box {
		flex: 1;
		margin-top: 20rpx;
	}
}
</style>
