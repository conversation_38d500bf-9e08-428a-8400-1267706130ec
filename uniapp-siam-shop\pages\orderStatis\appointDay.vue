<template>
	<view class="appointDay">
		<u-navbar title-size="36" :border-bottom="false" title="订单统计"></u-navbar>
		<view class="appointDay-time">{{ time }}</view>
		<view class="today-cont">
			<view class="flex flex-align-center">
				<view class="today-cont-box">
					<view class="today-cont-money">
						<text class="money-num">100</text>
						<text>单</text>
					</view>
					<view class="today-cont-money">完成订单</view>
				</view>
				<view class="today-cont-box">
					<view class="today-cont-money">
						<text class="money-num">600</text>
						<text>元</text>
					</view>
					<view class="today-cont-money">收入金额</view>
				</view>
				<view class="today-cont-box">
					<view class="today-cont-money">
						<text class="money-num">3</text>
						<text>单</text>
					</view>
					<view class="today-cont-money">取消订单</view>
				</view>
			</view>
			<view class="today-cont-footer-text">收入相关信息请前往 “我的账户”查看</view>
		</view>
		<view class="today-title">订单明细</view>
		<scroll-view scroll-y="true" style="flex: 1; height: 0rpx; padding-bottom: 40rpx;">
			<template v-for="(item, index) of dataList">
				<view class="order-list-box" :key="index">
					<view class="order-list flex flex-align-center justify-space-between">
						<view class="list-title">同城急送小程序 0369</view>
						<u-icon name="arrow-down" color="#999" label="￥25" label-size="32" label-color="#333" size="28" label-pos="left"></u-icon>
					</view>
					<view class="mapPosition flex flex-align-center justify-space-between">
						<u-icon name="map-fill" label="奎克科技大厦A座 西门" label-size="28" label-color="#999" color="#00BF7F" size="28"></u-icon>
						<text>20:36 完成</text>
					</view>
				</view>
			</template>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			dataList: new Array(10),
			list: new Array(10),
			time: ''
		};
	},
	onLoad(e) {
		this.time = e.time;
	}
};
</script>

<style lang="scss" scoped>
.appointDay {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	height: 100vh;
	background: #ffffff;

	.appointDay-time {
		background: $uni-bg-color-grey;
		padding: 30rpx 34rpx;
		font-size: 26rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		min-height: 94rpx;
	}

	.today-title {
		font-size: 34rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		padding: 50rpx 32rpx 32rpx;
	}

	.today-cont {
		padding: 40rpx 40rpx 0rpx;
		background: #ff6320;
		border-radius: 10px;
		margin: 32rpx 32rpx 0rpx;

		.today-cont-box {
			flex: 1;
			text-align: center;

			.today-cont-money {
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;

				.money-num {
					font-size: 52rpx;
					color: #ffffff;
				}
			}
		}

		.today-cont-footer-text {
			margin-top: 20rpx;
			padding: 20rpx 0rpx;
			text-align: center;
			font-size: 20rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			opacity: 0.5;
			border-top: 1rpx solid rgba(255, 255, 255, 0.7);
		}
	}

	.order-list-box {
		padding: 20rpx 0;

		&::after {
			content: '';
			display: block;
			height: 1rpx;
			margin: 40rpx 32rpx 0;
			background: #eee;
		}

		&:last-child {
			&::after {
				content: '';
				display: none;
			}
		}

		.order-list {
			padding: 0rpx 32rpx 20rpx;

			.list-title {
				color: #333;
				font-weight: bold;
				font-size: 30rpx;
			}
		}

		.mapPosition {
			padding: 0 32rpx;
			font-size: 26rpx;
			color: #999;
		}
	}
}
</style>
