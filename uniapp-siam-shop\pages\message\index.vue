<template>
	<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false }" :fixed="false">
		<view class="message-list">
			<u-swipe-action v-for="(item, index) in messageList" :key="item.id" 
				:show="item.show" 
				:index="index" 
				@click="deleteMessage(index)"
				@open="openSwipe(index)"
				:options="swipeOptions">
				<view class="message-item" @click="goToChat(item)">
					<view class="avatar">
						<u-avatar :src="item.avatar" size="large"></u-avatar>
						<u-badge v-if="item.unreadCount > 0" :count="item.unreadCount" absolute :offset="[0, 0]"></u-badge>
					</view>
					<view class="message-content">
						<view class="user-info">
							<text class="username">{{ item.username }}</text>
						</view>
						<view class="last-message">
							<text class="message-text">{{ item.lastMessage }}</text>
						</view>
					</view>
					<view class="message-meta">
						<text class="time">{{ item.time }}</text>
						<!-- <u-badge v-if="item.unreadCount > 0" :count="item.unreadCount" type="error"></u-badge> -->
					</view>
				</view>
			</u-swipe-action>
			
			<u-empty v-if="messageList.length === 0" text="暂无消息" mode="message"></u-empty>
		</view>
	</mescroll-uni>
</template>

<script>
	export default {
		data() {
			return {
				messageList: [
					// 示例数据
					// {
					// 	id: 1,
					// 	username: '张三',
					// 	avatar: 'https://example.com/avatar1.jpg',
					// 	lastMessage: '你好，请问还在营业吗？',
					// 	time: '15:30',
					// 	unreadCount: 2,
					// 	show: false
					// }
				],
				swipeOptions: [
					{
						text: '删除',
						style: {
							backgroundColor: '#dd524d'
						}
					}
				]
			}
		},
		onLoad() {
			// this.loadMessageList();
		},
		methods: {
			// mescroll初始化完成的回调
			// 初始化mescroll对象
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			
			// 下拉刷新回调
			downCallback() {
				this.loadMessageList(1).then(() => {
					this.mescroll.endSuccess();
				}).catch(() => {
					this.mescroll.endErr();
				});
			},
			
			// 上拉加载回调
			upCallback(page) {
				this.loadMessageList(page.num).then((hasNext) => {
					this.mescroll.endSuccess(hasNext);
				}).catch(() => {
					this.mescroll.endErr();
				});
			},
			
			// 加载消息列表数据
			loadMessageList(pageNum = 1) {
				return new Promise((resolve, reject) => {
					// 模拟异步请求数据
					setTimeout(() => {
						try {
							// 这里应该从服务器获取数据
							// 示例数据
							const newData = [
								{
									id: 1 + (pageNum-1)*3,
									username: '张三',
									avatar: '/static/images/avatar1.jpg',
									lastMessage: '你好，请问还在营业吗？',
									time: '15:30',
									unreadCount: 2,
									show: false
								},
								{
									id: 2 + (pageNum-1)*3,
									username: '李四',
									avatar: '/static/images/avatar2.jpg',
									lastMessage: '我的订单大概多久能送到？',
									time: '14:20',
									unreadCount: 0,
									show: false
								},
								{
									id: 3 + (pageNum-1)*3,
									username: '王五',
									avatar: '/static/images/avatar3.jpg',
									lastMessage: '谢谢，食物很美味！',
									time: '昨天',
									unreadCount: 0,
									show: false
								}
							];
							
							if (pageNum === 1) {
								this.messageList = newData;
							} else {
								this.messageList = this.messageList.concat(newData);
							}
							
							// 判断是否还有下一页数据
							const hasNext = pageNum < 3; // 假设有3页数据
							resolve(hasNext);
						} catch (error) {
							reject(error);
						}
					}, 1000);
				});
			},
			
			// 打开滑动菜单
			openSwipe(index) {
				this.messageList.forEach((item, i) => {
					if (index === i) {
						item.show = true;
					} else {
						item.show = false;
					}
				});
			},
			
			// 删除消息
			deleteMessage(index) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该聊天记录吗？',
					success: (res) => {
						if (res.confirm) {
							this.messageList.splice(index, 1);
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 跳转到聊天页面
			goToChat(item) {
				// 跳转到聊天页面，传递用户信息
				uni.navigateTo({
					url: `/pages/message/chat?userId=${item.id}&username=${item.username}`
				});
				
				// 清除未读标记
				item.unreadCount = 0;
			}
		}
	}
</script>

<style scoped>
.message-list {
	padding: 20rpx 0;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.message-item {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
}

.avatar {
	position: relative;
	margin-right: 20rpx;
}

.message-content {
	flex: 1;
	overflow: hidden;
}

.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.username {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.last-message {
}

.message-text {
	font-size: 28rpx;
	color: #999999;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.message-meta {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	justify-content: space-between;
	height: 80rpx;
}

.time {
	font-size: 24rpx;
	color: #cccccc;
	margin-bottom: 10rpx;
}
</style>