<!-- <template>
	<view class="upDataTwo">
		<u-navbar title-size="36" :border-bottom="false" title="人脸识别"></u-navbar>
		<view class="Face-pic">
			<image class="Face-pic-img" src="./static/<EMAIL>" mode="widthFix" />
			<navigator class="ruleExplain" url="/pages/authentication/ruleExplain">
				<view >规则说明</view>
			</navigator>
		</view>
		<view class="brush-face">刷脸实名认证</view>
		<view class="brush-face-tips">按以下注意事项拍摄</view>
		<view class="warning-info flex flex-align-center justify-space-between">
			<view class="warning-info-cont">
				<image class="warning-info-cont-pic" src="./static/<EMAIL>" mode="widthFix" />
				<view class="warning-text">勿遮挡面部</view>
			</view>
			<view class="warning-info-cont">
				<image class="warning-info-cont-pic" src="./static/<EMAIL>" mode="widthFix" />
				<view class="warning-text">勿骑行/走动</view>
			</view>
			<view class="warning-info-cont">
				<image class="warning-info-cont-pic" src="./static/<EMAIL>" mode="widthFix" />
				<view class="warning-text">勿逆光/无光</view>
			</view>
		</view>
		<view class="footer-tips">
			<view class="footer-tips-cont">
				<text>确保是本人操作，并同意</text>
				<u-link href="/pages/authentication/upDataTwo">隐私协议</u-link>
			</view>
			<view class="footer-tips-cont">您提交的信息只会用于实名认证审核</view>
			<navigator url="/pages/authentication/upDataTwo">
				<view class="upDataOne-btn">同意协议，开始认证</view>
			</navigator>
		</view>
	</view>
</template>

<script></script>

<style lang="scss" scoped>
.upDataTwo {
	
	.Face-pic {
		position: relative;
		width: 420rpx;
		height: 420rpx;
		margin: 60rpx auto 0;

		.ruleExplain {
			position: absolute;
			top: 0rpx;
			right: -90rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #333333;
		}

		.Face-pic-img {
			width: 100%;
			height: 100%;
		}
	}

	.brush-face {
		padding: 40rpx 0 10px;
		text-align: center;
		font-size: 50rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
	}

	.brush-face-tips {
		text-align: center;
		font-size: 28rpx;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #999999;
	}

	.warning-info {
		margin-top: 100rpx;
		
		.warning-info-cont {
			text-align: center;
			flex: 1;
			
			.warning-info-cont-pic {
				width: 88rpx;
				height: 88rpx;
			}
			
			.warning-text {
				font-size: 26rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;
				padding: 10rpx 0;
			}
		}
	}
	
	.footer-tips{
		position: fixed;
		bottom: 40rpx;
		left: 0rpx;
		right: 0rpx;
		
		.footer-tips-cont{
			text-align: center;
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
			margin-bottom: 20rpx;
		}
		
		.upDataOne-btn{
			width: 650rpx;
			height: 92rpx;
			line-height: 92rpx;
			margin: 0 auto;
			text-align: center;
			background: linear-gradient(90deg, #FF7900 0%, #F01513 100%);
			border-radius: 46rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
		}
	}
}
</style>
 -->