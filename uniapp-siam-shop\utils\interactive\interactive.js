/**
 * @param {Object} obj 弹窗参数
 * @param {Boolearn} IsRouter 是开启 Promise 模式，用于路由弹窗后延时跳转
 */
export function ShowToast({
	title = "",
	icon = 'none',
	image = '',
	position = 'center',
	duration = 1500,
}, IsRouter = true) {
	if (IsRouter) {
		return new Promise((resolve, reject) => {
			uni.showToast({
				title,
				icon,
				image,
				duration,
				position,
				mask: true,
				success: scs => {
					setTimeout(() => {
						resolve(scs)
					}, duration-500)
				},
				fail: err => {
					reject(err)
				}
			})
		})
	} else {
		uni.showToast({
			title,
			icon,
			image,
			duration,
			position,
			success: scs => {},
			fail: err => {}
		})
	}

}

/**
 * @description 表单验证
 * @param {Object} formData 验证的表单
 * @param {Object} titleList 验证的字段
 * */
export function formIsEmpty(formData, titleList) {
	return new Promise((resove, reject) => {
		for (let key in titleList) {
			if (formData[key] !== undefined) {
				if (!formData[key] || JSON.stringify(formData[key]) == '[]') {
					ShowToast({
						title: titleList[key]
					}, false);
					resove(false)
					return
				}
			}
		}
		resove(true)
	})
}
