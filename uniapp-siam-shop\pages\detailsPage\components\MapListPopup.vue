<template>
	<u-popup v-model="show" mode="bottom" height="700rpx" border-radius="20" :safe-area-inset-bottom="true" :closeable="true" close-icon-pos="top-left">
		<view class="text-cont">
			<view class="text-title">地图列表:</view>
			<view class="flex flex-align-center">
				<text class="moneyicon">百度地图</text>
			</view>
			<view class="flex flex-align-center">
				<text class="moneyicon">高等地图</text>
			</view>
		</view>
	</u-popup>
</template>

<script>
export default {
	data() {
		return {
			show: false,
		};
	},
	watch: {
	},
	methods: {
		showPopup() {
			this.show = true;
			this.gotoMapApp();
		},
		gotoMapApp() {
			/*wx.canLaunch({
				appParameter: 'baidumap://map/direction', // 可以尝试一个空参数判断
				success: (res) => {
					if (res.canLaunch) {
						this.launchBaiduMap(lat, lon); // 如果存在直接调用
					} else {
						uni.navigateTo({ ... }); // 跳转应用商店或其他网页
					}
				}
			});*/

			wx.openLocation({
				latitude: 39.90469, // 纬度，范围为 -90~90，负数表示南纬
				longitude: 116.40717, // 经度，范围为 -180~180，负数表示西经
				name: '北京市', // 位置名
				address: '北京市东城区', // 地址的详细说明
				scale: 18, // 地图缩放级别，取值范围为3~28
				success(res) {
					console.log('打开地图成功', res);
				},
				fail(err) {
					console.error('打开地图失败', err);
				}
			});
		}
	}
};
</script>

<style lang="scss">
.text-cont {
	position: relative;
	top: 0rpx;
	left: 0rpx;
	padding: 80rpx 40rpx 40rpx;
	height: 100%;
	overflow: hidden;

	.scroll-Y {
		height: 80%;
	}

	.text-title {
		font-size: 38rpx;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #333333;
	}
	.moneyicon {
		font-size: 50rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		padding-right: 20rpx;
	}

	.text-cont-input {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 50rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
	}
	.text-title-two {
		padding: 20rpx 0rpx;
		font-size: $font-my-size-28;
		font-family: Source Han Sans CN;
		font-weight: 400;
		height: 78rpx;
		color: #fa3534;
		transition: all 0.1 ease;
	}

	.text-cont-info {
		background: #f5f5f5;
		border-radius: 2px;
		padding: 0rpx 20rpx 0rpx;

		.text-cont-info-menu {
			padding: 20rpx 0;
			border-bottom: 2rpx dashed #c2c2c2;
		}

		.text-cont-info-list {
			width: 630rpx;
			padding: 40rpx 0rpx;
			overflow: hidden;
			transition: all 0.1s ease;

			.u-section {
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0rpx;
				}
			}

			.gripInfo {
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				margin-top: 60rpx;
			}
		}

		.text-cont-info-list-height {
			height: 0rpx;
			padding: 0rpx;
		}
	}
	.confirmBtn {
		position: relative;
		height: 20%;

		.confirm {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 600rpx;
			height: 92rpx;
			min-height: 92rpx;
			line-height: 92rpx;
			background: #252b3b;
			border-radius: 46rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
		}
	}
}
</style>
