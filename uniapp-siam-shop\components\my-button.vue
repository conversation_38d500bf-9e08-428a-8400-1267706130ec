<template>
	<button class="my-button" :class="fiexd ? 'fiexd' : ''" :disabled="disabled"
		:style="'width:' + style.width + ';' + 'height:' + style.height + ';' + 'background:' + style.background + ';' + 'margin:' + style.margin + ';' + 'line-height:' + style.lineHeight + ';' + 'font-size:' + style.fontSize + ';' + 'margin-top:' + style.marginTop + ';' + 'margin-left:' + style.marginLeft + ';' + 'margin-bottom:' + style.marginBottom + ';' + 'margin-right:' + style.marginRight + ';' + 'border-radius:' + style.borderRadius + ';' + 'color:' + style.color + ';' + 'border:' + style.border + ';' + 'font-weight:' + style.fontWeight"
		@click.stop="handClick">
		<slot></slot>
		<view class="my-button-box"></view>
	</button>
</template>

<script>
	/**
	 * mybutton 按钮
	 * @property {Boolean} disabled 是否禁用按钮
	 * @property {String} width 按钮宽度
	 * @property {String} height 按钮宽度
	 * @property {String} fontSize 按钮宽度
	 * @property {String} marginTop 按钮宽度
	 * @property {String} marginLeft 按钮宽度
	 * @property {String} marginLeft 按钮宽度
	 * @property {String} marginRight 按钮宽度
	 * @property {String} marginBottom 按钮宽度
	 * @property {String} background 按钮宽度
	 * @property {String} borderRadius 按钮宽度
	 * @property {String} color 按钮宽度
	 */
	var time = null;
	export default {
		props: {
			color: {
				type: String,
				default: '#fff'
			},
			// 按钮是否禁用
			disabled: {
				type: Boolean,
				default: false
			},
			throttle: {
				type: Boolean,
				default: false
			},
			width: {
				type: [String, Number],
				default: ''
			},
			fiexd: {
				type: Boolean,
				default: false
			},
			height: {
				type: [String, Number],
				default: '86'
			},
			fontSize: {
				type: [String, Number],
				default: '32'
			},
			margin: {
				type: Boolean,
				default: true
			},
			marginTop: {
				type: [String, Number],
				default: '0'
			},
			marginLeft: {
				type: [String, Number],
				default: 'auto'
			},
			marginRight: {
				type: [String, Number],
				default: 'auto'
			},
			marginBottom: {
				type: [String, Number],
				default: '0'
			},
			background: {
				type: [String],
				default: 'linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))'
			},
			borderRadius: {
				type: [String, Number],
				default: 60
			},
			border: {
				type: Boolean,
				default: false
			},
			borderWidth: {
				type: [String, Number],
				default: 1
			},
			borderColor: {
				type: String,
				default: '#666'
			},
			bold: {
				type: Boolean,
				default: false
			},
			flexAuto: {
				type: Boolean,
				default: true
			}
		},
		computed: {
			style() {
				let obj = {
					width: this.width.indexOf('%') >= 0 ? this.width : this.width + 'rpx',
					height: this.height + 'rpx',
					margin: this.margin ? '0 auto' : '0rpx 0rpx 0rpx 0rpx',
					lineHeight: this.border ? this.height - 2 * this.borderWidth + 'rpx' : this.height + 'rpx',
					fontSize: this.fontSize + 'rpx',
					marginTop: this.marginTop + 'rpx',
					marginLeft: this.marginLeft + 'rpx',
					marginRight: this.marginRight + 'rpx',
					borderRadius: this.borderRadius + 'rpx',
					marginBottom: this.marginBottom + 'rpx',
					background: this.disabled ? '#e2e2e2' : this.background,
					color: this.color + ' !important',
					border: this.border ? `${this.borderWidth}rpx solid ${this.borderColor}` : 'none',
					fontWeight: this.bold ? 'bold' : 'none'
				};
				// obj = JSON.stringify(obj)
				// obj = obj.substring(0, obj.lastIndexOf('}'))
				// obj = obj.substr(1);
				// obj = JSON.parse(obj)
				// console.log(obj)
				return obj;
			}
		},
		data() {
			return {};
		},
		methods: {
			handClick() {
				if (!this.time || (Date.now() - this.time > 500)) {
					this.$emit('confirm');
					this.time = Date.now();
				}
			}
		},
		mounted() {}
	};
</script>

<style lang="scss" scoped>
	.my-button {
		position: relative;
		display: block;
		text-align: center;
		outline: none;
		letter-spacing: 10rpx;

		&::after {
			border: none;
			outline: none;
		}
	}

	.after {
		&::after {
			background: rgba(200, 200, 200, 0.3);
			z-index: 1;
		}
	}

	.my-button-box {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 40px;
		height: 40px;
		line-height: 40px;
		transform: translate(-50%, -50%);
		z-index: 2;
		text-align: center;
	}

	.loading {
		&::before {
			display: inline-block;
			width: 40px;
			height: 39px;
			transform-origin: center center;
			animation: myfirst 0.8s linear infinite;
			font-size: 20px;
			color: #ffffff;
			content: '\e61b';
			font-family: 'iconfont';
			z-index: 2;
		}
	}

	.fiexd {
		position: fixed;
		bottom: 20rpx;
		left: 50%;
		transform: translateX(-50%);
	}

	@keyframes myfirst {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}
</style>
