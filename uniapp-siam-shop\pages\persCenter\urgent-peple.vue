<template>
	<view class="urgentpeple">
		<view class="health-title">修改紧急联系人</view>
		<view class="form-box">
			<view class="health-form-item">
				<view class="health-form-item-top flex flex-align-center">
					<text class="form-item-title">紧急联系人姓名：</text>
					<uni-easyinput class="form-item-input" type="text" :inputBorder="false" v-model="formData.urgentUsername" placeholder="请输入紧急联系人姓名"></uni-easyinput>
				</view>
			</view>
			<my-line></my-line>
			<view class="health-form-item">
				<view class="health-form-item-top flex flex-align-center">
					<text class="form-item-title">紧急联系人电话：</text>
					<uni-easyinput class="form-item-input" type="text" :inputBorder="false" v-model="formData.urgentTel" placeholder="请输入紧急联系人电话"></uni-easyinput>
				</view>
			</view>
			<my-line></my-line>
		</view>
		<my-button margin-top="100" width="696" background="#252B3B" color="#fff" border-radius="15" @confirm="submitData">保存</my-button>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	data() {
		return {
			formData: {
				urgentUsername: '',
				urgentTel: ''
			},
			relus: {
				urgentUsername: '紧急联系人姓名不能为空！',
				urgentTel: '紧急联系人电话不能为空！'
			}
		};
	},
	computed: {
		...mapState(['UserInfo'])
	},
	onLoad() {
		this.formData.urgentUsername = this.UserInfo.urgentUsername;
		this.formData.urgentTel = this.UserInfo.urgentTel;
	},
	methods: {
		submitData() {
			this.$interactive.formIsEmpty(this.formData, this.relus).then(flag => {
				if (flag) {
					this.$request({
						url: '/api-merchant/rest/merchant/update',
						data: this.formData
					}).then(result => {
						// #ifdef MP-WEIXIN
						const res = JSON.parse(result)
						// #endif
						if (res.code === 200) {
							uni.$emit('RefreshUserInfo');
							this.$interactive
								.ShowToast({
									title: '提交成功！'
								})
								.then(res => {
									uni.navigateBack({
										delta: 1
									})
								});
						} else {
							this.$interactive.ShowToast({ title: res.message })
						}
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.urgentpeple {
	.health-title {
		font-size: $font-my-size-32;
		background: $uni-bg-color-grey;
		padding: 24rpx 32rpx;
		color: $font-my-color-9;
	}

	.form-box {
		padding: 0 32rpx;

		.health-form-item {
			padding: 20rpx 0;
			margin-bottom: 20rpx;

			.health-form-item-top {
				padding: 10rpx 0;

				.form-item-title {
					font-size: $font-my-size-32;
				}

				.form-item-input {
					flex: 1;
				}
			}

			.health-form-item-tips {
				font-size: $font-my-size-24;
				color: $font-my-color-b;
				padding: 10rpx 0;

				.mr {
					margin-right: 10rpx;
				}
			}
		}
	}
}
</style>
