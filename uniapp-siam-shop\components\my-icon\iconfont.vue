<template>
	<text :style="{ color: color, 'font-size': size + CompType, fontWeight: bold ? 'bold' : '400' }" class="iconfont" :class="[type]" @click="_onClick">
		<slot></slot>
	</text>
</template>

<script>
import iconfont from './iconfont.json';

/**
 * Icons 图标
 * @description 用于展示 icons 图标
 * @tutorial https://ext.dcloud.net.cn/plugin?id=28
 * @property {Number} size 图标大小
 * @property {String} type 图标图案，参考示例
 * @property {String} color 图标颜色
 * @event {Function} click 点击 Icon 触发事件
 */
export default {
	name: 'iconfont',
	props: {
		type: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '#333333'
		},
		size: {
			type: [Number, String],
			default: 16
		},
		bold: {
			type: Boolean,
			default: false
		},
		CompType: {
			type: String,
			default: 'rpx'
		}
	},
	data() {
		return {
			iconfont: iconfont
		};
	},
	methods: {
		_onClick() {
			this.$emit('click');
		},
	}
};
</script>

<style lang="scss" scoped>
@font-face {
	font-family: 'iconfont';
	src: url('./iconfont.ttf') format('woff2');
}

.iconfont {
	font-family: 'iconfont';
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
</style>
