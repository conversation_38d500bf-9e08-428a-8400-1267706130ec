<template>
	<view class="modal">
		<text class="modal-title">{{ modalInfo.title }}</text>
		<view class="cont-box">
			<text class="cont-box-text">
				{{ modalInfo.type == 1 ? `拨号给：${modalInfo.tel}` : `请确认是否转出此订单，当前等级每天还可免费转出${configInfo.riderSet.transferNum}单！` }}
			</text>
		</view>
		<view class="footer-box" v-if="modalInfo.type == 1">
			<text class="btn-text" style="color: #999;" @click="close">取消</text>
			<text class="btn-text btn-text-color" @click="confirm">{{ modalInfo.confirmBtnText }}</text>
		</view>
		<view class="footer-box" v-else-if="modalInfo.type == 2">
			<text class="btn-text" style="color: red;" @click="confirm">确认</text>
			<text class="btn-text btn-text-color" @click="close">{{ modalInfo.confirmBtnText }}</text>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import { dialFun } from '../../common/method/common.js';
export default {
	data() {
		return {
			modalInfo: {
				title: '',
				tel: '',
				confirmBtnText: '确认拨打',
				type: 1
			}
		};
	},
	computed: {
		...mapState(['UserInfo', 'address', 'configInfo'])
	},
	onLoad() {
		uni.$on('modalPhone', this.getModalInfo);
	},
	onUnload() {
		uni.$off('modalPhone');
	},
	methods: {
		getModalInfo(msg) {
			Object.assign(this.modalInfo, msg);
		},
		confirm() {
			// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
			const phone = uni.getSubNVueById('modalID');
			// #endif
			if (this.modalInfo.type == 1) {
				dialFun(this.modalInfo.tel);
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				phone.show('zoom-in', 300);
				// #endif
			} else if (this.modalInfo.type == 2) {
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				phone.show('zoom-in', 300);
				// #endif
				uni.$emit('callback', '', 'transfer');
			}
		},
		close() {
			// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
			const phone = uni.getSubNVueById('modalID');
			phone.hide('zoom-in', 300);
			// #endif
		}
	}
};
</script>

<style scoped>
.modal {
	align-items: center;
}
.modal-title {
	font-family: Source Han Sans CN;
	font-size: 42rpx;
	padding-top: 48rpx;
	font-weight: 500;
	text-align: center;
	font-weight: bold;
	color: #303133;
}

.cont-box {
	flex: 1;
	justify-content: center;
	padding-left: 48rpx;
	padding-right: 48rpx;
}

.cont-box-text {
	font-size: 15px;
	color: #606266;
}

.footer-box {
	border-top-width: 1rpx;
	border-style: solid;
	border-top-color: #eee;
	flex-direction: row;
	width: 750rpx;
}

.btn-text {
	flex: 1;
	text-align: center;
	justify-content: center;
	color: #999;
	font-weight: normal;
	font-size: 32rpx;
	height: 100rpx;
	line-height: 100rpx;
}

.btn-text-color {
	color: #2979ff;
}
</style>
