<template>
	<mescroll-uni @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }"
		:up="{ auto: false,empty:{tip: '暂无报名信息',icon: '/static/icon/bmEmpty.png'} }">
		<view class="studyTrain-box">
			<view class="studyTrain-cont">
				<view class="studyTrain-footer" v-for="(item, index) of trainList" :key="item.id">
					<view class="studyTrain-footer-title flex flex-align-center justify-space-between">
						<view class="tetle-name">
							<text class="tetle-name-text">{{ item.name }}</text>
							<u-tag text="入职培训" mode="dark" size="mini" bg-color="#D8EFFD" color="#138ADB" />
						</view>
						<view class="tetle-type"
							:style="{ color: stateName[item.state] ? stateName[item.state].color : '#de4644' }">
							{{ item.state == 1 ? item.people + '人' : stateName[item.state].title }}
						</view>
					</view>
					<view class="studyTrain-cont-info">培训地址：{{ item.address }}</view>
					<view class="studyTrain-cont-info">联系人：{{ item.linkMan }}</view>
					<view class="studyTrain-cont-info">联系人电话：{{ item.tel }}</view>
				</view>
			</view>
		</view>
	</mescroll-uni>
</template>

<script>
	export default {
		data() {
			return {
				value: 0,
				trainList: [],
				stateName: {
					2: {
						color: '#2196f3',
						title: '已通过'
					},
					3: {
						color: '#f44336',
						title: '已拒绝'
					}
				}
			};
		},
		created() {
			uni.$on('resfTrainAddress', this.getTrainAddress);
		},
		methods: {
			handClickOrderInfo(msg) {
				uni.navigateTo({
					url: '/pages/detailsPage/orderDetails?id=' + msg
				});
			},
			// 下拉回调函数
			downCallback(e) {
				this.mescroll.resetUpScroll();
			},
			// 上拉加载更多
			upCallback(e) {
				this.getTrainAddress(e.num);
			},
			getTrainAddress(msg = 1) {
				this.$request({
					url: '/train/my-enroll',
					method: 'GET',
					data: {
						page: msg
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
						res = JSON.parse(res)
					// #endif
					if (res.code == 1) {
						if (msg == 1) {
							this.trainList = res.data;
						} else {
							this.trainList = this.trainList.concat(res.data);
						}
						this.mescroll.endSuccess(res.data.length, res.data.length >= 10 ? true : true);
					} else {
						this.mescroll.endSuccess(0, false);
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	/deep/.mescroll-uni {
		top: 0rpx !important;
	}

	.studyTrain-box {
		padding: 40rpx 32rpx;

		.studyTrain-cont-info {
			font-size: $font-my-size-26;
			font-weight: 400;
			color: #666666;
			line-height: 48rpx;
			padding-right: 80rpx;
		}
	}
</style>

