<template>
	<view class="answerOne">
		<view class="answerOne-cont">
			<view class="answerOne-title">培训考试</view>
			<view class="answerOne-title-tips">为协助您更好的开展工作，邀请您参加以下入职培训和考试，专项考试可按照您实际需要参加。</view>
			<view class="answerOne-cont-name">
				<view class="answerOne-cont-title">
					<my-icon size="36">&#xe71e;</my-icon>
					<text class="answerOne-cont-title-name">新员工入职必考</text>
				</view>
				<template v-for="(item, index) of studyList">
					<view class="cont-list flex flex-align-center justify-space-between" :key="index">
						<view class="answerOne-cont-ls">{{ item.title }}</view>
						<button class="answerOne-cont-btn" v-if="item.state == 2" @click="routePage(item)">去培训</button>
						<text v-else-if="item.state == 1" style="width: 128rpx; text-align: center; color: #18B566;">已通过</text>
					</view>
				</template>
			</view>
			<view class="answerOne-cont-name">
				<view class="answerOne-cont-title">
					<my-icon size="36">&#xe71e;</my-icon>
					<text class="answerOne-cont-title-name">专业考试</text>
				</view>
				<view class="answerOne-title-tips padding-lr">完成考试后可接改类型订单</view>
				<template v-for="(item, index) of studyListT">
					<view class="cont-list flex flex-align-center justify-space-between" :key="index">
						<view class="answerOne-cont-ls">{{ item.title }}</view>
						<button class="answerOne-cont-btn" v-if="item.state == 2" @click="routePage(item)">去培训</button>
						<text v-else-if="item.state == 1" style="width: 128rpx; text-align: center; color: #18B566;">已通过</text>
					</view>
				</template>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			studyList: [],
			studyListT: []
		};
	},
	onShow() {
		this.getStudyList(1);
		this.getStudyList(2);
	},
	methods: {
		routePage(item) {
			uni.navigateTo({
				url: '/pages/authentication/answer-page-one?id=' + item.id + '&state=' + item.state
			});
		},
		getStudyList(msg) {
			this.$request({
				url: '/train/learn-list',
				method: 'GET',
				data: {
					typeId: msg
				}
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					if (msg == 1) {
						this.studyList = res.data;
					} else if (msg == 2) {
						this.studyListT = res.data;
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.answerOne {
	.answerOne-cont {
		padding: 32rpx;

		.answerOne-title {
			font-size: $font-my-size-54;
			font-weight: bold;
			color: #000000;
		}
		.answerOne-title-tips {
			padding-top: 20rpx;
			font-size: $font-my-size-28;
			font-weight: 400;
			color: $font-my-color-9;
		}

		.padding-lr {
			padding: 0rpx 48rpx 0;
		}

		.answerOne-cont-name {
			padding: 60rpx 0 0;

			.answerOne-cont-title {
				font-size: $font-my-size-38;
				font-weight: bold;
				color: $font-my-color-0;
				padding: 0 0 20rpx;

				.answerOne-cont-title-name {
					margin-left: 20rpx;
				}
			}

			.cont-list {
				position: relative;
				padding: 40rpx 0rpx 40rpx 56rpx;

				.answerOne-cont-ls {
					font-size: $font-my-size-32;
					color: $font-my-color-3;
					flex: 1;
				}

				.answerOne-cont-btn {
					height: 53rpx;
					line-height: 53rpx;
					border-radius: 26rpx;
					background: transparent;
					font-size: $font-my-size-24;
					font-weight: bold;
					color: #999;
					border: 1rpx solid #eee;

					&::after {
						border: none;
						outline: none;
					}
				}

				&::after {
					position: absolute;
					left: 60rpx;
					right: 0rpx;
					bottom: 0rpx;
					display: inline-block;
					content: '';
					height: 1rpx;
					background: $uni-bg-color-grey;
				}
			}
		}
	}
}
</style>
