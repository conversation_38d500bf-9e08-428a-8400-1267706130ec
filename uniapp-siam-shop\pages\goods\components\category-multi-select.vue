<template>
  <view class="category-multi-select">
    <u-popup v-model="show" mode="bottom" border-radius="20" :closeable="false" @close="onClose">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择商品类别</text>
          <button class="confirm-btn" @click="onConfirm">确定</button>
        </view>
        <scroll-view scroll-y class="category-list">
          <view 
            v-for="item in categoryList" 
            :key="item.id"
            class="category-item"
            :class="{ selected: isSelected(item.id) }"
            @click="toggleSelection(item.id)"
          >
            <view class="category-info">
              <text class="category-name">{{ item.name }}</text>
            </view>
            <view class="check-icon">
              <uni-icons 
                :type="isSelected(item.id) ? 'checkbox-filled' : 'circle'" 
                :color="isSelected(item.id) ? '#007AFF' : '#ccc'"
                size="24"
              ></uni-icons>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'CategoryMultiSelect',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    categoryList: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedIds: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedIds = [...newVal];
      },
      immediate: true
    }
  },
  methods: {
    isSelected(id) {
      return this.selectedIds.includes(id.toString());
    },
    toggleSelection(id) {
      const index = this.selectedIds.indexOf(id.toString());
      if (index > -1) {
        this.selectedIds.splice(index, 1);
      } else {
        this.selectedIds.push(id);
      }
    },
    onConfirm() {
      this.$emit('confirm', this.selectedIds);
    },
    onClose() {
      this.$emit('close');
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content {
  min-height: 400rpx;
  max-height: 800rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
	flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.confirm-btn {
  padding: 0rpx 40rpx;
  background-color: #007AFF;
  color: #fff;
  border-radius: 6rpx;
  font-size: 28rpx;
}

.category-list {
  max-height: 600rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  
  &.selected {
    background-color: #f0f8ff;
  }
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 28rpx;
  color: #333;
}

.check-icon {
  margin-left: 20rpx;
}
</style>