<template>
  <view class="perfectInfo">
    <uni-status-bar></uni-status-bar>
    <u-form :model="formData" ref="uForm" :error-type="['message']" :label-width="150">

      <!-- 优惠类型选择器 -->
      <u-select v-model="preferentialTypeSelectShow" mode="single-column" :list="preferentialTypeSelectList" @confirm="onPreferentialTypeConfirm"></u-select>
      <!-- 时效选择器 -->
      <u-select v-model="validTypeSelectShow" mode="single-column" :list="validTypeSelectList" @confirm="onValidTypeConfirm"></u-select>
      <u-calendar v-model="showStartTime" mode="range" max-date="2050-10-1" @change="changeTime"></u-calendar>
      <!-- <u-calendar v-model="showEndTime" mode="date" @change="changeTime"></u-calendar> -->
      
      <view class="phone-box">
        <!-- 优惠券名称 -->
        <u-form-item label="优惠券名称" prop="name" :required="true">
          <uni-easyinput class="phone-input" :inputBorder="false" v-model="formData.name"
            placeholder="请输入优惠券名称"></uni-easyinput>
        </u-form-item>

        <!-- 优惠类型 -->
        <!-- <u-form-item label="优惠类型" prop="preferentialType" :required="true">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showPreferentialTypeSelect">
            <text>{{ getPreferentialTypeText(formData.preferentialType) }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item> -->

        <!-- 折扣额度 (仅当优惠类型为折扣时显示) -->
        <u-form-item label="折扣额度" prop="discountAmount" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.discountAmount"
            placeholder="请输入（如：0折=0、5折=0.5）"></uni-easyinput>
        </u-form-item>

        <!-- 满足价格 (仅当优惠类型为满减时显示) -->
        <!-- <u-form-item v-if="formData.preferentialType === 1"  label="满足价格" prop="limitedPrice" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.limitedPrice"
            placeholder="请输入满足价格"></uni-easyinput>
        </u-form-item> -->

        <!-- 减价额度 (仅当优惠类型为满减时显示) -->
        <!-- <u-form-item label="减价额度" prop="reducedPrice" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.reducedPrice"
            placeholder="请输入减价额度"></uni-easyinput>
        </u-form-item> -->

        <!-- 使用规则描述 -->
        <u-form-item label="使用规则描述" prop="description" :required="true">
          <uni-easyinput class="phone-input" type="textarea" :inputBorder="false" v-model="formData.description"
            placeholder="请输入使用规则描述"></uni-easyinput>
        </u-form-item>

        <!-- 时效 -->
        <u-form-item label="时效" prop="validType" :required="true">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showValidTypeSelect">
            <text>{{ getValidTypeText(formData.validType) }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item>

        <!-- 使用开始时间 (仅当时效为固定日期时显示) -->
        <u-form-item v-if="formData.validType == '1'" label="开始时间" prop="validStartTime" :required="false">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showDatePicker('start')">
            <text>{{ formData.validStartTime || '请选择开始时间' }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item>

        <!-- 使用结束时间 (仅当时效为固定日期时显示) -->
        <u-form-item v-if="formData.validType == '1'" label="结束时间" prop="validEndTime" :required="false">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showDatePicker('end')">
            <text>{{ formData.validEndTime || '请选择结束时间' }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item>

        <!-- 有效天数 (仅当时效为领取后N天有效时显示) -->
        <u-form-item v-if="formData.validType == '2'" label="有效天数" prop="validDays" :required="false">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.validDays"
            placeholder="请输入有效天数"></uni-easyinput>
        </u-form-item>


      </view>
    </u-form>

    <my-button margin-top="60" :bold="true" color="#fff" font-size="32" @confirm="saveCoupon">保存</my-button>
  </view>
</template>

<script>
export default {
  data () {
    return {
      isEdit: false,
      couponId: null,
      formData: {
        // name: '',
        // preferentialType: 1, // 1-满减，2-折扣
        // discountAmount: '',
        // limitedPrice: null,
        // reducedPrice: null,
        // description: null,
        // validType: '1', // 1-固定日期，2-领取后N天有效
        validStartTime: null,
        validEndTime: null,
        // validDays: ''
      },
      // u-select相关数据
      // 优惠类型选择器
      preferentialTypeSelectShow: false,
      preferentialTypeSelectList: [],
      preferentialTypeList: [
        { name: '满减', value: 1 },
        { name: '折扣', value: 2 }
      ],
      // 时效选择器
      validTypeSelectShow: false,
      validTypeSelectList: [],
      validTypeList: [
        { name: '使用时间段', value: '1' },
        { name: '使用天数', value: '2' }
      ],
      // 日期选择器
      dateType: '', // 'start' 或 'end'
      showStartTime: false,
      showEndTime: false,
      // 表单验证规则
      rules: {
        name: [{ 
          required: true, 
          message: '请输入优惠券名称', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            return !!value; 
          }
        }], 
        preferentialType: [{ 
          required: true, 
          message: '请输入优惠类型', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            return !!value; 
          }
        }], 
        discountAmount: [{ 
          required: true, 
          message: '请输入折扣额度', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            // if (!value) return false;
            const numValue = Number(value);
            if (isNaN(numValue) || numValue < 0 || numValue >= 1) {
              uni.showToast({
                title: '请填写正确的折扣额度',
                icon: 'none'
              });
              return false;
            }
            return true;
          }
        }], 
        limitedPrice: [{ 
          required: true, 
          message: '请输入满足价格', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            return !!value; 
          }
        }], 
        reducedPrice: [{ 
          required: true, 
          message: '请输入减价额度', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            return !!value; 
          }
        }], 
        description: [{ 
          required: true, 
          message: '请输入使用规则描述', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            return !!value; 
          }
        }], 
        validType: [{ 
          required: true, 
          message: '请选择时效', 
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => { 
            return !!value; 
          }
        }], 
        validStartTime: [{ 
          required: false, 
          message: '请选择使用开始时间', 
          trigger: ['change', 'blur']
        }], 
        validEndTime: [{ 
          required: false, 
          message: '请选择使用结束时间', 
          trigger: ['change', 'blur']
        }], 
        // validDays: [{ 
        //   required: false, 
        //   message: '请输入自领取之日起有效天数', 
        //   trigger: ['change', 'blur']
        // }]
      }
    }
  },
  onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
  onLoad (options) {
    if (options.data) {
      try {
        const data = JSON.parse(options.data)
        this.isEdit = true;
        this.couponId = data.id;
        // 只合并formData中已有的字段
        this.formData = {
          // name: data.name || '',
          // preferentialType: data.preferentialType || 1,
          // discountAmount: data.discountAmount || '',
          // limitedPrice: data.limitedPrice || '',
          // reducedPrice: data.reducedPrice || '',
          // description: data.description || '',
          // validType: data.validType || '1',
          // validStartTime: data.validStartTime || '',
          // validEndTime: data.validEndTime || '',
          // validDays: data.validDays || '',
          // id: data.id
          ...data
        }
      } catch (error) {
        console.error('解析数据失败:', error);
      }
    }
    
    // 准备选择器数据
    
    this.preferentialTypeSelectList = this.preferentialTypeList.map(item => ({
      label: item.name,
      value: item.value
    }));
    
    this.validTypeSelectList = this.validTypeList.map(item => ({
      label: item.name,
      value: item.value
    }));
  },
  methods: {

    
    // 优惠类型选择器
    showPreferentialTypeSelect() {
      this.preferentialTypeSelectShow = true;
    },
    onPreferentialTypeConfirm(e) {
      const item = e[0];
      if (item) {
        this.formData.preferentialType = item.value;
      }
      this.preferentialTypeSelectShow = false;
    },
    getPreferentialTypeText(type) {
      const typeMap = {
        1: '满减',
        2: '折扣'
      };
      return typeMap[type] || '请选择';
    },
    
    // 时效选择器
    showValidTypeSelect() {
      this.validTypeSelectShow = true;
    },
    onValidTypeConfirm(e) {
      const item = e[0];
      if (item) {
        this.formData.validType = item.value;
      }
      this.validTypeSelectShow = false;
    },
    getValidTypeText(type) {
      const typeMap = {
        '1': '使用时间段',
        '2': '使用天数'
      };
      return typeMap[type] || '请选择';
    },
    
    // 日期选择器
    showDatePicker(type) {
      this.dateType = type;
       this.showStartTime = true;
    },
    changeTime(e) {

      this.formData.validStartTime = this.$u.timeFormat(new Date(e.startDate), 'yyyy/mm/dd');
      this.formData.validEndTime = this.$u.timeFormat(new Date(e.endDate), 'yyyy/mm/dd');
      this.showStartTime = false;
    },
    

    // 保存优惠券
    async saveCoupon() {
      // 使用u-form进行表单验证
      try {
        const valid = await this.$refs.uForm.validate();
        if (!valid) {
          return;
        }
      } catch (error) {
        console.error('表单验证失败:', error);
        return;
      }

      uni.showLoading({ title: '保存中...' });
      try {
        const url = this.isEdit
          ? `/api-promotion/rest/merchant/coupons/update`
          : '/api-promotion/rest/merchant/coupons/insert';

        const res = await this.$request({
          url,
          method: this.isEdit ? 'PUT' : 'POST',
          data: {
            ...this.formData,
            preferentialType: 1,
            source: 1
          }
        });

        if (res.code === 200) {
          uni.showToast({ title: '保存成功', icon: 'success' });
          // 设置全局标记，用于返回列表页时刷新数据
          uni.setStorageSync('couponsListNeedRefresh', true);
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({ title: res.message || '保存失败', icon: 'none' });
        }
      } catch (error) {
        console.error('保存优惠券失败:', error);
        uni.showToast({ title: '保存失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.perfectInfo {
  padding: 0 32rpx;

  .phone-box {
    padding-top: 20rpx;
  }

  // u-form-item 样式调整
  /deep/ .u-form-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid $uni-bg-color-grey;
    
    .u-form-item__body {
      display: flex;
      align-items: center;
    }
    
    .u-form-item__body__left__content__label {
      font-size: $font-my-size-32;
      color: $font-my-color-3;
      min-width: 150rpx;
    }
  }
}

.phone-input {
  flex: 1;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.flex {
  display: flex;
}

.flex-align-center {
  align-items: center;
}

.justify-space-between {
  justify-content: space-between;
}
</style>