<template>
	<view class="perfectInfo">
		<view class="phone-box">
			<view class="phone flex flex-align-center">
				<text class="label-title" style="margin-right: 20rpx;">头像</text>
				<view class="addHeadPic" @click="addHeadPic" v-if="!fromData.headImg">
					<uni-icons class="addHeadPic-icon" type="plusempty"></uni-icons>
				</view>
				<image class="addHeadPic" :src="$common.getFullFilePath(this.imgSrc)" v-else @click="addHeadPic"></image>
			</view>
			<view class="phone flex flex-align-center">
				<text class="label-title" style="margin-right: 20rpx;">性别：</text>
				<u-radio-group class="studyTrain-footer-btn" v-model="fromData.sex">
					<u-radio name="1">男</u-radio>
					<u-radio name="2">女</u-radio>
				</u-radio-group>
			</view>
			<view class="phone flex flex-align-center">
				<text class="label-title">年龄：</text>
				<uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="fromData.age"
					placeholder="请输入您的真实年龄"></uni-easyinput>
			</view>
			<view class="phone flex flex-align-center justify-space-between" @click="openMap">
				<text class="label-title" style="margin-right: 16rpx;">家庭地址：</text>
				<text class="phone-input">{{ fromData.address ? fromData.address : '请选择您的家庭地址' }}</text>
				<uni-icons type="forward"></uni-icons>
			</view>
		</view>
		<my-button margin-top="60" :bold="true" color="#fff" font-size="32" @confirm="submitData">保存</my-button>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	export default {
		data() {
			return {
				fromData: {
					headImg: '',
					sex: '1',
					age: '',
					address: ''
				},
				imgSrc: '',
				ruls: {
					headImg: '请先上传您的头像',
					sex: '请输入您的性别',
					age: '请输入您的年龄',
					address: '请输入详细地址'
				}
			};
		},
		computed: {
			...mapState(['UserInfo'])
		},
		onLoad() {
			this.fromData = {
				headImg: this.UserInfo.headImg,
				sex: this.UserInfo.sex,
				age: this.UserInfo.age,
				address: this.UserInfo.address
			};
			this.imgSrc = this.UserInfo.headImg;
		},
		methods: {
			openMap() {
				uni.chooseLocation({
					success: (res) => {
						this.fromData.address = res.address + '.' + res.name;
					}
				})
			},
			// 上传头像
			addHeadPic() {
				this.$common
					.file_select({
						sourceType: ['album', 'camera']
					})
					.then(res => {
						this.$common.UpLoadFile(res).then(uploadRes => {
							if (uploadRes && uploadRes.length >= 1) {
								this.fromData.headImg = uploadRes[0];
								this.imgSrc = this.fromData.headImg;
							} else {
								this.$interactive.ShowToast({ title: '上传失败' }, false );
							}
						}).catch(error => {
							this.$interactive.ShowToast({ title: '上传失败' }, false );
						});
					});
			},
			// 表单提交
			submitData() {
				this.$interactive.formIsEmpty(this.fromData, this.ruls).then(flag => {
					if (flag) {
						this.$request({
							url: '/api-merchant/rest/merchant/update',
							data: this.fromData
						}).then(result => {
							// #ifdef MP-WEIXIN
							const res = JSON.parse(result)
							// #endif
							if (res.code === 200) {
								uni.$emit('RefreshUserInfo');
								this.$interactive
									.ShowToast({
										title: '保存成功！'
									})
									.then(() => {
										uni.navigateBack({
											delta: 1
										});
									});
							} else {
								this.$interactive.ShowToast({
										title: res.message
									},
									false
								);
							}
						});
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.perfectInfo {
		padding: 0 32rpx;

		.phone {
			padding: 20rpx 0;
			border-bottom: 1rpx solid $uni-bg-color-grey;

			.label-title {
				font-size: $font-my-size-32;
				color: $font-my-color-3;
				text-align: justify;
				min-width: 150rpx;
			}

			.phone-input {
				padding: 0rpx 0;
				flex: 1;
			}

			.addHeadPic {
				position: relative;
				border: 1rpx solid #999;
				width: 120rpx;
				height: 120rpx;
				border-radius: 10rpx;
				text-align: center;

				.addHeadPic-icon {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
	}
</style>
