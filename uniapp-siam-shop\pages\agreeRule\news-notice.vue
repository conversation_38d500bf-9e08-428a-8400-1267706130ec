<template>
	<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false, empty: { tip: '~暂无消息~', icon: '../../static/iconimage/empty-null.png' } }">
		<view class="news-notice">
			<template v-for="(item, index) of newsList">
				<view class="list-modl" :key="index" @click="routePage(item)">
					<view class="flex flex-align-center justify-space-between" style="width: 100%; max-width: 100%;">
						<view style="flex: 1; min-width: 0rpx;">
							<view class="list-title flex flex-align-start" :style="{ paddingLeft: item.state == 2 ? '0rpx' : '24rpx', color: item.state == 2 ? '#333' : '#999' }">
								<section v-if="item.state == 2" class="list-circ"></section>
								<text class="list-title-text">{{ item.title }}</text>
							</view>
							<view class="list-time">{{ item.createdAt.substring(0, 16) }}</view>
							<view class="list-cont" v-html="item.body"></view>
						</view>
						<uni-icons type="arrowright"></uni-icons>
					</view>
					<my-line :margin="[20, 0, 0, 0]"></my-line>
				</view>
			</template>
		</view>
	</mescroll-body>
</template>

<script>
export default {
	data() {
		return {
			newsList: []
		};
	},
	methods: {
		// 初始化
		mescrollInit() {},
		// 下拉刷新
		downCallback(e) {
			e.resetUpScroll();
		},
		// 上拉加载
		upCallback(msg) {
			this.getNewsList(msg.num);
		},
		getNewsList(msg) {
			this.$request({
				url: '/config/notice',
				data: { type: 4 },
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					if (msg == 1) {
						this.newsList = res.data;
					} else {
						Object.assign(this.newsList, res.data);
					}
					this.mescroll.endSuccess(res.data.length, res.data.length >= 10 ? true : false);
				} else {
					this.mescroll.endSuccess(0, false);
				}
			});
		},
		routePage(msg) {
			uni.setStorage({
				key: 'content',
				data: msg,
				success: () => {
					uni.navigateTo({
						url: '/pages/agreeRule/notice-info?type=4&id=' + msg.id
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.news-notice {
	padding: 0 32rpx;

	.list-modl {
		margin-bottom: 20rpx;
		overflow: hidden;

		.list-title {
			color: $font-my-color-3;
			font-size: $font-my-size-32;
			width: 100%;
			max-width: 100%;

			.list-circ {
				width: 12rpx;
				min-width: 12rpx;
				height: 12rpx;
				background-color: #fa3534;
				border-radius: 50%;
				margin-right: 10rpx;
				margin-top: 18rpx;
			}
			.list-title-text {
				white-space: nowrap;
				word-break: normal;
				text-overflow: ellipsis;
				overflow: hidden;
				flex: 1;
				min-width: 0rpx;
				width: 100%;
				max-width: 100%;
			}
		}
	}

	.list-time {
		color: #999;
		font-size: $font-my-size-24;
		padding: 6rpx 24rpx 10rpx;
	}

	.list-cont {
		width: 100%;
		max-width: 100%;
		padding: 0 24rpx;
		color: #999;
		font-size: $font-my-size-28;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;

		/deep/ol,
		/deep/ul,
		/deep/p,
		/deep/div,
		/deep/a,
		/deep/h3,
		/deep/h4,
		/deep/h5,
		/deep/h6 {
			font-size: 28rpx !important;
			height: auto !important;
			line-height: 48rpx !important;
			color: #999 !important;
			padding: 0rpx !important;
			margin: 0rpx !important;

			li {
				color: #999 !important;
				outline: none;
				list-style: none;
			}
		}
	}
}
</style>
