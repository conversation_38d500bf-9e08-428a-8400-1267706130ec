<template>
	<view class="register">
		<view class="register-title">输入手机号</view>
		<view class="phone">
			<uni-easyinput class="phone-input" type="number" maxlength="11" :inputBorder="false" v-model="tel" placeholder="请输入11位手机号码"></uni-easyinput>
		</view>
		<my-button margin-top="60" height="86" width="626" :bold="true" color="#fff" :disabled="!tel" font-size="34" @confirm="downPage">下一步</my-button>
		<view class="tirp-info">
			<view class="flex flex-align-center justify-space-center">
				<u-checkbox-group @change="changeRadio($event, 'agreementShow')" size="26">
					<u-checkbox v-model="agreementShow" name="1">
						<text class="tirp-info-text">注册代表同意</text>
						<text class="tirp-info-color" @click.stop="getAgreement('agreement')">《易达脉联商家用户协议》</text>
					</u-checkbox>
				</u-checkbox-group>
			</view>
			<view class="flex flex-align-center justify-space-center">
				<u-checkbox-group @change="changeRadio($event, 'agreementShowOne')" size="26">
					<u-checkbox v-model="agreementShowOne" name="1">
						<text class="tirp-info-text">和</text>
						<text class="tirp-info-color" @click.stop="getAgreement('privacy')">《易达脉联商家隐私协议》</text>
					</u-checkbox>
				</u-checkbox-group>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			show: false,
			tel: '',
			agreement: '',
			agreementShow: false,
			agreementShowOne: false,
			RunDomainNameShow: false
		};
	},
	methods: {
		changeRadio(msg, key) {
			if (msg.length > 0) {
				this[key] = true;
			} else {
				this[key] = false;
			}
		},
		downPage() {
			// for local test only
			// this.tel = '13256781013';
			// this.agreementShow = true;
			// this.agreementShowOne = true;

			// uni.navigateTo({
			// 	url: '/pages/login/register-code?type=1&PageType=1&tel=' + this.tel
			// });
			// return;

			if (this.tel.length === 11) {
				if (!this.agreementShow || !this.agreementShowOne) {
					this.$interactive.ShowToast({
						title: '请先阅读协议'
					});
					return;
				}
				this.$request({
					url: '/api-util/rest/smsLog/sendMobileCode',
					data: {
						mobile: this.tel,
						type: "register"
					},
					IsGetStorg: false
				}).then(res => {
					if (res.code === 200) {
						uni.navigateTo({
							url: '/pages/login/register-code?type=1&PageType=1&tel=' + this.tel
						});
					} else {
						this.$interactive.ShowToast({ title: res.message }, false);
					}
				});
			} else {
				this.$interactive.ShowToast(
					{
						title: '请输入正确的手机号！'
					},
					false
				);
			}
		},
		confirm() {
			this.show = false;
		},
		// 获取协议信息
		getAgreement(msg) {
			uni.navigateTo({
				url: '/pages/login/' + msg
			})
		}
	}
};
</script>

<style lang="scss" scoped>
.register {
	.register-title {
		font-size: $font-my-size-30;
		font-weight: bold;
		padding: 40rpx 62rpx 0;
	}
	.phone {
		margin: 40rpx 62rpx 0;
		border-bottom: 1rpx solid $uni-bg-color-grey;

		.phone-input {
			padding: 20rpx 0;
			flex: 1;

			.placeholderStyl {
			}
		}
	}

	.tirp-info {
		position: fixed;
		bottom: 40rpx;
		left: 0rpx;
		right: 0rpx;
		text-align: center;
		padding-top: 30rpx;
		font-size: $font-my-size-24;
		color: $font-my-color-8;

		.tirp-info-text {
			display: inline-block;
			font-size: $font-my-size-24;
			color: $font-my-color-3;
			margin-bottom: 10rpx;
		}

		.tirp-info-color {
			color: #03a9f4;
			font-size: $font-my-size-24;
		}

		/deep/.u-checkbox__icon-wrap {
			border-radius: 50% !important;
		}
	}

	.agreement {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100%;

		.agreement-title {
			font-size: $font-my-size-36;
			padding: 20rpx 32rpx;
			font-weight: bold;
		}

		.agreement-cont {
			flex: 1;
			text-align: justify;
			width: 100%;
			padding: 0 32rpx;
		}

		.footer-btn {
			width: 100%;
			text-align: center;
			padding: 30rpx 0;
			font-size: $font-my-size-34;
			font-weight: bold;
			border-top: 1rpx solid #ccc;
		}
	}
}
</style>
