<template>
	<view class="extract">
		<my-nav-bar title="提取保证金" color="#333"></my-nav-bar>
		<view class="extract-cont">
			<view class="extract-box flex flex-align-center">
				<view class="extract-name">金额：</view>
				<input class="extract-input" type="number" v-model="moneNum" placeholder="0" placeholder-class="placeholder-class"/>
			</view>
			<my-line :margin="[0,0,20]"></my-line>
			<view class="flex flex-align-center justify-space-between">
				<view class="extract-money-info">可提取金额 0</view>
				<button class="extract-btn">全部提取</button>
			</view>
			<button class="extract-confirm-btn" :class="{ 'extract-confirm-btn-bg': !moneNum}" type="default" :disabled="!moneNum">提取</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			moneNum:　""
		};
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.extract {
	.extract-cont {
		padding: 0 30rpx;
		
		.extract-box {
			padding: 30rpx 0 0rpx;

			.extract-name {
				font-size: 32rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: $font-my-color-6;
			}

			.extract-input {
				flex: 1;
				margin-left: 20rpx;
				height: 100rpx;
				font-size: 50rpx;
				font-weight: bold;
				color: $font-my-color-3;
				
				.placeholder-class{
					font-weight: 400;
					color: #aaa;
				}
			}
		}

		.extract-money-info {
			font-size: $font-my-size-24;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
		}

		.extract-btn {
			margin: 0rpx;
			padding: 0rpx 10rpx;
			border: 1rpx solid #c1c1c1;
			border-radius: 50rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: $font-my-color-3;
			line-height: 40rpx;
			outline: none;
			background: transparent;
			
			&::after{
				border: none;
				outline: none;
			}
		}

		.extract-confirm-btn {
			margin-top: 110rpx;
			height: 98rpx;
			border-radius: 4px;
			background: #eee;
			color: #fff;
			
			&::after{
				border: none;
				outline: none;
			}
		}
		
		.extract-confirm-btn-bg{
			
		}
	}
}
</style>
