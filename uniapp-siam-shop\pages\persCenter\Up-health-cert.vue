<template>
	<view class="Up-health-cert">
		<my-nav-bar title="健康证"></my-nav-bar>
		<!-- 	<view class="health-tips">健康证审核过程中暂时无法接单</view>
		<view class="health-title">填写证件信息</view>
		<view class="health-form">
			<view class="health-form-item">
				<view class="health-form-item-top flex flex-align-center">
					<text class="form-item-title">证件号码</text>
					<input class="form-item-input" type="text" v-model="formData.code" placeholder="请输入健康证号码" placeholder-class="form-item-input-pl" />
				</view>
			</view>
			<MyLine></MyLine>
			<view class="health-form-item">
				<view class="health-form-item-top flex flex-align-center">
					<text class="form-item-title">发证单位</text>
					<input class="form-item-input" type="text" v-model="formData.company" placeholder="请输入发证单位名称(选填)" placeholder-class="form-item-input-pl" />
				</view>
			</view>
			<MyLine></MyLine>
			<view class="health-form-item">
				<view class="health-form-item-top flex flex-align-center">
					<text class="form-item-title">到期时间</text>
					<input
						class="form-item-input"
						type="text"
						v-model="formData.endAt"
						:disabled="true"
						placeholder="选择证件到期时间"
						placeholder-class="form-item-input-pl"
						@click="openTime"
					/>
				</view>
				<view class="health-form-item-tips">
					<uni-icons class="mr" type="info-filled" color="#bbb" size="32"></uni-icons>
					<text class="health-form-item-tips-text">不同区域证件不一样，请仔细核减并确认到期时间</text>
				</view>
			</view>
			<MyLine></MyLine>
		</view> -->
		<view class="health-title">上传证件照片</view>
		<view class="health-form">
			<view class="health-form-item">
				<view class="health-form-item-top flex flex-align-center"><text class="form-item-title">健康证正面</text></view>
				<view class="health-form-item-tips" style="padding: 0rpx;"><text class="health-form-item-tips-text">不同区域证件不一样，请仔细核减并确认到期时间</text></view>
			</view>
			<view class="photograph">
				<view class="photograph-btn" v-if="!formData.healthIcon" @click="UpLoadPic"><uni-icons type="camera" size="60" color="#aaa"></uni-icons></view>
				<view class="photograph-pic-box" :class="{ handPreview: handPreviewShow }" v-else>
					<image class="photograph-pic" :src="formData.healthIcon" mode="widthFix" @click="handPreview"></image>
					<uni-icons class="close-btn" size="40" type="close" @click="del"></uni-icons>
				</view>
			</view>
		</view>
		<my-button margin-top="100" width="696" background="#252B3B" color="#fff" border-radius="15" @confirm="submitData">保存</my-button>
		<u-picker v-model="show" mode="time" @confirm="confirmSelt"></u-picker>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	data() {
		return {
			show: false,
			handPreviewShow: false,
			pathPic: '',
			formData: {
				type: 'health',
				healthIcon: ''
			},
			ruls: {
				icon: '请上传健康证'
			}
		};
	},
	computed: {
		...mapState(['UserInfo'])
	},
	onLoad() {
		this.formData.healthIcon = this.UserInfo.healthIcon || '';
	},
	methods: {
		// 图片上传
		UpLoadPic() {
			this.$common.file_select({ quality: 50, sourceType: ['album', 'camera'] }).then(res => {
				this.$common.UpLoadFile(res).then(msg => {
					this.formData.healthIcon = msg.toString();
				});
			});
		},
		// 打开时间选择
		openTime() {
			this.show = true;
		},
		confirmSelt(msg) {
			this.formData.endAt = `${msg.year}年${msg.month}月${msg.day}日`;
		},
		// 图片预览
		handPreview() {
			this.handPreviewShow = !this.handPreviewShow;
		},
		del() {
			this.formData.healthIcon = '';
		},
		// 获取健康证表单
		// getHealthInfo() {
		// 	this.$request({
		// 		url: '/login/rider-health',
		// 		method: 'GET'
		// 	}).then(res => {
		// 		if (res.code == 1) {
		// 			for (let key in this.formData) {
		// 				this.formData[key] = res.data[key];
		// 			}
		// 		} else {
		// 			this.$interactive.ShowToast(
		// 				{
		// 					title: res.message
		// 				},
		// 				false
		// 			);
		// 		}
		// 	});
		// },
		// 数据提交
		submitData() {
			this.$interactive.formIsEmpty(this.formData, this.ruls).then(flag => {
				if (flag) {
					this.$request({
						url: '/login/modify-rider',
						data: this.formData
					}).then(res => {
						// #ifdef MP-WEIXIN
							res = JSON.parse(res)
						// #endif
						if (res.code == 1) {
							uni.$emit('RefreshUserInfo');
							this.$interactive
								.ShowToast({
									title: '提交成功！'
								})
								.then(res => {
									uni.navigateBack({
										delta: 1
									});
								});
						} else {
							this.$interactive.ShowToast(
								{
									title: res.message
								},
								false
							);
						}
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.Up-health-cert {
	.form-item-input-pl {
		color: $font-my-color-a;
	}

	.health-tips {
		background: #7d7f8b;
		color: #ffffff;
		padding: 16rpx 0rpx;
		text-align: center;
	}

	.health-title {
		font-size: $font-my-size-24;
		background: $uni-bg-color-grey;
		padding: 24rpx 32rpx;
		color: $font-my-color-9;
	}

	.health-form {
		padding: 0 32rpx;

		.health-form-item {
			padding: 20rpx 0;
			margin-bottom: 20rpx;

			.health-form-item-top {
				padding: 10rpx 0;

				.form-item-title {
					padding-right: 40rpx;
					font-size: $font-my-size-32;
				}

				.form-item-input {
					height: 48rpx;
					flex: 1;
				}
			}

			.health-form-item-tips {
				font-size: $font-my-size-24;
				color: $font-my-color-b;
				padding: 10rpx 0;

				.mr {
					margin-right: 10rpx;
				}
			}
		}

		.photograph {
			.photograph-btn {
				border: 1rpx dashed #aaa;
				width: 250rpx;
				height: 150rpx;
				line-height: 130rpx;
				text-align: center;
				border-radius: 10rpx;
			}

			.photograph-pic-box {
				position: relative;
				width: 250rpx;
				height: 150rpx;
				transition: all 0.1s ease;

				.photograph-pic {
					width: 100%;
					min-width: 100%;
					max-width: 100%;
					max-height: 100%;
				}

				.close-btn {
					position: absolute;
					top: -30rpx;
					right: -30rpx;
				}
			}

			.handPreview {
				position: fixed;
				top: 0rpx;
				bottom: 0rpx;
				left: 0rpx;
				right: 0rpx;
				background: #000;
				z-index: 999;
				width: auto;
				height: auto;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}

			.photograph-pic {
				max-height: auto;
			}
		}
	}
}
</style>
