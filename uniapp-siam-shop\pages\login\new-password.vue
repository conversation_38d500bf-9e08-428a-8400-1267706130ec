<template>
	<view class="new-password">
		<view class="register-title">请设置商家账户信息</view>
		<view class="register-title-tips">密码最少6位字符，包含字母、数字、符号中的任意两项</view>
		<view class="phone-box">
			<view class="phone flex flex-align-center">
				<uni-easyinput class="phone-input" type="text" :inputBorder="false" v-model="fromData.username" placeholder="请输入登录账号"></uni-easyinput>
			</view>
			<view class="phone flex flex-align-center">
				<uni-easyinput class="phone-input" type="password" :inputBorder="false" v-model="password1" placeholder="请输入密码"></uni-easyinput>
			</view>
			<view class="phone flex flex-align-center">
				<uni-easyinput class="phone-input" type="password" :inputBorder="false" v-model="password2" placeholder="请确认密码"></uni-easyinput>
			</view>
		</view>
		<my-button margin-top="60" :bold="true" width="626" color="#fff" :disabled="!(fromData.username && password1 && password2)" font-size="34" @confirm="submitData">提交并登录</my-button>
	</view>
</template>

<script>
import encodeDecode from "@/utils/encodeDecode/index.js"
import {mapMutations} from "vuex";
export default {
	data() {
		return {
			password1: '',
			password2: '',
			fromData: {
				username: '',
				newPassword: '',
				oldPassword: encodeDecode.wxStrToBase64('123456'), 
				domainUrl: '',
				systemId: '',
				registrationId: '',
				mobile: '',
				mobileCode: ''
			},
			ruls: {
				username: '请输入用户名',
				newPassword: '请输入密码'
				// domainUrl: '请输入域名',
				// systemId: '平台ID获取错误！'
			}
		};
	},
	onLoad(e) {
		// const obj = uni.getStorageSync('RunDomainName');
		// this.fromData.registrationId = uni.getStorageSync('registrationID');
		this.fromData.mobile = e.tel;
		this.fromData.mobileCode = e.mobileCode;
		// this.fromData.domainUrl = obj.RunDomainName;
		// this.fromData.systemId = obj.systemId;

		// for local test only
		// this.fromData.username = 'username013'
		// this.password1 = '123456';
		// this.password2 = '123456';
	},
	methods: {
		...mapMutations(['setAccount']),

		// 表单提交
		submitData() {
			if (this.password1 === this.password2) {
				this.fromData.newPassword = encodeDecode.wxStrToBase64(this.password2);
				this.$interactive.formIsEmpty(this.fromData, this.ruls).then(res => {
					if (res) {
						this.$request({
							url: '/api-merchant/rest/merchant/updatePassword',
							data: this.fromData
						}).then(result => {
							console.log(result);
							if (result.code === 200) {
								// uni.setStorage({
								// 	key: 'RunKey',
								// 	data: result.data,
								// 	complete: () => {
										this.$interactive.ShowToast({ title: '注册成功！' })
											.then(res => {
												// The token stands for logined status
												// this.setAccount({
												// 	authorization: result.data.token
												// })
												// uni.$emit('RefreshUserInfo');
												// uni.$emit('IsOpenGps');
												// uni.reLaunch({ url: '/pages/index/index' });
                        uni.reLaunch({url: '/pages/myInfo/shopInfo'});

											});
								// 	}
								// });
							} else {
								this.$interactive.ShowToast(
									{
										title: result.message
									},
									false
								);
							}
						});
					}
				});
			} else {
				this.$interactive.ShowToast(
					{
						title: '密码不一致，请重新输入！'
					},
					false
				);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.new-password {
	padding: 0 62rpx;

	.register-title {
		font-size: $font-my-size-30;
		font-weight: bold;
		padding: 40rpx 0rpx 0;
	}
	.register-title-tips {
		font-size: $font-my-size-24;
		color: $font-my-color-9;
		padding-top: 30rpx;
	}

	.phone-box {
		padding-top: 40rpx;

		.phone {
			border-bottom: 1rpx solid $uni-bg-color-grey;

			.phone-input {
				padding: 20rpx 0;
			}

			/deep/.uni-input-input {
				font-size: 32rpx;
			}

			/deep/.uni-input-placeholder {
				color: #999;
				font-size: 32rpx;
			}

			/deep/.uni-icons {
				font-size: 40rpx !important;
			}
		}
	}
}
</style>
