<template>
	<view class="empty-main">
		<image class="empty-pic" src="@/static/icon/empty.png" mode="widthFix"></image>
		<text class="trip">{{ content }}</text>
	</view>
</template>

<script>
export default {
	props: {
		content: {
			type: String,
			default: '列表为空'
		}
	},
	data() {
		return {};
	}
};
</script>

<style lang="scss" scoped>
.empty-main {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	min-height: 100%;
	max-height: 800rpx;

	.empty-pic {
		width: 500rpx;
		height: 416rpx;
		max-height: 450rpx;
	}
	
	.trip{
		font-size: 32rpx;
		color: $font-my-color-9;
	}
}
</style>
