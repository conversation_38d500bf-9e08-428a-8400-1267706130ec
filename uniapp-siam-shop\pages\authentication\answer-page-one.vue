<template>
	<view class="answer">
		<view class="imgpic-box">
			<image class="imgpic" src="./static/bj.png" mode="widthFix"></image>
			<text class="imgpic-box-title">考试状态：未考试</text>
		</view>
		<view class="answer-cont">
			<view class="answer-cont-title">考试规则</view>
			<view class="answer-cont-ls">
				<view class="answer-cont-ls-t">1、您需要完成接下来的学习，才能开始本次考试;</view>
				<view class="answer-cont-ls-t">2、需全部答对所有题目，才算合格;</view>
				<view class="answer-cont-ls-t">3、提交后不可更改答案;</view>
				<view class="answer-cont-ls-t">4、可多次参加考试。</view>
			</view>
		</view>
		<view class="footer-btn flex flex-align-center">
			<my-button width="300" @confirm="routePage" background="transparent" :border="true" color="#333">学习题库</my-button>
			<my-button style="flex: 1; min-width: 0rpx; margin-left: 30rpx;" @confirm="exm">正式考试</my-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			state: '',
			id: ''
		};
	},
	onLoad(e) {
		this.id = e.id;
		this.state = e.state;
	},
	methods: {
		routePage() {
			uni.navigateTo({
				url: '/pages/authentication/study?id=' + this.id
			});
		},
		exm() {
			uni.navigateTo({
				url: '/pages/authentication/examination?id=' + this.id
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.answer {
	.imgpic-box {
		text-align: center;
		padding-top: 150rpx;

		.imgpic {
			width: 210rpx;
			margin-left: 40rpx;
		}

		.imgpic-box-title {
			display: block;
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #333333;
			padding: 20rpx 0;
		}
	}

	.answer-cont {
		padding: 20rpx 32rpx;

		.answer-cont-title {
			font-size: 36rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #333333;
		}

		.answer-cont-ls {
		}

		.answer-cont-ls-t {
			padding: 20rpx 0;
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #333333;
		}
	}

	.footer-btn {
		position: fixed;
		bottom: 30rpx;
		left: 0rpx;
		right: 0rpx;
		padding: 0 32rpx;
	}
}
</style>
