<template>
	<view class="popup-box-all">
		<custom-mask :show="show" @click="show = false" :custom-style="{ width: '750rpx' }"></custom-mask>
		<view class="popup-box" :class="{ 'popup-box-zero': !show }">
			<view style="min-width: 670rpx;" :style="{ opacity: show ? 1 : 0, transition: show ? 'opacity 0.1s ease 0.3s' : 'none' }">
				<view class="popup-title">
					完成下面
					<text style="color: #fd6211;">{{ condition.length }}</text>
					步获取接单资格
				</view>
				<view class="cont-list flex flex-align-start justify-space-between">
					<image class="pic" src="../static/toux2x.png" mode="scaleToFill"></image>
					<view class="popup-cont">
						<view class="popup-title-name">1.实名认证</view>
						<text class="popup-title-name-tirp">为保证配送服务安全监督，请您先进行实名认证以及完善资料</text>
					</view>
					<navigator url="/pages/myInfo/shopInfo" v-if="stateNum == 0 || stateNum == null || stateNum == 3" hover-class="none">
						<button class="btn" type="default" :style="{ background: stateNum == 3 ? '#ff0000' : '#fd6211' }">{{ stateNum == 3 ? '已拒绝' : '去完成' }}</button>
					</navigator>
					<view class="btn-one" v-if=" stateNum == 1">待审核</view>
					<view class="btn-one" v-if="stateNum == 2" style="color: #19BE6B;">已通过</view>
				</view>
				<view class="cont-list flex flex-align-start justify-space-between" v-if="condition.indexOf('3') >= 0">
					<image class="pic" src="../static/jl2x.png" mode="scaleToFill"></image>
					<view class="popup-cont">
						<view class="popup-title-name">2.缴纳保证金</view>
						<text class="popup-title-name-tirp">同城新工考试</text>
					</view>
					<view class="btn-one" style="color: #999;" v-if="stateNum == 0 || stateNum == null || stateNum == 1 || stateNum == 3">去缴纳</view>
					<navigator url="/pages/authentication/answerOne" v-else><button class="btn" type="default">去完成</button></navigator>
				</view>
				<view class="cont-list flex flex-align-start justify-space-between" v-if="condition.indexOf('1') >= 0">
					<image class="pic" src="../static/guk2x.png" mode="scaleToFill"></image>
					<view class="popup-cont">
						<view class="popup-title-name">{{ condition.indexOf('3') >= 0 ? condition.indexOf('1') + 3 : condition.indexOf('1') + 2 }}.线下入职培训</view>
						<text class="popup-title-name-tirp">线下入职培训</text>
					</view>
					<view class="btn-one" style="color: #999;" v-if="stateNum == 0 || stateNum == null || stateNum == 1 || stateNum == 3">去报名</view>
					<navigator url="/pages/authentication/studyTrain" v-else-if="isTrain == '2'"><button class="btn" type="default">去完成</button></navigator>
					<view class="btn-one" v-else-if="isTrain == '1'" style="color: #19BE6B;">已通过</view>
				</view>
				<view class="cont-list flex flex-align-start justify-space-between" v-if="condition.indexOf('2') >= 0">
					<image class="pic" src="../static/jl2x.png" mode="scaleToFill"></image>
					<view class="popup-cont">
						<view class="popup-title-name">{{ condition.indexOf('3') >= 0 ? condition.indexOf('2') + 3 : condition.indexOf('2') + 2 }}.新手考试</view>
						<text class="popup-title-name-tirp">同城新工考试</text>
					</view>
					<view class="btn-one" style="color: #999;" v-if="stateNum == 0 || stateNum == null || stateNum == 1 || stateNum == 3">去考试</view>
					<navigator url="/pages/authentication/answerOne" v-else><button class="btn" type="default">去完成</button></navigator>
				</view>
			</view>
			<button class="closeBtn" type="default" @click.stop="show = false"><u-icon name="arrow-down" color="#3A3A3A" size="36"></u-icon></button>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import CustomMask from '@/components/custom-mask/custom-mask.vue';

export default {
	components: {
		CustomMask
	},
	data() {
		return {
			show: false,
			condition: [],
			conditionTwo: []
		};
	},
	computed: {
		...mapState(['UserInfo', 'configInfo']),
		stateNum() {
			return parseInt(this.UserInfo.auditStatus);
		},
		isTrain() {
			return this.UserInfo.isTrain;
		}
	},
	methods: {
		open(msg) {
			this.show = msg;
			this.condition = this.configInfo.condition.sort();
		}
	}
};
</script>

<style lang="scss" scoped>
.popup-box-all {
	.popup-box {
		height: 746rpx;
		position: absolute;
		right: 0rpx;
		bottom: 0rpx;
		background: #fff;
		width: 750rpx;
		border-radius: 30rpx 30rpx 0 0;
		transition: all 0.3s ease;
		z-index: 10071;
		padding: 40rpx;

		.popup-title {
			font-size: $font-my-size-40;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #000000;
			padding-bottom: 30rpx;
		}

		.cont-list {
			position: relative;
			padding: 30rpx 0;

			.pic {
				width: 70rpx;
				height: 70rpx;
				margin-right: 20rpx;
				margin-top: 10rpx;
			}

			.popup-cont {
				flex: 1;
				font-size: 22rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				padding-right: 50rpx;
				text-align: justify;

				&::after {
					position: absolute;
					left: 90rpx;
					right: 0rpx;
					bottom: 0rpx;
					display: block;
					content: '';
					height: 1rpx;
					background: #e4e4e4;
				}

				.popup-title-name {
					font-size: 30rpx;
					font-weight: bold;
					color: #333333;
				}

				.popup-title-name-tirp {
					font-size: $font-my-size-24;
					color: $font-my-color-6;
				}
			}

			.btn {
				height: 50rpx;
				line-height: 50rpx;
				background: #fd6211;
				border-radius: 50rpx;
				border: none;
				font-size: 22rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
			}

			.btn-one {
				height: 50rpx;
				line-height: 50rpx;
				color: #fd6211;
				padding: 0 20rpx;
			}
		}
		.closeBtn {
			position: absolute;
			padding: 0rpx;
			top: -80rpx;
			right: 20rpx;
			text-align: center;
			width: 64rpx;
			height: 64rpx;
			line-height: 64rpx;
			background: #ffffff;
			border-radius: 50%;
			border: none;
			outline: none;
			z-index: 10075;
		}
	}

	.popup-box-zero {
		height: 0rpx;
		bottom: 350rpx;
		right: 120rpx;
		width: 0rpx;
		border-radius: 50%;
		overflow: hidden;
		padding: 0rpx;
	}
}
</style>
