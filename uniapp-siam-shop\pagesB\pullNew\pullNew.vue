<template>
	<view class="pullNew">
		<my-nav-bar title="有奖推荐" >
			<view slot="left-cont">
				<my-icon size="30" style="margin-left: 20rpx;">&#xe6aa;</my-icon>
			</view>
		</my-nav-bar>
		<view class="pullNew-cont">
			<view class="pullNew-cont-title">
				<navigator url="/pagesB/pullNew/active-rule">
					<u-icon name="arrow-right" label="活动规则" label-pos="left" color="#fff" label-color="#fff" size="28"></u-icon>
				</navigator>
			</view>
			<view class="pullNew-cont-info">
				<text>每成功邀请一人</text>
				<view>
					<text>最高可得</text>
					<text style="color: #FFF7D6;">￥</text>
					<text style="font-size: 52rpx; color: #FFF7D6;">1088</text>
				</view>
			</view>
			<view class="pullNew-main-cont" style="padding: 40rpx 0;">
				<view class="pullNew-main-cont-ls">
					<view class="flex flex-align-center">
						<view class="pullNew-main-cont-title">
							<text>成功邀请(人)</text>
							<view class="title-num">0</view>
						</view>
						<view class="pullNew-main-cont-title">
							<text>已激活(人)</text>
							<view class="title-num">0</view>
						</view>
						<view class="pullNew-main-cont-title">
							<text>获奖(元)</text>
							<view class="title-num">0</view>
						</view>
					</view>
				</view>
				<my-line :margin="[20,0,0]"></my-line>
				<view class="shareEWM">分享二维码 好友帮你赚更多</view>
				<view class="EWM"><image class="ewm-pic" src="~@/static/image/ewm.png" mode="widthFix"></image></view>
				<view class="flex flex-align-center justify-space-between" style="width: 390rpx; margin: 50rpx auto 0rpx;">
					<view class="flex flex-direction-column flex-align-center">
						<my-icon color="#50B674" size="90">&#xe633;</my-icon>
						<text class="shar-text">微信</text>
					</view>
					<view class="flex flex-direction-column flex-align-center">
						<my-icon color="#FFBE2A" size="90">&#xe965;</my-icon>
						<text class="shar-text">朋友圈</text>
					</view>
				</view>
			</view>
			<view class="pullNew-main-cont" style="border-radius: 10rpx 10rpx 0 0;">
				<view class="flex justify-space-between flex-align-center" style="padding: 0 32rpx;">
					<view class="kinght-styl">骑士拉新排行榜</view>
					<view class="tab-list flex flex-align-center">
						<view class="tab-list-btn tab-list-btn-l" :class="{ 'tab-list-btn-bgColor': styleType === 1 }" @click="handClickChange(1)">日榜</view>
						<view class="tab-list-btn tab-list-btn-r" :class="{ 'tab-list-btn-bgColor': styleType === 2 }" @click="handClickChange(2)">月榜</view>
					</view>
				</view>
				<view class="ranking-list flex flex-align-end">
					<view class="ranking-box ranking-box-l">
						<image class="pic" src="@/pagesA/rankingList/static/2.png" mode="widthFix"></image>
						<view class="ranking-box-name">纪待*</view>
						<view class="money-num">￥47156</view>
						<view class="peopl-num">
							<my-icon size="28" color="#c4cdde">&#xe604;</my-icon>
							<text>68</text>
						</u-icon></view>
					</view>
					<view class="ranking-box ranking-box-c">
						<image class="pic-c" src="@/pagesA/rankingList/static/1.png" mode="widthFix"></image>
						<view class="ranking-box-name">郭*</view>
						<view class="money-num">￥70640</view>
						<view class="peopl-num peopl-num-c">
							<my-icon size="28" color="#e8cd8b">&#xe604;</my-icon>
							<text>68</text>
						</view>
					</view>
					<view class="ranking-box ranking-box-r">
						<image class="pic" src="@/pagesA/rankingList/static/3.png" mode="widthFix"></image>
						<view class="ranking-box-name">丁朵*</view>
						<view class="money-num">￥41978</view>
						<view class="peopl-num peopl-num-r">
							<my-icon size="28" color="#e8cd8b">&#xe604;</my-icon>
							<text>68</text>
						</view>
					</view>
				</view>
				<view class="ranking-list-box">
					<u-row class="ranking-list-box-ls">
						<u-col span="2"><view class="ls-styl">名次</view></u-col>
						<u-col span="3"><view class="ls-styl">姓名</view></u-col>
						<u-col span="4"><view class="ls-styl">成功邀请(人)</view></u-col>
						<u-col span="3"><view class="ls-styl">奖励(元)</view></u-col>
					</u-row>
					<u-row class="ranking-list-box-ls" v-for="(item, index) of list" :key="index">
						<u-col span="2">
							<view class="ls-styl-cont">{{ index }}</view>
						</u-col>
						<u-col span="3">
							<view class="ls-styl-cont">常维*</view>
						</u-col>
						<u-col span="4">
							<view class="ls-styl-cont">72</view>
						</u-col>
						<u-col span="3">
							<view class="ls-styl-cont">39746</view>
						</u-col>
					</u-row>
				</view>
				<view class="time-static">总榜统计时间：2020.01.01-2020.10.27</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			styleType: 1,
			list: new Array(20)
		};
	},
	methods: {
		handClickChange(msg) {
			this.styleType = msg;
		}
	}
};
</script>

<style lang="scss" scoped>
.pullNew {
	background: #e94043;

	.pullNew-cont {
		padding: 40rpx 32rpx 0;
		// background: url(../static/image/<EMAIL>) no-repeat;
		// background-size: 100% auto;
		// background-position: top left;

		.pullNew-cont-title {
			text-align: right;
		}

		.pullNew-cont-info {
			padding: 38rpx 0rpx 70rpx;
			font-size: 42rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #ffffff;
			font-style: italic;
		}

		.pullNew-main-cont {
			padding: 46rpx 0rpx;
			background: #ffffff;
			border-radius: 10rpx;
			margin-bottom: 32rpx;

			&:last-child {
				margin-bottom: 0rpx;
			}

			.pullNew-main-cont-title {
				flex: 1;
				text-align: center;
				font-size: $font-my-size-26;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #666666;

				.title-num {
					height: 88rpx;
					line-height: 88rpx;
					font-size: 44rpx;
					font-weight: bold;
					color: #333333;
				}
			}

			.pullNew-main-cont-ls {
			}

			.shareEWM {
				text-align: center;
				padding: 40rpx 0 40rpx;
				font-size: $font-my-size-36;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333333;
			}

			.EWM {
				width: 392rpx;
				height: 392rpx;
				border: 1px solid #eeeeee;
				padding: 30rpx;
				margin: 0 auto;
				border-radius: 10rpx;

				.ewm-pic {
					width: 100%;
					height: 100%;
					border-radius: 9rpx;
				}
			}
			
			.shar-text{
				color: $font-my-color-9;
			}

			.kinght-styl {
				font-size: 36rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333333;
			}

			.tab-list {
				width: 262rpx;

				.tab-list-btn {
					flex: 1;
					height: 58rpx;
					line-height: 54rpx;
					text-align: center;
					font-size: 26rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #e94043;
					border: 1rpx solid #e94043;
				}

				.tab-list-btn-l {
					border-right: none;
					border-radius: 29rpx 0rpx 0rpx 29rpx;
				}

				.tab-list-btn-r {
					border-left: none;
					border-radius: 0rpx 29rpx 29rpx 0rpx;
				}

				.tab-list-btn-bgColor {
					background: #e94043;
					color: #fff;
				}
			}

			.ranking-list {
				padding: 72rpx 32rpx 0;

				.ranking-box {
					flex: 1;
					height: 354rpx;
					border: 2rpx solid;
					border-radius: 10rpx;
					text-align: center;
					padding: 45rpx;
					font-family: Source Han Sans CN;

					.pic {
						width: 86rpx;
					}

					.pic-c {
						width: 164rpx;
					}

					.ranking-box-name {
						font-size: 32rpx;
						font-weight: 400;
						color: #333333;
					}

					.money-num {
						font-size: 24rpx;
						font-weight: bold;
						color: #333333;
						padding: 10rpx 0;
					}

					.peopl-num {
						border: 1rpx solid #c4cdde;
						border-radius: 6rpx;
						color: #c4cdde;
						font-size: $font-my-size-26;
					}

					.peopl-num-c {
						border-color: #e8cd8b;
						color: #e8cd8b;
					}

					.peopl-num-r {
						border-color: #f2b58c;
						color: #f2b58c;
					}
				}

				.ranking-box-l {
					background: #f6f9ff;
					border-color: #d9e4f9;
				}
				.ranking-box-c {
					height: 404rpx;
					margin: 0 -10rpx;
					background: #fef6e1;
					border-color: #efdcb1;
					transform: translateZ(1rpx);
				}
				.ranking-box-r {
					background: #fff5ec;
					border-color: #fbe4d3;
				}
			}

			.ranking-list-box {
				padding: 60rpx 0rpx 0rpx;

				.ranking-list-box-ls {
					padding: 30rpx 32rpx;

					&:nth-child(2n) {
						background: #fafafa;
					}

					.ls-styl {
						font-size: 26rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: #999999;
					}

					.ls-styl-cont {
						font-size: $font-my-size-34;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: $font-my-color-6;
					}
				}
			}

			.time-static {
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #999999;
				text-align: center;
				padding: 40rpx 0 0rpx;
			}
		}
	}
}
</style>
