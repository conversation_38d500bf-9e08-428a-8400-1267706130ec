import Vue from 'vue'
import App from './App'
import store from "store"

import validate from "./utils/validate/index.js" // 全局验证方法
import request from "@/common/method/request.js" // 请求封装
import common from "@/common/index.js" // 全局公用法法
import interactive from "./utils/interactive/index.js" // 全局交互方法
import siteinfo from "@/common/siteinfo.js" // 站点信息

import moment from "moment";
Vue.prototype.$moment = moment;

import uView from 'uview-ui'; // 全局组件注册 引入全局uView
Vue.use(uView);

import UniStatusBar from '@/components/uni-status-bar/uni-status-bar.vue'
import myButton from '@/components/my-button.vue'
import MyIcon from '@/components/my-icon/iconfont.vue'
import MyNavBar from "@/components/my-nav-bar.vue"
import MyLine from "@/components/my-line.vue"
import CustomTabs from '@/components/custom-tabs/custom-tabs.vue'
import MescrollBody from "@/components/mescroll-uni/mescroll-body.vue"
import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue"
import CustomEmpty from "@/components/custom-empty.vue"
Vue.component('mescroll-body', MescrollBody)
Vue.component('mescroll-uni', MescrollUni)
Vue.component('UniStatusBar', UniStatusBar)
Vue.component('myButton', myButton)
Vue.component('MyIcon', MyIcon)
Vue.component('MyNavBar', MyNavBar)
Vue.component('MyLine', MyLine)
Vue.component('CustomTabs', CustomTabs)
Vue.component('CustomEmpty', CustomEmpty)
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
Vue.mixin(MescrollMixin)

// 公用方法
Vue.prototype.$validate = validate;
Vue.prototype.$request = request;
Vue.prototype.$interactive = interactive;
Vue.prototype.$common = common;
Vue.prototype.$imageBaseUrl = siteinfo.picRoot; // 添加全局图片基础URL

Vue.config.productionTip = false;

App.mpType = 'app'

const app = new Vue({
	store,
	...App
})

app.$mount()
