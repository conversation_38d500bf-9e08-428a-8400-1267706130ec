<template>
  <view class="perfectInfo">
    <uni-status-bar></uni-status-bar>
    <u-form :model="formData" ref="uForm" :error-type="['message']" :label-width="150">
      <!-- 状态选择器 -->
      <u-select v-model="statusSelectShow" mode="single-column" :list="statusSelectList" @confirm="onStatusConfirm"></u-select>
      <view class="phone-box">
        <!-- 规则名称 -->
        <u-form-item label="规则名称" prop="name" :required="true">
          <uni-easyinput class="phone-input" :inputBorder="false" v-model="formData.name"
            placeholder="请输入规则名称"></uni-easyinput>
        </u-form-item>

        <!-- 满足价格 -->
        <u-form-item label="满足价格" prop="limitedPrice" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.limitedPrice"
            placeholder="请输入满足价格"></uni-easyinput>
        </u-form-item>

        <!-- 减价额度 -->
        <u-form-item label="减价额度" prop="reducedPrice" :required="true">
          <uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="formData.reducedPrice"
            placeholder="请输入减价额度"></uni-easyinput>
        </u-form-item>

        <!-- 活动状态 -->
        <u-form-item label="活动状态" prop="status" :required="true">
          <view class="phone-input flex flex-align-center justify-space-between" @click="showStatusSelect">
            <text>{{ getStatusText(formData.status) }}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
        </u-form-item>
      </view>
    </u-form>

    <my-button margin-top="60" :bold="true" color="#fff" font-size="32" @confirm="saveRule">保存</my-button>
  </view>
</template>

<script>
export default {
  data () {
    return {
      isEdit: false,
      ruleId: null,
      formData: {
        name: '',
        limitedPrice: '',
        reducedPrice: '',
        status: 1
      },
      // u-select相关数据
      statusSelectShow: false,
      statusSelectList: [],
      statusList: [
        { name: '开启', value: 1 },
        { name: '关闭', value: 2 }
      ],
      // 表单验证规则
      rules: {
        name: [
          {
            required: true,
            message: '请输入规则名称',
            trigger: ['change', 'blur']
          },
          {
            min: 1,
            max: 50,
            message: '规则名称长度在1到50个字符',
            trigger: ['change', 'blur']
          }
        ],
        limitedPrice: [
          {
            required: true,
            message: '请输入满足金额',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return !!value;
            }
          },
          {
            type: 'number',
            message: '满足金额必须为数字',
            trigger: ['change', 'blur'],
            transform(value) {
              return Number(value);
            }
          },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                return false;
              }
              return true;
            },
            message: '满足金额必须大于0',
            trigger: ['change', 'blur']
          }
        ],
        reducedPrice: [
          {
            required: true,
            message: '请输入减价额度',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return !!value;
            }
          },
          {
            type: 'number',
            message: '减价额度必须为数字',
            trigger: ['change', 'blur'],
            transform(value) {
              return Number(value);
            }
          },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                return false;
              }
              return true;
            },
            message: '减价额度必须大于0',
            trigger: ['change', 'blur']
          }
        ],
        status: [
          {
            required: true,
            message: '请选择活动状态',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return !!value;
            }
          }
        ]
      }
    }
  },
  onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
  onLoad (options) {
    if (options.data) {
      try {
        const data = JSON.parse(options.data)
        this.isEdit = true;
        this.ruleId = data.id;
        // 只合并formData中已有的字段
        this.formData = {
          ...data
        }
      } catch (error) {
        
      }
      
      // this.loadRuleDetail();
    }
    // 准备状态选择器数据
    this.statusSelectList = this.statusList.map(item => ({
      label: item.name,
      value: item.value
    }));
  },
  methods: {
    // u-select显示方法
    showStatusSelect() {
      // 准备状态选择器数据
      this.statusSelectList = this.statusList.map(item => ({
        label: item.name,
        value: item.value
      }));
      this.statusSelectShow = true;
    },
    
    // u-select确认方法
    onStatusConfirm(e) {
      const item = e[0];
      if (item) {
        this.formData.status = item.value;
      }
      this.statusSelectShow = false;
    },
    
    // 获取状态文本
    getStatusText (status) {
      const statusMap = {
        1: '启用',
        2: '关闭'
      };
      return statusMap[status] || '请选择';
    },

    // 加载规则详情
    async loadRuleDetail () {
      try {
        const res = await this.$request({
          url: `/api-promotion/rest/merchant/fullReductionRule/getById`,
          method: 'POST',
          data: {
            id: this.ruleId
          }
        });
        if (res.code === 200) {
          // 只合并formData中已有的字段
          this.formData = {
            name: res.data.name || '',
            limitedPrice: res.data.limitedPrice || '',
            reducedPrice: res.data.reducedPrice || '',
            status: res.data.status || 1
          };
        }
      } catch (error) {
        console.error('加载规则详情失败:', error);
        uni.showToast({ title: '加载规则详情失败', icon: 'none' });
      }
    },

    // 保存规则
    async saveRule () {
      // 使用u-form进行表单验证
      try {
        const valid = await this.$refs.uForm.validate();
        if (!valid) {
          return;
        }
        
      } catch (error) {
        console.error('表单验证失败:', error);
        return;
      }

      uni.showLoading({ title: '保存中...' });
      try {
        const url = this.isEdit
          ? `/api-promotion/rest/merchant/fullReductionRule/update`
          : '/api-promotion/rest/merchant/fullReductionRule/insert';

        const res = await this.$request({
          url,
          method: this.isEdit ? 'PUT' : 'POST',
          data: this.formData
        });

        if (res.code === 200) {
          uni.showToast({ title: '保存成功', icon: 'success' });
          // 设置全局标记，用于返回列表页时刷新数据
          uni.setStorageSync('ruleListNeedRefresh', true);
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({ title: res.message || '保存失败', icon: 'none' });
        }
      } catch (error) {
        console.error('保存规则失败:', error);
        uni.showToast({ title: '保存失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.perfectInfo {
  padding: 0 32rpx;

  .phone-box {
    padding-top: 20rpx;
  }

  // u-form-item 样式调整
  /deep/ .u-form-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid $uni-bg-color-grey;
    
    .u-form-item__body {
      display: flex;
      align-items: center;
    }
    
    .u-form-item__body__left__content__label {
      font-size: $font-my-size-32;
      color: $font-my-color-3;
      min-width: 150rpx;
    }
  }
}

.phone-input {
  flex: 1;
  font-size: $font-my-size-32;
  color: $font-my-color-3;
}

.flex {
  display: flex;
}

.flex-align-center {
  align-items: center;
}

.justify-space-between {
  justify-content: space-between;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.addHeadPic {
  width: 120rpx;
  height: 120rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.addHeadPic-icon {
  font-size: 40rpx;
  color: #999;
}
</style>