<template>
	<div class="mymap-box">
		<uni-status-bar></uni-status-bar>
		<div class="search-box">
			<div class="postion">
				<uni-icons type="location-filled" color="#666" size="28"></uni-icons>
				<text class="postion-text">{{ address.city ? address.city : '定位中' }}</text>
			</div>
			<div class="search"><input class="search-label" type="text" v-model="key" placeholder="请输入地理名称" /></div>
			<button class="search-btn" size="mini" type="primary" @click="handClickSeach">搜索</button>
		</div>
		<map v-if="mapshow" id="mymap" class="mymap" ref="mymap" :show-location="true" :scale="scale" :markers="markers" @tap="handClickAmp"></map>
		<list class="footer-list-box" v-if="type == 1">
			<template v-if="poiList.length">
				<cell class="footer-list-cont" v-for="(item, index) of poiList" :key="index" @click="handClickChange(item)">
					<uni-icons type="location-filled" color="#2a75ed" size="36"></uni-icons>
					<div class="footer-list-cont-l">
						<text class="footer-list-cont-l-text">{{ item.name }}</text>
						<text class="footer-list-cont-l-bottom">{{ item.address }}</text>
					</div>
				</cell>
			</template>
		</list>
	</div>
</template>

<script>
import { mapState } from 'vuex';
var myMap, mapseach;

export default {
	data() {
		return {
			poiList: [],
			mapshow: true,
			key: '',
			city: '',
			scale: 16,
			formData: {
				lat: null,
				lng: null,
				positionName: ''
			},
			amapPlugin: null,
			markers: [],
			type: '1'
		};
	},
	onLoad(e) {
		this.type = e.type || 1;
		myMap = uni.createMapContext('mymap');
		mapseach = new plus.maps.Search('mymap');
		this.handClickSeach();
	},
	onShow() {
		uni.$emit('RefreshAddressInfo');
	},
	computed: {
		...mapState(['address'])
	},
	methods: {
		// 点击地图
		handClickAmp(msg) {
			const point = new plus.maps.Point(msg.detail.longitude, msg.detail.latitude);
			plus.maps.Map.reverseGeocode(point, { city: '武汉', coordType: 'wgs84' }, event => {
				this.key = event.address;
				this.handClickSeach();
			});
		},
		// 位置检索
		handClickSeach() {
			// mapseach.poiSearchNearBy(this.address.city, this.key ? this.key : this.address.address);
			const cent = new plus.maps.Point(this.address.lng, this.address.lat);
			mapseach.poiSearchNearBy(this.key ? this.key : this.address.address, cent, 500000, 1);
			mapseach.onPoiSearchComplete = (state, result) => {
				if (state === 0) {
					this.poiList = result.poiList;
					myMap.moveToLocation({
						longitude: this.poiList[0].point.longitude,
						latitude: this.poiList[0].point.latitude
					});
					myMap.translateMarker({
						markerId: 1,
						autoRotate: true,
						duration: 300,
						destination: {
							longitude: this.poiList[0].point.longitude,
							latitude: this.poiList[0].point.latitude
						}
					});
				}
			};
		},
		// 位置选中
		handClickChange(msg) {
			uni.$emit('getAddressInfo', msg);
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>

<style>
.mymap-box {
	flex: 1;
	width: 750rpx;
	background-color: #ffffff;
}

.search-box {
	width: 750rpx;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	margin-top: 66rpx;
}

.postion {
	width: 150rpx;
	height: 66rpx;
	flex-direction: row;
	align-items: center;
	justify-content: flex-start;
	text-align: right;
	margin-top: 12rpx;
}

.postion-text {
	font-size: 30rpx;
	margin-bottom: 10rpx;
	color: #666;
}

.search {
	width: 400rpx;
	border-width: 1rpx;
	border-radius: 50rpx;
	justify-content: center;
	height: 66rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.search-btn {
	flex: 1;
	margin-left: 20rpx;
	border-radius: 50rpx;
	height: 66rpx !important;
}

.search-label {
	font-size: 28rpx;
	text-overflow: ellipsis;
	overflow: hidden;
}

.mymap {
	flex: 1;
	width: 750rpx;
	height: 1000rpx;
}

.footer-list-box {
	height: 700rpx;
}

.footer-list-cont {
	padding: 20rpx 10rpx;
	flex-direction: row;
	justify-content: space-between;
	align-items: flex-end;
	border-bottom-width: 1rpx;
	border-bottom-color: #999;
}

.footer-list-cont-l {
	flex: 1;
	margin-left: 20rpx;
}

.footer-list-cont-l-text {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
}

.footer-list-cont-l-bottom {
	font-size: 28rpx;
	color: #999;
	padding: 10rpx 0;
}

.distance {
	font-size: 28rpx;
	color: #999;
}
</style>
