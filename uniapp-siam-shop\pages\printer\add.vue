<template>
	<view class="content">
		<view class="form-container">
			<view class="form-item">
				<view class="form-label">打印机名称</view>
				<input class="form-input" v-model="printerForm.name" placeholder="请输入打印机名称" />
			</view>
			
			<view class="form-item" @click="showBrandPicker">
				<view class="form-label">品牌</view>
				<view class="form-input picker-input">
					<text v-if="!printerForm.brand" class="placeholder">请选择打印机品牌</text>
					<text v-else>{{ getBrandName(printerForm.brand) }}</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
			
			<view class="form-item">
				<view class="form-label">编号</view>
				<input class="form-input" v-model="printerForm.number" placeholder="请输入打印机编号" />
			</view>
			
			<view class="form-item">
				<view class="form-label">识别码</view>
				<input class="form-input" v-model="printerForm.identifyingCode" placeholder="请输入打印机识别码" />
			</view>
			
			<view class="form-item">
				<view class="form-label">打印机类型</view>
				<view class="type-display">{{ printerType === 1 ? '小票打印机' : '标签打印机' }}</view>
			</view>
			
			<view class="form-item">
				<view class="form-label">自动打印</view>
				<radio-group class="radio-group" @change="autoPrintChange">
					<label class="radio">
						<radio value="1" :checked="printerForm.isAutoPrint" />是
					</label>
					<label class="radio">
						<radio value="0" :checked="printerForm.isAutoPrint" />否
					</label>
				</radio-group>
			</view>
		</view>
		
		<view class="submit-button">
			<button type="primary" @click="savePrinter">保存</button>
		</view>
		
		<!-- 品牌选择弹窗 -->
		<uni-popup ref="brandPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择打印机品牌</text>
					<view class="popup-close" @click="closeBrandPicker">
						<uni-icons type="closeempty" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="brand-list">
					<view 
						class="brand-item" 
						v-for="brand in brandList" 
						:key="brand.value" 
						@click="selectBrand(brand)"
					>
						<text class="brand-name">{{ brand.label }}</text>
						<uni-icons 
							v-if="printerForm.brand == brand.value" 
							type="checkmarkempty" 
							size="20" 
							color="#fa7c25"
						></uni-icons>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				printerForm: {
					name: '',
					number: '',
					identifyingCode: '',
					isAutoPrint: 1,
					brand: '',
					status: 1,
					type: 1 // 1: 小票打印机, 2: 标签打印机
				},
				printerType: 1,
				brandList: [
					{ value: '1', label: '飞鹅' },
					{ value: '2', label: '芯烨云' },
					{ value: '3', label: '大趋智能' },
					{ value: '4', label: '商鹏' }
				]
			};
		},
		onLoad(options) {
			if (options.type) {
				this.printerType = parseInt(options.type);
				this.printerForm.type = this.printerType;
			}
		},
		methods: {
			autoPrintChange(e) {
				this.printerForm.isAutoPrint = e.detail.value == 1;
			},
			
			statusChange(e) {
				this.printerForm.status = parseInt(e.detail.value);
			},
			
			showBrandPicker() {
				this.$refs.brandPopup.open();
			},
			
			closeBrandPicker() {
				this.$refs.brandPopup.close();
			},
			
			selectBrand(brand) {
				this.printerForm.brand = brand.value;
				this.closeBrandPicker();
			},
			
			getBrandName(value) {
				const brand = this.brandList.find(item => item.value == value);
				return brand ? brand.label : '';
			},
			
			savePrinter() {
				if (!this.printerForm.name) {
					uni.showToast({
						title: '请输入打印机名称',
						icon: 'none'
					});
					return;
				}
				
				if (!this.printerForm.brand) {
					uni.showToast({
						title: '请选择打印机品牌',
						icon: 'none'
					});
					return;
				}
				
				if (!this.printerForm.number) {
					uni.showToast({
						title: '请输入打印机编号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.printerForm.identifyingCode) {
					uni.showToast({
						title: '请输入打印机识别码',
						icon: 'none'
					});
					return;
				}
				
				this.$request({
					url: '/api-util/rest/merchant/printer/insert',
					method: 'POST',
					data: this.printerForm
				}).then(res => {
					if (res.code === 200) {
						uni.showToast({
							title: '添加成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.$emit('RefreshPrinterList')
							uni.navigateBack();
						}, 1000);
					} else {
						uni.showToast({
							title: res.message || '添加失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '网络请求异常',
						icon: 'none'
					});
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.content {
		padding: 20rpx;
	}
	
	.form-container {
		background: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		
		.form-item {
			margin-bottom: 30rpx;
			
			.form-label {
				font-size: 28rpx;
				margin-bottom: 15rpx;
				color: #333;
			}
			
			.form-input {
				border: 1rpx solid #ddd;
				border-radius: 10rpx;
				padding: 20rpx;
				font-size: 28rpx;
				
				&.picker-input {
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.placeholder {
						color: #999;
					}
				}
			}
			
			.type-display {
				font-size: 28rpx;
				padding: 20rpx;
				border: 1rpx solid #ddd;
				border-radius: 10rpx;
				color: #666;
			}
			
			.radio-group {
				display: flex;
				
				.radio {
					margin-right: 30rpx;
					display: flex;
					align-items: center;
				}
			}
		}
	}
	
	.submit-button {
		padding: 40rpx 20rpx;
		text-align: center;
	}
	
	.popup-content {
		background-color: #fff;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		padding: 30rpx;
		
		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.popup-title {
				font-size: 32rpx;
				font-weight: bold;
			}
			
			.popup-close {
				padding: 10rpx;
			}
		}
		
		.brand-list {
			max-height: 500rpx;
			overflow-y: auto;
			
			.brand-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx 0;
				border-bottom: 1rpx solid #eee;
				
				&:last-child {
					border-bottom: none;
				}
				
				.brand-name {
					font-size: 28rpx;
					color: #333;
				}
			}
		}
	}
</style>