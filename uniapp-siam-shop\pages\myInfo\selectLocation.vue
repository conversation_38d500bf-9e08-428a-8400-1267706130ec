<template>
	<view class="container">
		<web-view :src="webViewUrl" @onPostMessage="handleMessage" @message="handleMessage"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				webViewUrl: ''
			}
		},
		onLoad() {
			// 构建百度地图页面的URL，添加时间戳确保页面刷新
			this.webViewUrl = '/hybrid/html/baidumap.html?t=' + new Date().getTime();
		},
		methods: {
			handleMessage(event) {
				// 接收来自web-view的消息
				console.log('Received message from webview:', event);
				
				// 兼容不同格式的消息
				let data = null;
				if (event.detail && event.detail.data) {
					// 微信小程序格式
					data = event.detail.data[0] || event.detail.data;
				} else if (event.detail) {
					// 其他格式
					data = event.detail;
				} else {
					// H5格式
					data = event.data;
				}
				
				console.log('Parsed data:', data);

				if (data && data.type === 'selectedLocation') {
					// 将选中的位置信息返回给上一个页面
					uni.$emit('locationSelected', data.location);
					uni.navigateBack();
				}
			}
		}
	}
</script>

<style scoped>
	.container {
		width: 100%;
		height: 100vh;
	}
</style>