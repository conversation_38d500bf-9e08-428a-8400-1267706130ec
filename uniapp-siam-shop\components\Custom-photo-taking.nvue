<template>
	<view class="Custom-photo-taking">
		<KJ-Camera v-if="show" ref="Camera" class="KJ-Camera" :style="{ width: '100%', height: cameraHeight }"></KJ-Camera>
		<cover-view class="Custom-photo-taking-box">123123123123</cover-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			show: false,
			cameraHeight: 'auto'
		};
	},
	onLoad() {
		setTimeout(() => {
			this.show = true;
			this.$nextTick(() => {
				this.$refs.Camera.initCamera();
			});
		}, 500);
		// this.openPhoto();
	},
	onUnload() {
		
	},
	methods: {
		openPhoto() {
			var _this = this;
			var dic = {
				savePath: plus.io.convertLocalFileSystemURL('_doc/KJ-Camera') //保存图片位置，一定要是_doc绝对路径
			};
			this.$refs.Camera.captureImage(
				dic,
				res => {
					console.log('filePath: ' + res);
					//_this.src = "file://" + res.filePath;
					uni.saveImageToPhotosAlbum({
						filePath: plus.io.convertAbsoluteFileSystem(res.filePath),
						success: function() {
							console.log('save success');
						},
						fail: function(e) {
							console.log(JSON.stringify(e));
						}
					});
				},
				res => {
					//返回base64字符串，比filePath回调快
					_this.src = '' + res.base64;
					console.log('base64: ' + res);
				}
			);
		},
		videoSuccess() {}
	}
};
</script>

<style scoped>
.Custom-photo-taking {
	width: 750rpx;
	align-items: center;
	justify-content: center;
	flex: 1;
}

.KJ-Camera {
	width: 750rpx;
	position: fixed;
	top: 0rpx;
	bottom: 0rpx;
	left: 0rpx;
	right: 0rpx;
}

.Custom-photo-taking-box{
	width: 600rpx;
	height: 500rpx;
	background-color: pink;
}
</style>
