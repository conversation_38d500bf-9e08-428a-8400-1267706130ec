<template>
	<scroll-view class="scroll-Y" :scroll-top="0" scroll-y="true">
		<view class="histoy-activit">
			<view class="haveInHand-cont">
				<navigator url="/pagesB/activitCenter/active-details-punching">
					<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
						<view class="title-name">
							<text>首单奖</text>
							<view class="active-state">已结束</view>
						</view>
						<view class="active-price">0元</view>
					</view>
				</navigator>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<view class="haveInHand-cont-title flex flex-align-center justify-space-between">
					<view class="title-name">
						<text>首单奖</text>
						<view class="active-state">已结束</view>
					</view>
					<view class="active-price">0元</view>
				</view>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
		</view>
	</scroll-view>
</template>

<script></script>

<style lang="scss" scoped>
.scroll-Y {
	height: 100%;
}
.histoy-activit {
	padding: 20rpx;
	min-height: 100%;

	.haveInHand-cont {
		padding: 28rpx;
		background: #ffffff;
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0rpx;
		}

		.haveInHand-cont-title {
			padding: 20rpx 0;

			.title-name {
				font-size: 40rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #4a4a4a;
			}

			.active-state {
				display: inline-block;
				padding: 5rpx 10rpx;
				margin: 0 0 3rpx 10rpx;
				vertical-align: middle;
				border: 1rpx solid #c8c8c8;
				border-radius: 5rpx;
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #999999;
			}
		}

		.active-price {
			font-size: 40rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #4a4a4a;
		}

		.haveInHand-time {
			height: 80rpx;
			line-height: 80rpx;
			font-size: 24rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
		}

		.haveInHand-cont-trip {
			font-size: 24rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #666666;
			margin-top: 10rpx;

			&::before {
				content: '';
				display: block;
				height: 1rpx;
				background: #eeeeee;
				margin-bottom: 30rpx;
			}
		}
	}
}
</style>
