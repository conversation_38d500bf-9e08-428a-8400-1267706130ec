<template>
	<view class="category-container">
		<!-- 分类列表 -->
		<view class="category-list">
			<mescroll-uni 
				ref="mescrollRef" 
				@init="mescrollInit" 
				@down="onRefresh" 
				@up="onLoadMore"
				:down="{use:true}"
				:up="{use:false, empty: {tip: '暂无商品数据' }}"
				:fixed="false"
			>
				<view class="category-item" v-for="(item, index) in categoryList" :key="item.id">
					<view class="item-content">
						<view class="item-info">
							<text class="item-name">{{ item.name }}</text>
							<text class="item-sort">排序: {{ item.sortNumber }}</text>
						</view>
						<view class="item-actions">
							<button class="action-btn edit-btn" @click="editCategory(item)">编辑</button>
							<button class="action-btn delete-btn" @click="deleteCategory(item.id)">删除</button>
						</view>
					</view>
				</view>
			</mescroll-uni>
		</view>

		<!-- 操作按钮 -->
		<view class="add-button-container">
			<button class="add-button" @click="showAddModal">新增分类</button>
		</view>

		<!-- 新增/编辑弹窗 -->
		<view class="modal" v-if="showModal">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">{{ modalTitle }}</text>
				</view>
				<view class="modal-body">
					<input class="input-item" v-model="currentCategory.name" placeholder="请输入分类名称" />
					<input class="input-item" v-model="currentCategory.sort" type="number" placeholder="请输入排序值" />
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeModal">取消</button>
					<button class="modal-btn confirm-btn" @click="saveCategory">{{ modalConfirmText }}</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				categoryList: [],
				pageNo: 1,
				pageSize: 20,
				total: 0,
				showModal: false,
				modalType: 'add', // add 或 edit
				currentCategory: {
					id: null,
					name: '',
					sort: ''
				},
				mescroll: null // 添加mescroll实例
			}
		},
		computed: {
			modalTitle() {
				return this.modalType === 'add' ? '新增分类' : '编辑分类'
			},
			modalConfirmText() {
				return this.modalType === 'add' ? '新增' : '保存'
			}
		},
		onLoad() {
			this.loadCategoryList()
		},
		methods: {
			// 加载分类列表
			async loadCategoryList(pageNo = 1) {
				try {
					const res = await this.$request({
						url: '/api-goods/rest/merchant/menu/list',
						method: 'POST',
						data: {
							pageNo: pageNo,
							pageSize: this.pageSize
						}
					})
					
					if (res.code === 200) {
						if (this.pageNo === 1) {
							this.categoryList = res.data.records
						} else {
							this.categoryList = [...this.categoryList, ...res.data.records]
						}
            			this.mescroll.endSuccess(res.data.total, res.data.total >= 20 ? true : true);
						this.total = res.data.total
					} else {
            			this.mescroll.endSuccess(0, false);
						uni.showToast({
							title: res.msg || '加载失败',
							icon: 'none'
						})
					}
				} catch (error) {
					this.mescroll.endSuccess(0, false);
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					})
				} finally {
				}
			},
			
			// mescroll初始化完成的回调
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			
			// 下拉刷新
			onRefresh() {
      			this.loadCategoryList()
			},
			
			// 上拉加载更多
			onLoadMore(e) {
				this.loadCategoryList(e.num)
			},
			
			// 显示新增弹窗
			showAddModal() {
				this.modalType = 'add'
				this.currentCategory = {
					id: null,
					name: '',
					sort: ''
				}
				this.showModal = true
			},
			
			// 编辑分类
			editCategory(item) {
				this.modalType = 'edit'
				this.currentCategory = {
					id: item.id,
					name: item.name,
					sort: item.sortNumber
				}
				this.showModal = true
			},
			
			// 关闭弹窗
			closeModal() {
				this.showModal = false
			},
			
			// 保存分类（新增或编辑）
			async saveCategory() {
				if (!this.currentCategory.name) {
					uni.showToast({
						title: '请输入分类名称',
						icon: 'none'
					})
					return
				}
				
				if (!this.currentCategory.sort && this.currentCategory.sort !== 0) {
					uni.showToast({
						title: '请输入排序值',
						icon: 'none'
					})
					return
				}
				
				try {
					let res
					if (this.modalType === 'add') {
						// 新增分类
						res = await this.$request({
							url: '/api-goods/rest/merchant/menu/insert',
							method: 'POST',
							data: {
								name: this.currentCategory.name,
								sortNumber: parseInt(this.currentCategory.sort)
							}
						})
					} else {
						// 编辑分类
						res = await this.$request({
							url: '/api-goods/rest/merchant/menu/update',
							method: 'PUT',
							data: {
								id: this.currentCategory.id,
								name: this.currentCategory.name,
								sortNumber: parseInt(this.currentCategory.sort)
							}
						})
					}
					
					if (res.code === 200) {
						uni.showToast({
							title: this.modalType === 'add' ? '新增成功' : '编辑成功',
							icon: 'success'
						})
						this.closeModal()
						// 刷新列表
						this.onRefresh(); // 重置列表并重新加载
					} else {
						uni.showToast({
							title: res.msg || (this.modalType === 'add' ? '新增失败' : '编辑失败'),
							icon: 'none'
						})
					}
				} catch (error) {
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					})
				}
			},
			
			// 删除分类
			deleteCategory(id) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该分类吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								const res = await this.$request({
									url: '/api-goods/rest/merchant/menu/delete',
									method: 'DELETE',
									data: {
										ids: [id]
									}
								})
								
								if (res.code === 200) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									})
									// 刷新列表
									this.onRefresh(); // 重置列表并重新加载
								} else {
									uni.showToast({
										title: res.msg || '删除失败',
										icon: 'none'
									})
								}
							} catch (error) {
								uni.showToast({
									title: '请求失败',
									icon: 'none'
								})
							}
						}
					}
				})
			}
		}
	}
</script>

<style scoped>
	.category-container {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #f5f5f5;
	}

	.category-list {
		flex: 1;
		padding: 20rpx;
	}

	.scroll-y {
		height: 100%;
	}

	.category-item {
		background-color: #ffffff;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.item-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.item-info {
		flex: 1;
	}

	.item-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		display: block;
		margin-bottom: 10rpx;
	}

	.item-sort {
		font-size: 26rpx;
		color: #999999;
	}

	.item-actions {
		display: flex;
	}

	.action-btn {
		font-size: 24rpx;
		padding: 10rpx 20rpx;
		margin-left: 20rpx;
		border-radius: 6rpx;
	}

	.edit-btn {
		background-color: #007AFF;
		color: #ffffff;
	}

	.delete-btn {
		background-color: #FF3B30;
		color: #ffffff;
	}

	.loading-status {
		text-align: center;
		padding: 20rpx 0;
		font-size: 26rpx;
		color: #999999;
	}

	.add-button-container {
		padding: 20rpx;
		background-color: #ffffff;
		border-top: 1rpx solid #e5e5e5;
	}

	.add-button {
		width: 100%;
		background-color: #007AFF;
		color: #ffffff;
		font-size: 32rpx;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 10rpx;
	}

	.modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	.modal-content {
		background-color: #ffffff;
		width: 80%;
		border-radius: 10rpx;
		overflow: hidden;
	}

	.modal-header {
		padding: 30rpx;
		text-align: center;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.modal-body {
		padding: 30rpx;
	}

	.input-item {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #e5e5e5;
		border-radius: 10rpx;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}

	.modal-footer {
		display: flex;
		border-top: 1rpx solid #e5e5e5;
	}

	.modal-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		border-radius: 0;
	}

	.cancel-btn {
		background-color: #f5f5f5;
		color: #333333;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #ffffff;
	}
</style>