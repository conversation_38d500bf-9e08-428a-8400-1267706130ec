<template>
	<uni-drawer ref="drawer" :width="600" zindex="99"  style="z-index: 99;">
		<view class="leftMenu">
			<view class="cont">
				<view class="nameInfo">
					<navigator url="/pages/persCenter/persCenter" hover-class="none">
						<view class="flex flex-align-center">
							<text class="nameInfo-name">{{ UserInfo.shopName }}</text>
							<uni-icons type="forward" size="44" color="#666"></uni-icons>
						</view>
					</navigator>
					<!-- <view class="medal">
						<view class="medal-cont">{{ UserInfo.name || '暂无等级' }}</view>
					</view> -->
				</view>
				<view class="other flex flex-align-start">
					<view class="otherCont">
						<view class="title-num">{{ UserInfo.serviceRating }}</view>
						<view class="flex flex-align-center">
							<text class="otherCont-title">评价等级</text>
							<my-icon class="otherCont-title-icon" size="38" color="#7c8e9c">&#xe603;</my-icon>
						</view>
					</view>
					<view class="otherCont">
						<navigator url="/pages/accountCore/staticForm" hover-class="none">
							<view class="title-num">{{ UserInfo.todayCountPaid || 0 }}</view>
							<view class="flex flex-align-center">
								<text class="otherCont-title">今日单量</text>
								<my-icon class="otherCont-title-icon" size="38" color="#7c8e9c">&#xe603;</my-icon>
							</view>
						</navigator>
					</view>
					<view class="otherCont">
						<navigator url="/pages/myInfo/accountCore" hover-class="none">
							<view class="title-num">{{ UserInfo.todaySumMerchantIncome || 0 }}</view>
							<view class="flex flex-align-center">
								<text class="otherCont-title">今日收入</text>
								<my-icon class="otherCont-title-icon" size="38" color="#7c8e9c">&#xe603;</my-icon>
							</view>
						</navigator>
					</view>
				</view>
			</view>
			<view class="centInfo">
				<view class="centInfo-list">
					<!-- <view class="centInfo-title">
						<navigator url="/pages/orderStatis/orderStatis" hover-class="none">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon1.png" mode="widthFix"></image>
								<text>订单统计</text>
							</view>
						</navigator>
					</view> -->
					<view class="centInfo-title">
						<navigator :url="'/pages/myInfo/my-account?money=' + (UserInfo.balance || '')" hover-class="none" class="flex flex-align-center justify-space-between">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon2.png" mode="widthFix"></image>
								<text>我的账户</text>
							</view>
							<view class="balance">余额 ￥{{ UserInfo.balance || '0.00' }}</view>
						</navigator>
					</view>
					<!-- <view class="centInfo-title">
						<navigator url="/pages/agreeRule/news-notice" hover-class="none">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon3.png" mode="widthFix"></image>
								<text>消息通知</text>
							</view>
						</navigator>
					</view> -->
					
					<view class="centInfo-title">
						<navigator :url="`/pages/goods/list?shopId=${UserInfo.shopId}`" hover-class="none" class="flex flex-align-center justify-space-between">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon2.png" mode="widthFix"></image>
								<text>商品管理</text>
							</view>
						</navigator>
					</view>
					<view class="centInfo-title">
						<navigator :url="'/pages/fullReductionRuleList/list'" hover-class="none" class="flex flex-align-center justify-space-between">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon2.png" mode="widthFix"></image>
								<text>满减活动</text>
							</view>
						</navigator>
					</view>
					<view class="centInfo-title">
						<navigator :url="'/pages/couponsList/list'" hover-class="none" class="flex flex-align-center justify-space-between">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon2.png" mode="widthFix"></image>
								<text>优惠券管理</text>
							</view>
						</navigator>
					</view>
					<!-- <view class="centInfo-title">
						<navigator :url="'/pages/myInfo/my-account?money=' + (UserInfo.balance || '')" hover-class="none" class="flex flex-align-center justify-space-between">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon2.png" mode="widthFix"></image>
								<text>评论管理</text>
							</view>
						</navigator>
					</view> -->
					
					<view class="centInfo-title">
						<navigator url="/pages/printer/list" hover-class="none" class="flex flex-align-center justify-space-between">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon2.png" mode="widthFix"></image>
								<text>打印机管理</text>
							</view>
						</navigator>
					</view>
					<!-- <view class="centInfo-title">
						<navigator url="/pagesB/appeal/appeal" hover-class="none">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon3.png" mode="widthFix"></image>
								<text>违规申诉</text>
							</view>
						</navigator>
					</view>
					<view class="centInfo-title">
						<navigator url="/pagesB/pullNew/pullNew" hover-class="none">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon4.png" mode="widthFix"></image>
								<text>拉新有奖</text>
							</view>
						</navigator>
					</view>
					<view class="centInfo-title">
						<navigator url="/pagesB/activitCenter/activitCenter" hover-class="none">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon5.png" mode="widthFix"></image>
								<text>活动中心</text>
							</view>
						</navigator>
					</view>
					<view class="centInfo-title">
						<navigator url="/pagesA/myInfo/accountCore" hover-class="none">
							<view class="centInfo-title-ls">
								<image class="centInfo-title-pic" src="@/static/icon/icon6.png" mode="widthFix"></image>
								<text>装备商城</text>
							</view>
						</navigator>
					</view> -->
				</view>
				<view class="ejectMenu" :class="{ UpTrue: UpShow }">
					<view class="ejectMenu-icon" @click="handClickUp">
						<uni-icons :type="UpShow ? 'arrowdown' : 'arrowup'" color="#666" size="48"></uni-icons>
					</view>
					<view class="ejectMenuGrad flex flex-align-center flex-wrap">
						<!-- <view class="ejectMenuGrad-box flex flex-direction-column flex-align-center" @click="$common.scanCode(scanCodes)">
							<image class="footerMenu-ls" src="@/static/iconimage/ss.png" mode="widthFix"></image>
							<text class="grid-text">扫一扫</text>
						</view> -->
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pagesA/rankingList/rankingList" class="flex flex-direction-column flex-align-center">
								<image class="footerMenu-ls" src="@/static/iconimage/rl.png" mode="widthFix"></image>
								<text class="grid-text">热力订单</text>
							</navigator>
						</view> -->
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pagesA/rankingList/rankingList" class="flex flex-direction-column flex-align-center" hover-class="none">
								<image class="footerMenu-ls" src="@/static/iconimage/xg.png" mode="widthFix"></image>
								<text class="grid-text">骑士排行榜</text>
							</navigator>
						</view> -->
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pages/authentication/answerOne" class="flex flex-direction-column flex-align-center" hover-class="none">
								<image class="footerMenu-ls" src="@/static/iconimage/xy.png" mode="widthFix"></image>
								<text class="grid-text">神奇学院</text>
							</navigator>
						</view> -->
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pages/authentication/studyTrain" class="flex flex-direction-column flex-align-center" hover-class="none">
								<image class="footerMenu-ls" src="@/static/iconimage/px.png" mode="widthFix"></image>
								<text class="grid-text">实地培训</text>
							</navigator>
						</view> -->
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pagesA/rankingList/rankingList" class="flex flex-direction-column flex-align-center">
								<image class="footerMenu-ls" src="@/static/iconimage/ys.png" mode="widthFix"></image>
								<text class="grid-text">我的保险</text>
							</navigator>
						</view> -->
						<view class="ejectMenuGrad-box">
							<navigator url="/pages/message/index" class="flex flex-direction-column flex-align-center">
								<image class="footerMenu-ls" src="@/static/iconimage/zx.png" mode="widthFix"></image>
								<text class="grid-text">消息中心</text>
							</navigator>
						</view>
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pagesA/rankingList/rankingList" class="flex flex-direction-column flex-align-center">
								<image class="footerMenu-ls" src="@/static/iconimage/sc.png" mode="widthFix"></image>
								<text class="grid-text">装备商城</text>
							</navigator>
						</view> -->
						<view class="ejectMenuGrad-box">
							<navigator url="/pages/shopData/index" class="flex flex-direction-column flex-align-center" hover-class="none">
								<image class="footerMenu-ls" src="@/static/iconimage/sc.png" mode="widthFix"></image>
								<text class="grid-text">门店数据</text>
							</navigator>
						</view>
						<view class="ejectMenuGrad-box">
							<navigator url="/pages/setting/setting" class="flex flex-direction-column flex-align-center" hover-class="none">
								<image class="footerMenu-ls" src="@/static/iconimage/set.png" mode="widthFix"></image>
								<text class="grid-text">设置</text>
							</navigator>
						</view>
						<!-- <view class="ejectMenuGrad-box">
							<navigator url="/pagesA/rankingList/rankingList" class="flex flex-direction-column flex-align-center">
								<image class="footerMenu-ls" src="@/static/iconimage/ipon.png" mode="widthFix"></image>
								<text class="grid-text">号码采集</text>
							</navigator>
						</view>
						<view class="ejectMenuGrad-box">
							<navigator url="/pagesA/rankingList/rankingList" class="flex flex-direction-column flex-align-center">
								<image class="footerMenu-ls" src="~@/static/iconimage/jb.png" mode="widthFix"></image>
								<text class="grid-text">骑士举报</text>
							</navigator>
						</view> -->
					</view>
				</view>
			</view>
			<view class="footerInfo">
<!-- 				<uni-icons type="location-filled" color="#666"></uni-icons>
				<text class="footerInfo-text">{{ address.address ? address.address : '位置获取中...' }}</text>
				<navigator url="" hover-class="none">
					<button class="footerInfo-btn" type="default" size="mini">去更新</button>
				</navigator> -->
			</view>
		</view>
	</uni-drawer>
</template>

<script>
import { mapState } from 'vuex';
export default {
	props: {
		UserInfo: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			UpShow: false
		};
	},
	computed: {
		...mapState(['address'])
	},
	methods: {
		scanCodes(msg) {
			console.log(msg)
		},
		open() {
			this.$refs.drawer.open();
		},
		// 弹出菜单
		handClickUp() {
			this.UpShow = !this.UpShow;
		},
		// 弹出层关闭
		close() {
			this.$refs.drawer.close();
		}
	}
};
</script>

<style lang="scss" scoped>
.leftMenu {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	height: 100%;
	overflow: hidden;
	will-change: auto;

	.cont {
		width: 100%;
		padding: 0rpx 0rpx 80rpx 40rpx;
		background: url('data:image/png;base64,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') no-repeat;
		background-size: 100% 100%;
		background-position: left top;
		transition: all 0.3s ease;

		.nameInfo {
			margin-top: calc(var(--status-bar-height));
			padding-top: 50rpx;

			.nameInfo-name {
				font-size: 50rpx;
				font-weight: bold;
			}

			.medal {
				color: #ffffff;
				padding: 10rpx 0;

				.medal-cont {
					display: inline-block;
					background: #7c8e9c;
					padding: 5rpx 15rpx;
					font-size: 20rpx;
					transform: skew(-12deg);
					min-height: 40rpx;
				}
			}
		}

		.other {
			padding-top: 70rpx;
			transition: opacity 0.1s ease;

			.otherCont {
				flex: 1;
				font-family: Source Han Sans CN;
				color: #343434;

				.otherCont-title {
					font-size: $font-my-size-26;
				}

				.otherCont-title-icon {
					font-size: 32rpx;
					margin-top: 5rpx;
				}

				.title-num {
					font-size: 50rpx;
					font-weight: bold;
				}
			}
		}
	}

	.centInfo {
		flex: 1;
		position: relative;
		top: 0rpx;
		left: 0rpx;
		box-sizing: border-box;
		padding-left: 8rpx;

		.centInfo-list {
			padding: 40rpx 32rpx 0rpx;

			.centInfo-title {
				font-size: 32rpx;
				font-weight: bold;
				padding-bottom: 60rpx;

				.centInfo-title-ls {
					/deep/span {
						color: #050704;
						opacity: 0.8;
					}

					.centInfo-title-pic {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
						vertical-align: top;
						margin-top: 2rpx;
					}
				}

				.balance {
					font-size: 24rpx;
					border: 2rpx solid #666666;
					border-radius: 50rpx;
					padding: 4rpx 20rpx;
				}
			}
		}

		.ejectMenu {
			position: absolute;
			left: 0rpx;
			right: 0rpx;
			bottom: 0rpx;
			height: 200rpx;
			background: #ffffff;
			overflow: hidden;
			z-index: 1;
			transition: all 0.3s ease;

			.ejectMenu-icon {
				text-align: center;
				padding-bottom: 20rpx;
			}

			.ejectMenuGrad {
				.ejectMenuGrad-box {
					width: 33.33%;
					text-align: center;
					padding-bottom: 40rpx;

					.footerMenu-ls {
						width: 70rpx;
						height: 70rpx;
					}

					.grid-text {
						font-size: 24rpx;
						margin-top: 10rpx;
					}
				}
			}
		}

		.UpTrue {
			height: 100%;
		}
	}

	.footerInfo {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 25rpx 32rpx;
		border-top: 2rpx solid #eee;
		font-family: Source Han Sans CN;
		font-weight: 500;
		color: #666666;

		.footerInfo-text {
			font-size: 24rpx;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
			flex: 1;
		}

		.footerInfo-btn {
			margin: 0rpx 0rpx 0rpx 20rpx;
			padding: 0rpx 10rpx;
			font-size: 20rpx;
			line-height: 40rpx;
			height: 40rpx;
			color: #eb7736;
			border: none;
			border-radius: 30rpx;
			background-color: #fcf0e7;

			&::after {
				border: none;
				outline: none;
			}
		}
	}
}
</style>
