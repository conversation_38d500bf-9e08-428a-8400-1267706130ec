<template>
	<view class="new-phone">
		<view class="register-title">设置新手机号</view>
		<view class="phone-box">
			<view class="phone flex flex-align-center">
				<input class="phone-input" type="number" maxlength="11" v-model="formData.tel" placeholder="输入新手机号" placeholder-class="placeholderStyl" />
				<uni-icons type="close" v-if="formData.tel" @click="formData.tel = ''"></uni-icons>
			</view>
			<view class="phone flex flex-align-center">
				<input class="phone-input" type="number" maxlength="6" v-model="code" placeholder="输入验证码" placeholder-class="placeholderStyl" />
				<view class="phone-input-code" @click="countDown">{{ seconds == 60 ? (type == 1 ? '获取验证码' : '重新获取') : seconds + '秒重新获取' }}</view>
			</view>
		</view>
		<my-button margin-top="60" height="92" :bold="true" color="#fff" :disabled="!(code && formData.tel)" font-size="40" @confirm="submitData">完成</my-button>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				tel: ''
			},
			code: '',
			seconds: 60,
			time: null,
			type: 1
		};
	},
	methods: {
		submitData() {
			if (!this.code) {
				this.$interactive.ShowToast(
					{
						title: '请输入验证码！'
					},
					false
				);
				return;
			}
			this.$request({
				url: '/login/check-code',
				data: { code: this.code },
				IsGetStorg: false
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.submitTel();
				} else {
					this.$interactive.ShowToast(
						{
							title: res.message
						},
						false
					);
				}
			});
		},
		submitTel() {
			this.$request({
				url: '/login/change-tel',
				data: this.formData
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code) {
					this.$interactive
						.ShowToast({
							title: '修改成功！'
						})
						.then(() => {
							uni.reLaunch({
								url: '/pages/setting/account-set'
							});
						});
				}
			});
		},
		countDown() {
			if (!this.formData.tel) {
				this.$interactive.ShowToast(
					{
						title: '请输入手机号！'
					},
					false
				);
				return;
			}
			if (this.seconds == 60) {
				this.$request({
					url: '/login/get-code',
					data: { tel: this.formData.tel, type: 1 },
					IsGetStorg: false
				}).then(res => {
					// #ifdef MP-WEIXIN
						res = JSON.parse(res)
					// #endif
					if (res.code == 1) {
						this.seconds = 59;
						this.time = setInterval(() => {
							this.seconds--;
							if (this.seconds === 0) {
								clearInterval(this.time);
								this.type++;
								this.seconds = 60;
							}
						}, 1000);
					} else {
						this.$interactive.ShowToast(
							{
								title: res.message
							},
							false
						);
					}
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.new-phone {
	padding: 0 62rpx;

	.register-title {
		font-size: $font-my-size-30;
		font-weight: bold;
		padding: 40rpx 0rpx 0;
	}
	.register-title-tips {
		font-size: $font-my-size-22;
		color: $font-my-color-6;
		padding-top: 30rpx;
	}

	.phone-box {
		padding-top: 40rpx;

		.phone {
			border-bottom: 1rpx solid $uni-bg-color-grey;

			.phone-input {
				padding: 32rpx 0;
				flex: 1;

				.placeholderStyl {
				}
			}

			.phone-input-code {
				font-size: $font-my-size-28;
				color: $font-my-color-6;
				width: 200rpx;
				text-align: center;
				background: #ececec;
				padding: 6rpx 0rpx;
				border-radius: 6rpx;
				border: 1rpx solid #e4e2e2;
			}
		}
	}
}
</style>
