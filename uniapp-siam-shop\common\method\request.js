import { ShowToast } from "@/utils/interactive/interactive.js"

// 全局域名根据用户配置来产生 具体请看 ajax（） 方法
var baseURL = '';
var shakeProof = true;

/**
 * @property {function} ajax 接口权限接口配置 根据obj.IsApiType来判断是否使用默认域名
 * @param {Object} obj 请求信息
 * @param {Object} obj resove Promise成功回调函数
 * @param {Object} obj resove Promise失败回调函数
 * @param {Object} obj reject Promise失败回调函数
 * @param {type} res 获取本地用户信息，配置请求头
 * */
function ajax(obj, resove, reject, res) {
	if (obj.IsApiType) {
		uni.getStorage({
			key: "account",
			success: (msg) => {
				// if (msg.data.isDev == 1) { //http
				// 	baseURL = 'http://' + msg.data.domainUrl
				// } else{ // https
				// 	baseURL = 'https://' + msg.data.domainUrl
				// }
				baseURL = msg.data.domainUrl
				startRequest(obj, resove, reject, res, msg.data.uniacid)
			},
			fail: (err) => {
				ShowToast({ icon: 'close', title: '服务器错误！' })
			}
		})
	} else {
		startRequest(obj, resove, reject, res) // 开始请求
	}
}

/*
 * 发起请求
 * @param {Object} obj 提交的数据配置信息
 * @param {function} resove Promise成功回调函数
 * @param {function} reject Promise失败回调函数
 * @param {Object} res 获取本地用户信息，配置请求头
 * */
function startRequest(obj, resove, reject, res, uniacid) {
	console.log("startRequest")
	console.log("startRequest:"+ obj.IsApiType ? (baseURL + obj.url) : obj.url)
	uni.request({
		header: {
			'content-type': obj.contentType ? 'application/json;charset=UTF-8' : 'application/x-www-form-urlencoded',
			'uniacid': getApp().globalData.siteInfo.uniacid,
			'module': "yb_ps",
			'userId': res ? res.data.userId : "",
			'appType': 'weChat',
			'Authorization': res ? res.data.authorization : ''
		},
		dataType: 'JSON',
		url: obj.IsApiType ? (baseURL + obj.url) : obj.url,
		method: obj.method,
		data: obj.data,
		success: data => {
			resove(data.data)
		},
		fail: err => {
			reject(err)
		}
	})
}

/**
 * @property {Function} request 全局公用接口
 * @param {String} url 请求的接口
 * @param {String} method 请求类型
 * @param {String} data 请求参数
 * @param {Boolern} contentType 参数类型，默认 false（application/x-www-form-urlencoded）
 * @param {Boolern} IsThrottle 是否节流
 * @param {Boolern} IsGetStorg 是否获取本地缓存信息
 * @param {Boolern} IsApiType 是否使用全局域名 默认 true；false表示自己传域名
 */
export default function request({ url = '', contentType = true, method = 'POST', data = "", IsThrottle = false, IsGetStorg = true, IsApiType = true }) {
	let Data = { url, contentType, method, data, IsThrottle, IsGetStorg, IsApiType }
	return new Promise((resove, reject) => {
		if (IsGetStorg) { // 是否获取本地缓存信息
			uni.getStorage({
				key: "account",
				success: res => {
					ajax(Data, resove, reject, res);
				}
			});
		} else {
			ajax(Data, resove, reject, false)
		}
	}).catch(err => {
		console.log("网络错误")
		ShowToast({ title: "网络错误" }, false)
	})
}
