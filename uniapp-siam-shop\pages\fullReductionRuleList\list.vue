<template>
  <view class="content">
    <uni-status-bar></uni-status-bar>
    <view class="content-box">
      <custom-tabs-swiper ref="tabsSwiper" :list="statusList" active-color="#fa7c25" inactive-color="#333"
      bg-color="#fa7c25" :show-line="true" @changePage="changePage">
      <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :top="-40"
      :up="{ auto: false, empty: {tip: '暂无满减活动数据' } }">
      <view class="contInfo">
        <template v-for="(item, index) in ruleList" >
          <view class="rule-item margin-bottom">
            <view class="contInfo-title-box">
              <view class="contInfo-title">
                <view class="contTitle">{{ item.name }}</view>
                <view class="contTitle-label">满 <text class="price">¥{{ item.limitedPrice }}</text> 减 <text class="price">¥{{ item.reducedPrice }}</text></view>
                <view class="status-badge">
                  <u-tag :text="getStatusText(item.status)" :type="getStatusClass(item.status)" :plain="false" size="mini" :color="item.status === 1 ? '#34c759' : '#ff3b30'"></u-tag>
                </view>
              </view>
            </view>
            <view class="footerBoten">
              <view class="action-btn edit-btn" @click.stop="editRule(item)">编辑</view>
              <view class="action-btn delete-btn" @click.stop="deleteRule(item)">删除</view>
            </view>
          </view>
        </template>
      </view>
    </mescroll-uni>
    </custom-tabs-swiper>
    </view>
    
    <!-- 新增满减活动按钮 -->
    <view class="add-btn" @click="addRule">
      <text class="add-icon">+</text>
    </view>
    
  </view>
</template>

<script>
import CustomTabsSwiper from '../../components/custom-tabs-page-linkge/tab-page-linkage.vue'; // 全屏联动
export default {
  components: {
    CustomTabsSwiper
  },
  data () {
    return {
      statusList: [
        { name: '全部', value: '' },
        { name: '开启', value: 1 },
        { name: '关闭', value: 2 }
      ],
      ruleList: [],
      status: ''
    }
  },
  onShow() {
    // 检查是否需要刷新列表
    const needRefresh = uni.getStorageSync('ruleListNeedRefresh');
    if (needRefresh) {
      // 清除标记
      uni.removeStorageSync('ruleListNeedRefresh');
      // 刷新列表
      this.refresh();
    }
  },
  methods: {
    // 初始化mescroll对象
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
    },
    // 主动触发下拉刷新
    refresh () {
      if (this.mescroll) {
        this.mescroll.triggerDownScroll();
      }
    },
    changePage (e) { // 页面切换
      this.status = this.statusList[e].value;
      this.refresh();
    },
    // 下拉回调函数
    downCallback (e) {
      this.mescroll.resetUpScroll();
    },
    // 上拉加载更多
    upCallback (e) {
      this.getRuleList(e.num);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '',
        1: '开启', 
        2: '关闭'
      };
      return statusMap[status] || '';
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        1: 'primary',
        2: 'error'
      };
      return classMap[status] || '';
    },
    // 新增满减活动
    addRule() {
      uni.navigateTo({
        url: '/pages/fullReductionRuleList/edit'
      });
    },
    // 编辑满减活动
    editRule(item) {
      const formData = {
          name: item.name,
          limitedPrice: item.limitedPrice,
          reducedPrice: item.reducedPrice,
          status: item.status || 1,
          id: item.id,
          shopId: item.shopId
        }
      uni.navigateTo({
        url: '/pages/fullReductionRuleList/edit?data=' + JSON.stringify(formData)
      });
    },
    // 删除满减活动
    deleteRule(item) {
      uni.showModal({
        title: '提示',
        content: `确定要删除该满减活动吗？`,
        success: async(res) => {
          if (res.confirm) {
            const res = await this.$request({
              url: '/api-promotion/rest/merchant/fullReductionRule/delete',
              method: 'delete',
              data: { id: item.id }
            });
            if (res.code === 200) {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.refresh();
            } else {
              uni.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    // 获取满减活动列表
    async getRuleList (page = 1) {
      try {
        let url = '/api-promotion/rest/merchant/fullReductionRule/list';
        let params = {
          pageNo: page,
          pageSize: 10,
          status: this.status || undefined
        };
      
        
        this.$request({
          url,
          data: params
        }).then(res => {
          if (res.code === 200) {
            if (page === 1) {
              this.ruleList = res.data.records;
            } else {
              this.ruleList = this.ruleList.concat(res.data.records);
            }
            this.mescroll.endSuccess(res.data.records.length, res.data.records.length >= 10);
          } else {
            this.mescroll.endSuccess(0, false);
          }
        }).catch(err => {
          this.mescroll.endErr();
        });
      } catch (e) {
        console.error('error:', e);
        this.mescroll.endErr();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$borderColor: rgba(0, 0, 0, 0.8);
.content {
  position: relative;
  width: 750rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-x: hidden;
  background-color: $uni-bg-color-grey;
  .content-box {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
.contInfo {
  height: auto;
  padding: 0 20rpx;
}

.margin-bottom {
  margin-bottom: 20rpx;
}

.rule-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s ease;
  border-left: 8rpx solid #fa7c25;
}

.rule-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
}

.contInfo-title-box {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.contInfo-title {
  flex: 1;
  position: relative;
}

.contTitle {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  padding-right: 120rpx;
}

.contTitle-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.price {
  color: #fa7c25;
  font-weight: bold;
  font-size: 30rpx;
}

.status-badge {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4rpx;
  font-size: 22rpx;
  font-weight: 500;
  z-index: 2;
}

.footerBoten {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #f5f5f5;
  padding-top: 20rpx;
  margin-top: 16rpx;
}

.action-btn {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.03);
}

.edit-btn {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid rgba(24, 144, 255, 0.3);
}

.delete-btn {
  background-color: #fff1f0;
  color: #ff4d4f;
  border: 1rpx solid rgba(255, 77, 79, 0.3);
}

.add-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background: #fa7c25;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(250, 124, 37, 0.3);
  z-index: 10;
}

.add-icon {
  font-size: 60rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}
</style>