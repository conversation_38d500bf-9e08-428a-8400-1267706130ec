<template>
	<view class="rankingList">
		<custom-tabs :list="list" :is-scroll="false" :background="false" :current="current" :lineheight-show="true"
			@change="change" active-color="#E13234"></custom-tabs>
		<view class="rankingList-banner">
			<view class="bg-pic">
				<view class="flex flex-align-start justify-space-between">
					<view class="banner-info">
						<view>昨日收入榜</view>
						<text class="cont-info">(排行说明)</text>
					</view>
					<view class="tab-list flex flex-align-center">
						<view class="tab-list-btn tab-list-btn-l" :class="{ 'tab-list-btn-bgColor': styleType === 1 }"
							@click="handClickChange(1)">日榜</view>
						<view class="tab-list-btn tab-list-btn-r" :class="{ 'tab-list-btn-bgColor': styleType === 2 }"
							@click="handClickChange(2)">月榜</view>
					</view>
				</view>
				<view class="ranking-box">
					<view class="flex flex-align-end">
						<view class="ranking-num">
							<image class="ranking-num-pic" src="./static/2.png" mode="scaleToFill"></image>
							<view class="ranking-num-steps-one">
								<text
									class="ranking-num-name">{{ rankingList[1] ? rankingList[1].realName : '暂无骑手' }}</text>
								<view v-if="current == 0">{{ rankingList[1] ? '￥' + rankingList[1].money : '￥0.00'}}
								</view>
								<view v-else>{{ rankingList[1] ? '' + rankingList[1].count : '0'}}</view>
							</view>
						</view>
						<view class="ranking-num ranking-num-two">
							<image class="ranking-num-img" src="./static/1.png" mode="scaleToFill"></image>
							<view class="ranking-num-steps-one ranking-num-steps-two">
								<text
									class="ranking-num-name">{{ rankingList[0] ? rankingList[0].realName : '暂无骑手' }}</text>
								<view v-if="current == 0">{{ rankingList[0] ? '￥' + rankingList[0].money : '￥0.00'}}
								</view>
								<view v-else>{{ rankingList[0] ? rankingList[0].count : '0'}}</view>
							</view>
						</view>
						<view class="ranking-num">
							<image class="ranking-num-pic" src="./static/3.png" mode="scaleToFill"></image>
							<view class="ranking-num-steps-one">
								<text
									class="ranking-num-name">{{ rankingList[2] ? rankingList[2].realName : '暂无骑手' }}</text>
								<view v-if="current == 0">{{ rankingList[2] ? '￥' + rankingList[2].money : '￥0.00'}}
								</view>
								<view v-else>{{ rankingList[2] ? rankingList[2].count : '0'}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="ranking-cont-list">
			<template v-if="rankingList.length > 3">
				<view v-for="(item, index) of rankingList" v-if="index > 3">
					<view class="ranking-cont-list-ls flex flex-align-center justify-space-between" :key="item.riderId">
						<view class="list-ls-name">
							<text class="list-ls-name-num">{{ index + 1 }}</text>
							<text>{{ item.name ? item.name.replace(/[^]/, '*') : '***' }}</text>
						</view>
						<view class="list-ls-money">￥{{ item.money }}</view>
					</view>
				</view>
			</template>
			<u-empty v-else text="暂无名次" mode="list"></u-empty>
			<view class="footer-trip flex flex-align-end" v-if="popData">
				<image class="footer-trip-pic" src="./static/<EMAIL>" mode="widthFix"></image>
				<view class="footer-trip-cont">
					<view class="footer-trip-cont-name">{{ popData.name ? popData.name.replace(/[^]/, '*') : '***' }}
					</view>
					<text>排名第{{ rankingList.length + 1 }}位，继续加油哦</text>
				</view>
				<view class="footer-trip-money">
					￥
					<text style="font-size: 40rpx;">{{ popData.money }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import CustomTabs from '@/components/custom-tabs/custom-tabs.vue';

	export default {
		components: {
			CustomTabs
		},
		data() {
			return {
				current: 0,
				styleType: 1,
				list: [{
						name: '收入排行'
					},
					{
						name: '单量排行'
					}
				],
				championList: [],
				rankingList: [],
				popData: null
			};
		},
		onLoad() {
			this.getRanking();
		},
		methods: {
			change(msg) {
				this.current = msg;
				this.getRanking();
			},
			handClickChange(res) {
				this.styleType = res;
				this.getRanking();
			},
			getRanking() {
				this.$request({
					url: '/rider-bill/rider-ranking',
					method: 'GET',
					data: {
						type: this.styleType,
						item: this.current + 1
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
					res = JSON.parse(res)
					// #endif
					if (res.code == 1) {
						this.rankingList = res.data;
						if (this.rankingList.length > 8) {
							this.popData = this.rankingList.pop();
						}
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.rankingList {
		display: flex;
		flex-direction: column;
		height: 100vh;

		/deep/.u-tab-item {
			color: #333 !important;
		}

		.rankingList-banner {
			background: linear-gradient(0deg, #f85356 0%, #ed4447 52%, #df3335 100%);

			.bg-pic {
				width: 750rpx;
				background: url(static/bg.png) no-repeat;
				background-size: 100% 100%;
				padding: 40rpx 40rpx 0rpx;

				.banner-info {
					font-size: $font-my-size-34;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;

					.cont-info {
						font-size: $font-my-size-26;
						opacity: 1;
					}
				}

				.tab-list {
					width: 262rpx;

					.tab-list-btn {
						flex: 1;
						height: 58rpx;
						line-height: 54rpx;
						text-align: center;
						font-size: 26rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: #ffffff;
						border: 1rpx solid #ffffff;
					}

					.tab-list-btn-l {
						border-right: none;
						border-radius: 29rpx 0rpx 0rpx 29rpx;
					}

					.tab-list-btn-r {
						border-left: none;
						border-radius: 0rpx 29rpx 29rpx 0rpx;
					}

					.tab-list-btn-bgColor {
						background: #ffffff;
						color: #f85356;
					}
				}

				.ranking-box {
					margin-top: 60rpx;

					.ranking-num {
						flex: 1;
						text-align: center;

						.ranking-num-pic {
							width: 118rpx;
							height: 150rpx;
						}

						.ranking-num-img {
							width: 200rpx;
							height: 200rpx;
						}

						.ranking-num-steps-one {
							position: relative;
							margin-top: 30rpx;
							height: 138rpx;
							background: linear-gradient(0deg, #ff6b61 0%, #e93132 100%);
							font-size: $font-my-size-34;
							font-family: Source Han Sans CN;
							font-weight: 400;
							color: #ffffff;
							padding: 30rpx 0rpx 0rpx;

							&::before {
								position: absolute;
								top: -30rpx;
								left: 0rpx;
								right: 0rpx;
								display: block;
								height: 0rpx;
								border-color: transparent transparent rgba(255, 255, 255, 0.2) transparent;
								border-width: 0rpx 20rpx 30rpx 20rpx;
								border-style: solid;
								content: '';
							}

							.ranking-num-name {
								font-size: $font-my-size-34;
								color: #fdebc7;
								height: 50rpx;
								line-height: 50rpx;
							}
						}

						.ranking-num-steps-two {
							margin-top: 20rpx;
							height: 189rpx;
						}
					}

					.ranking-num-two {
						position: relative;
						z-index: 10;
						transform: translateZ(10rpx);
						margin: 0 -15rpx;
					}
				}
			}
		}

		.ranking-cont-list {
			position: relative;
			top: 0rpx;
			left: 0rpx;
			flex: 1;
			height: 500rpx;
			overflow-y: scroll;

			.ranking-cont-list-ls {
				padding: 40rpx 50rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #575757;

				&:nth-child(2n) {
					background: #f7f7f7;
				}

				.list-ls-name {
					font-size: $font-my-size-34;

					.list-ls-name-num {
						margin-right: 60rpx;
					}
				}

				.list-ls-money {
					font-size: $font-my-size-34;
				}
			}

			.footer-trip {
				position: fixed;
				bottom: 30rpx;
				left: 20rpx;
				right: 20rpx;
				height: 120rpx;
				background: rgba(0, 0, 0, 0.8);
				border-radius: 65rpx;

				.footer-trip-pic {
					width: 180rpx;
					height: 144rpx;
				}

				.footer-trip-cont {
					flex: 1;
					font-size: 28rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #999999;
					padding-left: 40rpx;
					margin-bottom: 14rpx;

					.footer-trip-cont-name {
						font-size: 36rpx;
						color: #ffffff;
					}
				}

				.footer-trip-money {
					font-size: 28rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
					height: 130rpx;
					line-height: 130rpx;
					padding-right: 60rpx;
				}
			}
		}
	}
</style>
