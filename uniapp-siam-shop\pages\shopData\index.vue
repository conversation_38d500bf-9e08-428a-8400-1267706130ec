<template>
  <view class="shop-data-container">

    <!-- 数据分析项目列表 -->
    <view class="data-list">
      <!-- 顾客分析 -->
      <view class="data-item" @click="navigateTo('/pages/shopData/customerAnalysis/index')">
        <view class="icon-wrapper purple">
          <text class="icon-text">析</text>
        </view>
        <view class="item-content">
          <view class="item-title">顾客分析</view>
          <view class="item-desc">看看回头客多还是新客多</view>
        </view>
        <view class="arrow">
          <u-icon color="#999" name="arrow-right"></u-icon>
        </view>
      </view>

      <!-- 资金明细 -->
      <view class="data-item" @click="navigateTo('/pages/shopData/fundDetail/index')">
        <view class="icon-wrapper orange">
          <text class="icon-text">账</text>
        </view>
        <view class="item-content">
          <view class="item-title">资金明细</view>
          <view class="item-desc">列举所有账单收入与支出的记录，一分钱都不错过</view>
        </view>
        <view class="arrow">
          <u-icon color="#999" name="arrow-right"></u-icon>
        </view>
      </view>

      <!-- 营收统计 -->
      <view class="data-item" @click="navigateTo('/pages/shopData/revenueStatistics/index')">
        <view class="icon-wrapper green">
          <text class="icon-text">营</text>
        </view>
        <view class="item-content">
          <view class="item-title">营收统计</view>
          <view class="item-desc">筛选汇总财务明细记录，直观看到每日收益</view>
        </view>
        <view class="arrow">
          <u-icon color="#999" name="arrow-right"></u-icon>
        </view>
      </view>

      <!-- 商品统计 -->
      <view class="data-item" @click="navigateTo('/pages/goods/statistics')">
        <view class="icon-wrapper blue">
          <text class="icon-text">商</text>
        </view>
        <view class="item-content">
          <view class="item-title">商品统计</view>
          <view class="item-desc">通过时间统计最近商品销量，从上往下排</view>
        </view>
        <view class="arrow">
          <u-icon color="#999" name="arrow-right"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {

    };
  },
  methods: {
    // 返回上一页
    goBack () {
      uni.navigateBack();
    },

    // 页面跳转
    navigateTo (url) {
      uni.navigateTo({
        url: url
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.shop-data-container {
  background-color: #f5f5f5;
  min-height: 100vh;

  .header {
    background-color: #FF6320;
    color: #fff;
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    position: relative;
    height: 88rpx;

    .back-icon {
      position: absolute;
      left: 30rpx;
      font-size: 40rpx;
    }

    .title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 500;
    }
  }

  .data-list {
    padding: 20rpx;

    .data-item {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;

      .icon-wrapper {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20rpx;

        .icon-text {
          color: #fff;
          font-size: 36rpx;
          font-weight: bold;
        }
      }

      .purple {
        background-color: #9932CC;
      }

      .orange {
        background-color: #FF8C00;
      }

      .green {
        background-color: #32CD32;
      }

      .blue {
        background-color: #1E90FF;
      }

      .item-content {
        flex: 1;

        .item-title {
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 10rpx;
        }

        .item-desc {
          font-size: 24rpx;
          color: #999;
        }
      }

      .arrow {
        color: #ccc;
        font-size: 36rpx;
      }
    }
  }
}
</style>