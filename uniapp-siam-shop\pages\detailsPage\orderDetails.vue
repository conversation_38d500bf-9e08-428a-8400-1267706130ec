<template>
	<view class="orderDetails" v-if="orderInfo">
		<map id="MyMapView" class="MyMapView" ref="MyMapView" :latitude="center.lat" :longitude="center.lng"
			:markers="markers" :polyline="polyline" :scale="scale"></map>
		<view class="cont-dom">
			<u-cell-group :border="false">
				<view class="contInfo-title-box flex flex-align-center">
					<u-icon name="bell-fill" size="36" color="#666"></u-icon>
					<view class="contInfo-title flex flex-align-start justify-space-between">
						<!-- <view class="contTitle">
							<text>{{ $common.calcTime(orderInfo.createdAt, parseInt(orderInfo.arrivalMinute) + parseInt(orderInfo.deliveryMinute), 1) }}分钟内送达</text>
							<view class="contTitle-label">
								预计{{ $common.calcTime(orderInfo.createdAt, parseInt(orderInfo.arrivalMinute) + parseInt(orderInfo.deliveryMinute), 2) }}前
							</view>
						</view> -->
						<view class="contTitle">
							<text>取餐号：{{ orderInfo.queueNo }}号</text>
							<text>，{{ statusName[orderInfo.status] }}</text>
							<view class="contTitle-label">{{ orderInfo.description }}</view>
						</view>
						<text class="contTitle-money">￥{{ orderInfo.actualPrice }}</text>
					</view>
				</view>
				<my-line></my-line>
				<view class="cont-title-box">
					<view class="title-box-address flex flex-align-start">
						<!-- <view class="cont-title-icons">取</view> -->
						<view class="cont-title" v-if="orderInfo.shoppingWay == 2">
							<view class="cont-title-name">骑手：{{ orderInfo.riderName ? orderInfo.riderName : '待骑手接单' }}</view>
							<!-- <view class="cont-title-label">{{ orderInfo.startAddress }}</view> -->
						</view>
						<!-- <view class="navigation" @click="handNavigation(1)">
							<uni-icons type="paperplane" size="40"></uni-icons>
						</view> -->
						<view class="cont-info">{{ orderInfo.distance }}</view>
					</view>
					<view class="title-box-address flex flex-align-start">
						<view class="cont-title-icons cont-title-icons-bg">送</view>
						<view class="cont-title">
							<view class="cont-title-name">{{ orderInfo.contactProvince + orderInfo.contactCity + orderInfo.contactArea + orderInfo.contactStreet + orderInfo.contactHouseNumber }}</view>
							<view class="cont-title-label">
								<text>{{ orderInfo.contactRealname ? orderInfo.contactRealname.replace(/[^]/, '*') : '***' }}</text>
								<text>{{ ' ' + (orderInfo.contactPhone ? orderInfo.contactPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '*******') }}</text>
							</view>
						</view>
						<view class="navigation" @click="handNavigation(2)">
							<uni-icons type="paperplane" size="40"></uni-icons>
						</view>
					</view>
					<view class="msg2" v-if="orderInfo.status == 7 || orderInfo.status == 9">
						订单状态：{{orderInfo.status == 7 ? '售后处理中' : '售后处理完成'}}<br/>
						退款类型：{{orderInfo.refundType == 1 ? '已支付订单1分钟内被取消' : '已支付订单24小时内申请退款'}}<br/>
						退款方式：{{orderInfo.refundWay == 1 ? '全额退款' : '部分退款'}}<br/>
						退款原因：{{ refundReasonName[orderInfo.refundReason] }}<br/>
						退款金额：{{orderInfo.refundAmount}}元<br/>
						退款状态：{{ refundStatusName[orderInfo.refundStatus] }}<br/>
					</view>
				</view>
				<my-line :margin="[20, 0]"></my-line>
				<u-cell-item :arrow="false" hover-class="none" :border-bottom="false">
					<view slot="title"><text class="shopInfo">订单明细</text></view>
					<view slot="label">
						<view class="shopInfo-label padding flex flex-align-start">
							<text>商品详情：</text>
							<text class="list-cont">{{ orderInfo.description.replace(/；([^；]*)$/, '') }}</text>
						</view>
						<view class="shopInfo-label padding flex flex-align-center">
							<text>实付金额：</text>
							<text class="list-cont">￥{{ orderInfo.actualPrice }}</text>
						</view>
						<view class="shopInfo-label padding flex flex-align-center">
							<text>订单编号：</text>
							<text class="list-cont">{{ orderInfo.orderNo }}</text>
							<view style="display: flex;align-items: center;">
								<text>(序号:</text>
								<text style="color: #18B566;">#{{ orderInfo.queueNo || 0 }}</text>)
							</view>
						</view>
						<view class="shopInfo-label padding flex flex-align-center">
							<text>推单时间：</text>
							<text
								class="list-cont">{{orderInfo.createTime}}</text>
						</view>
						<template v-if="orderInfo.remark">
							<view class="remarks" v-if="orderInfo.remark.trim()">
								<text style="color: #30B983;">备注：</text>
								{{ orderInfo.remark }}
							</view>
						</template>
					</view>
				</u-cell-item>
				<my-line :margin="[20, 0]"></my-line>
				<u-cell-item :arrow="false" hover-class="none" :border-bottom="false">
					<view slot="title"><text class="shopInfo">配送信息</text></view>
					<view slot="label">
						<view class="shopInfo-label flex flex-align-center">
							<text>订单号：</text>
							<text class="list-cont">{{ orderInfo.orderNo }}</text>
						</view>
						<view class="shopInfo-label flex flex-align-center">
							<text>商家名称：</text>
							<text class="list-cont">{{ `${orderInfo.shopName}(ID：${orderInfo.shopId})` }}</text>
						</view>
						<!-- <view class="shopInfo-label flex flex-align-center">
							<text>订单渠道：</text>
							<text class="list-cont">{{ orderInfo.sourceName }}</text>
						</view> -->
						<view class="shopInfo-label flex flex-align-center">
							<text>配送费：</text>
							<text class="list-cont">￥{{ orderInfo.deliveryFee || "0" }}</text>
						</view>
					</view>
				</u-cell-item>
			</u-cell-group>
		</view>
		<view class="footer-btn flex flex-align-center">
			<view class="footer-btn-icon flex flex-align-center"
				v-if="orderInfo.status">
				<view class="icon-btn flex flex-direction-column" @click="setDialToRider" v-if="orderInfo.riderId">
					<uni-icons class="icon-pic" type="phone-filled"></uni-icons>
					<view class="">骑手</view>
				</view>
				<view class="icon-btn flex flex-direction-column" @click="setDialToUser">
					<uni-icons class="icon-pic" type="phone-filled"></uni-icons>
					<view class="">用户</view>
				</view>
				<!-- <navigator :url="'/pages/detailsPage/probleam?id=' + orderInfo.id" hover-class="none">
					<view class="icon-btn flex flex-direction-column justify-space-between">
						<my-icon class="icon-pic" size="40">&#xe60f;</my-icon>
						<view class="">遇到问题</view>
					</view>
				</navigator> -->
				<!-- <view class="icon-btn flex flex-direction-column justify-space-between" @click="handTranOrder">
					<my-icon class="icon-pic" size="40">&#xe605;</my-icon>
					<view class="">转单</view>
				</view> -->
			</view>
			<!-- 			<my-button v-if="orderInfo.status != 1 && orderInfo.status != 5" color="#fff" width="50%" style="flex: 1;" border-radius="10" :disabled="orderInfo.status == 5"
				:background="backgroundColor[orderInfo.status]" @confirm="submit">
				{{ buttonName[orderInfo.status] }}
			</my-button> -->
			<my-button color="#fff" style="flex: 1;margin-right: 20rpx;" width="150" border-radius="10" v-if="orderInfo.status == 7"
				background="#2a75ed" @confirm="agreeRefund(orderInfo)">
				同意
			</my-button>
			<my-button color="#fff" style="flex: 1;" width="190" border-radius="10" v-if="orderInfo.status == 7"
				background="linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))" @confirm="disagreeRefund(orderInfo)">
				不同意
			</my-button>
		</view>
		<u-modal v-model="telShow" :title="setDialInfo.title" :show-cancel-button="true" confirm-text="确认拨打"
			:content="'拨号给：' + (setDialInfo.tel || '')" @confirm="$common.dialFun(setDialInfo.tel)"></u-modal>
		<u-modal v-model="transferShow" :title="setDialInfo.title" :show-cancel-button="true" confirm-text="确认"
			:cancel-text="setDialInfo.confirmBtnText"
			:content="'请确认是否转出此订单，当前等级每天还可免费转出' + transferNum + '单！'" @confirm="transferConfirm">
		</u-modal>
		<custom-modal ref="orderShow" :config="config" @confirm="confirmRobOrder"></custom-modal>
	</view>
</template>
<script>
	const amapFile = require('@/wx-jdk/amap-wx.js'); //如：..­/..­/libs/amap-wx.js
	import JSONbig from 'json-bigint';
	import { mapState	} from 'vuex';
	export default {
		data() {
			return {
				statusName: {
					1: '未付款',
					2: '待处理',
					3: '待自取',
					4: '待配送',
					5: '配送中',
					6: '已完成',
					7: '售后处理中',
					9: '售后处理完成',
					10: '已取消(未支付)',
					11: '已取消(已支付)'
				},
				refundStatusName: {
					1: '退款申请已提交',
					2: '等待商家处理',
					3: '商家拒绝退款',
					4: '等待平台处理',
					5: '平台拒绝退款，退款已关闭',
					6: '退款已关闭',
					7: '退款成功',
				},
				refundReasonName: {
					1: '信息填写错误',
					2: '送达时间选错了',
					3: '买错了/买少了',
					4: '商家缺货',
					5: '商家联系我取消',
					6: '配送太慢',
					7: '骑手联系我取消',
					8: '我不想要了',
					9: '商家通知我卖完了',
					10: '商家沟通态度差',
					11: '骑手沟通态度差',
					12: '送太慢了，等太久了',
					13: '商品撒漏/包装破损',
					14: '商家少送商品',
					15: '商家送错商品',
					16: '口味不佳/个人感受不好',
					17: '餐品内有异物',
					18: '食用后引起身体不适',
					19: '商品变质/有异味',
					20: '其他'
				},
				scale: 13,
				center: {
					lng: 116.434307,
					lat: 39.90909
				},
				markers: [
					{
						id: 0,
						iconPath: '../../static/icon/start.png',
						latitude: 39.989643,
						longitude: 116.481028,
						width: 23,
						height: 33
					}, {
						id: 1,
						iconPath: '../../static/icon/end.png',
						latitude: 39.90816,
						longitude: 116.434446,
						width: 24,
						height: 34
					}, {
						id: 2,
						iconPath: '../../static/icon/location.png',
						latitude: 39.90816,
						longitude: 116.434446,
						width: 30,
						height: 30
					}
				],
				polyline: [],
				hours: new Date().getHours(),
				orderInfo: null,
				distance: 0,
				buttonName: {
					4: '',
					5: '',
					6: '',
					7: '处理退款',
					9: '',
					11: '',
					// 5: '已完成'
				},
				backgroundColor: {
					1: 'linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))',
					2: '#48B36B',
					3: '#48B36B',
					4: '#2a75ed',
					5: '#999',
					7: 'linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))',
				},
				numList: [{
						name: '抢单'
					},
					{
						name: '到店'
					},
					{
						name: '取货'
					},
					{
						name: '送达'
					}
				],
				map: null,
				wxmap: null,
				searchObj: null,
				startPiot: null,
				endPiot: null,
				id: '',
				subNVue: null,
				toTheStrot: null,
				// 拨打电话弹窗配置
				telShow: false,
				transferShow: false,
				transferNum: 0,
				setDialInfo: {
					title: '是否拨打电话',
					tel: '',
					type: 1,
					confirmBtnText: '确认拨打'
				},
				// 订单操作弹窗参数配置
				config: {
					icon: '',
					title: '',
					topBtnText: '确认送达',
					topBtnBgColor: 'linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))',
					botBtnText: '取消',
					botBtnBgColor: 'transparent'
				},
				refreshLocationTimer: null
			};
		},
		onLoad(e) {
			this.map = uni.createMapContext('MyMapView');
			// 获取当前地图实例
			this.wxmap = new amapFile.AMapWX({
				key: '83d7fa2e18e86ed91f380517a21ace33'
			});
			this.id = e.id;
			// 获取订单详情
			this.getOrderInfo(e.id);
			// 绑定弹窗确认回调方法
			uni.$on('callback', this.submitTwo);

			// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
			// 获取弹窗体
			this.subNVue = uni.getSubNVueById('popID');
			this.toTheStrot = uni.getSubNVueById('tothestore');
			// #endif
			this.transferNum =this.configInfo.riderSet?(  this.configInfo.riderSet.transferNum || 0):0 //转单次数
			uni.$emit('RefreshConfigInfo');
		},
		onUnload() {
			console.log('orderDetails onUnload')
			uni.$off('callback');
			clearInterval(this.refreshLocationTimer);
		},
		onShow() {
			console.log('orderDetails onShow')
			this.refreshLocationTimer = setInterval(() => {
				this.markers[2].latitude = this.address.lat;
				this.markers[2].longitude = this.address.lng;
			}, 3000);
		},
		computed: {
			...mapState(['UserInfo', 'address', 'configInfo'])
		},
		methods: {
			// 获取订单详情
			getOrderInfo(id) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				this.$request({
					url: '/api-order/rest/merchant/order/selectById',
					data: {
						id
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
					res = JSONbig.parse(res)
					// #endif
					if (res.code === 200) {
						this.orderInfo = res.data;
						setTimeout(() => {
							uni.hideLoading();
						}, 1000);
						// 调用定图规划路线
						// this.getMapInfo();
						this.ghlx()
					} else {
						this.$interactive.ShowToast({
								title: res.message
							},
							false
						);
					}
				});
			},
			//
			ghlx() {
				const that = this
				console.log("ghlx")
				// 获取骑行路线
				const shopLatitude = parseFloat(this.orderInfo.shopLatitude).toFixed(6);
				this.markers[0].latitude = shopLatitude;
				const shopLongitude = parseFloat(this.orderInfo.shopLongitude).toFixed(6);
				this.markers[0].longitude = shopLongitude;
				const contactLatitude = parseFloat(this.orderInfo.contactLatitude).toFixed(6);
				this.markers[1].latitude = contactLatitude;
				const contactLongitude = parseFloat(this.orderInfo.contactLongitude).toFixed(6);
				this.markers[1].longitude = contactLongitude;
				console.log('wxmap:', this.wxmap)
				this.wxmap.getRidingRoute({
					origin: [shopLongitude, shopLatitude].toString(),
					destination: [contactLongitude, contactLatitude].toString(),
					success: (data) => {
						var points = [];
						if (data.paths && data.paths[0] && data.paths[0].steps) {
							const path = data.paths[0];
							this.distance = (path.distance/1000).toFixed(1)
							var steps = path.steps;
							for (var i = 0; i < steps.length; i++) {
								const step = steps[i];
								var poLen = step.polyline.split(';');
								for (var j = 0; j < poLen.length; j++) {
									points.push({
										longitude: parseFloat(poLen[j].split(',')[0]),
										latitude: parseFloat(poLen[j].split(',')[1])
									})
								}
							}
						}

						const centData = parseInt(points.length / 2)

						console.log('centData:', centData)
						console.log('points:', points)
						this.center = {
							lng: points[centData].longitude,
							lat: points[centData].latitude
						}
						console.log("this.center:", this.center)
						this.polyline = [{
							points: points,
							color: "#6BB64C",
							arrowLine: true,
							width: 10,
						}]
					},
					fail: (error) => {
						console.log(error)
					}
				})
			},

			// 获取地图实例，操作地图生成路线
			getMapInfo() {
				// 创建一个搜索对象 map 绑定的当前地图实例
				this.searchObj = new plus.maps.Search(this.map);
				// 开始点
				this.startPiot = new plus.maps.Point(parseFloat(this.orderInfo.shopLongitude), parseFloat(this.orderInfo.shopLatitude));
				// 结束点
				this.endPiot = new plus.maps.Point(parseFloat(this.orderInfo.contactLongitude), parseFloat(this.orderInfo.contactLatitude));
				// 策略
				this.searchObj.setDrivingPolicy(plus.maps.DRIVING_NO_EXPRESSWAY);
				// 路线检索
				this.searchObj.walkingSearch(this.startPiot, this.address.city, this.endPiot, this.address.city);
				// 检索成功回调函数返回检索的路线信息
				this.searchObj.onRouteSearchComplete = (status, result) => {
					// status = 0 表示检索成功
					console.log(state)
					if (state == 0) {
						console.log(result)
						if (result.routeNumber > 0) {
							for (let i = 0; i < result.routeNumber; i++) {
								setTimeout(() => {
									// 把当前路线规划信息插入到地图实例里面，显示视图
									console.log(result.getRoute(i))
									this.map.$getAppMap().addOverlay(result.getRoute(i));
								}, 100);
							}
						} else {
							this.$interactive.ShowToast({
									title: '没有检测到路线！'
								},
								false
							);
						}
						// 起始点距离计算
						plus.maps.Map.calculateDistance(
							this.startPiot,
							this.endPiot,
							e => {
								this.distance = (e.distance / 1000).toFixed(1);
							},
							err => {
								console.log(err);
							}
						);
					} else {
						switch (status) {
							case 3003:
								this.$interactive.ShowToast({
										title: '距离过长，没有推荐路线！'
									},
									false
								);
								break;
							default:
								break;
						}
					}
				};
			},
			// 订单操作
			submit() {

			},
			// the same function as the index.vue
			confirmRobOrder() {

			},
			// 订单操作two
			submitTwo(msg, type) {

			},
			// 打开地图导航
			handNavigation(type) {
				if (this.UserInfo.auditStatus == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast({
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast({
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				// uni.getLocation({
				// 	type: 'wgs84',
				// 	geocode: true,
				// 	complete: msg => {
				// 		if (msg.errMsg == 'getLocation:ok') {
				// 			// 起始点
				// 			let startPiot = new plus.maps.Point(parseFloat(msg.longitude), parseFloat(msg
				// 				.latitude));
				// 			// 终止点
				// 			let endPiot;
				// 			if (type == 1) {
				// 				endPiot = new plus.maps.Point(parseFloat(this.orderInfo.shopLongitude),
				// 					parseFloat(this.orderInfo.shopLatitude));
				// 			} else if (type == 2) {
				// 				endPiot = new plus.maps.Point(parseFloat(this.orderInfo.contactLongitude),
				// 					parseFloat(this.orderInfo.contactLatitude));
				// 			}
				// 			// 导航
				// 			plus.maps.openSysMap(endPiot, type == 1 ? this.orderInfo.startDetail : this
				// 				.orderInfo.endDetail, endPiot);
				// 		}
				// 	}
				// });
				let lat;
				let lng;
				let addr;
				if (type == 1) { // 取货点
					lat = this.orderInfo.shopLatitude
					lng = this.orderInfo.shopLongitude
					addr = this.orderInfo.startAddress
				} else if (type == 2) { // 配送点
					lat = this.orderInfo.contactLatitude
					lng = this.orderInfo.contactLongitude
					addr = this.orderInfo.endAddress
				}
				uni.openLocation({
					latitude: +lat,
					longitude: +lng,
					address: addr,
					success: res =>{
						// console.log(res)
					},
					fail: err =>{
						// console.log(err)
					}
				});


			},
			// 拨号
			/**
			 * state订单状态 1抢单，2已接单待到店 3已到店，待取货，4已取货，代配送
			 * */
			setDial() {
				if (this.UserInfo.auditStatus == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast({
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast({
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				const phone = uni.getSubNVueById('modalID');
				// #endif
				this.setDialInfo.type = 1;
				if (this.orderInfo.status == 2 || this.orderInfo.status == 3) {
					this.setDialInfo.title = '是否联系商家';
				} else if (this.orderInfo.status == 4) {
					this.setDialInfo.title = '是否联系用户';
				}
				if (this.orderInfo.status == 2 || this.orderInfo.status == 3) {
					this.setDialInfo.tel = this.orderInfo.startTel;
				} else if (this.orderInfo.status == 4) {
					this.setDialInfo.tel = this.orderInfo.contactPhone;
				}
				this.setDialInfo.confirmBtnText = '确认拨打';
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				phone.show('zoom-out', 300);
				uni.$emit('modalPhone', this.setDialInfo);
				// #endif
				this.telShow = true
			},
			// 拨号
			/**
			 * state订单状态 1抢单，2已接单待到店 3已到店，待取货，4已取货，代配送
			 * */
			setDialToRider() {
				if (this.UserInfo.auditStatus == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast({
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast({
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				const phone = uni.getSubNVueById('modalID');
				// #endif
				this.setDialInfo.type = 1;
				this.setDialInfo.title = '是否联系骑手';
				this.setDialInfo.tel = this.orderInfo.riderMobile;
				this.setDialInfo.confirmBtnText = '确认拨打';
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				phone.show('zoom-out', 300);
				uni.$emit('modalPhone', this.setDialInfo);
				// #endif
				this.telShow = true
			},
			// 拨号
			/**
			 * state订单状态 1抢单，2已接单待到店 3已到店，待取货，4已取货，代配送
			 * */
			setDialToUser() {
				if (this.UserInfo.auditStatus == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast({
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast({
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				const phone = uni.getSubNVueById('modalID');
				// #endif
				this.setDialInfo.type = 1;
				this.setDialInfo.title = '是否联系用户';
				this.setDialInfo.tel = this.orderInfo.contactPhone;
				this.setDialInfo.confirmBtnText = '确认拨打';
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				phone.show('zoom-out', 300);
				uni.$emit('modalPhone', this.setDialInfo);
				// #endif
				this.telShow = true
			},
			// 转单
			handTranOrder() {
				if (this.UserInfo.auditStatus == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast({
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast({
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				const phone = uni.getSubNVueById('modalID');
				// #endif
				this.setDialInfo.title = '是否转出此单？';
				this.setDialInfo.type = 2;
				this.setDialInfo.confirmBtnText = '我再想想';
				// #ifdef APP-NVUE || APP-PLUS || APP-PLUS-NVUE
				phone.show('zoom-out', 300);
				uni.$emit('modalPhone', this.setDialInfo);
				// #endif
				this.transferShow = true
			},
			// 订单操作
			agreeRefund(item) {
				if (parseInt(this.UserInfo.auditStatus) == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast(
					// 		{
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast(
						{
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				
				uni.showModal({
					title: '提示',
					content: '确定同意退款吗?',
					confirmText: '确定',
					cancelText: '取消',
					confirmColor: '#FF0000', // 确定按钮颜色（可选）
					success: (res) => {
					  if (res.confirm) { // 用户点击确定
							let data = {
								id: item.id,
								status: 1
							};
							let url = '/api-order/rest/merchant/order/auditAfterSalesOrder';
							this.$request({ url: url, data: data }).then(res => {
								// #ifdef MP-WEIXIN
								res = JSON.parse(res)
								// #endif
								if (res.code === 200) {
									this.getOrderInfo(item.id);
									uni.hideLoading();
								} else {
									this.$interactive .ShowToast({ title: res.message }) .then(() => {
										uni.hideLoading();
									});
								}
							});
					  } else if (res.cancel) { // 用户点击取消
					  }
					}
				});
			},
			// 订单操作
			disagreeRefund(item) {
				if (parseInt(this.UserInfo.auditStatus) == 2) {
					// if (this.UserInfo.isWork != '1') {
					// 	this.$interactive.ShowToast(
					// 		{
					// 			title: '请先上线！'
					// 		},
					// 		false
					// 	);
					// 	return;
					// }
				} else {
					this.$interactive.ShowToast(
						{
							title: '请先进行实名认证！'
						},
						false
					);
					return;
				}
				
				uni.showModal({
					title: '提示',
					content: '确定拒绝退款吗?',
					confirmText: '确定',
					cancelText: '取消',
					confirmColor: '#FF0000', // 确定按钮颜色（可选）
					success: (res) => {
					  if (res.confirm) { // 用户点击确定
							let data = {
								id: item.id,
								status: 2,
								opinion: "拒绝退款",
							};
							let url = '/api-order/rest/merchant/order/auditAfterSalesOrder';
							this.$request({ url: url, data: data }).then(res => {
								// #ifdef MP-WEIXIN
								res = JSON.parse(res)
								// #endif
								if (res.code === 200) {
									this.getOrderInfo(item.id);
									uni.hideLoading();
								} else {
									this.$interactive .ShowToast({ title: res.message }) .then(() => {
										uni.hideLoading();
									});
								}
							});
					  } else if (res.cancel) { // 用户点击取消
					  }
					}
				});
			},
			transferConfirm() {
				// for test only
				this.transferNum = 1;
				if(this.transferNum == 0){
					this.$interactive.ShowToast({
							title: '暂无转单次数，无法转出！'
						},
						false
					);
				} else{
					uni.$emit('callback', '', 'transfer');
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.orderDetails {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		padding-bottom: 100rpx;
		position: relative;

		.MyMapView {
			position: relative;
			width: 750rpx;
			height: 600rpx;
			max-height: 600rpx;
			z-index: 1;
		}

		.myMaptop {
			height: 0rpx;
		}

		.Navigation {
			position: relative;
			background-color: blue;
			color: #ffffff;
			text-align: center;
			padding: 30rpx 30rpx;
			width: 200rpx;
			margin: 30rpx auto;
			border-radius: 10rpx;
		}

		.cont-dom {
			.contInfo-title-box {
				padding: 20rpx 10rpx 20rpx 32rpx;

				.contInfo-title {
					flex: 1;
					padding: 0 30rpx;

					.contTitle {
						color: #333;
						font-weight: bold;
						font-size: 32rpx;

						.contTitle-label {
							font-size: 24rpx;
							color: #666;
							font-weight: 400;
							padding: 5rpx 0;
						}
					}

					.contTitle-money {
						color: #e44c4c;
						font-weight: bold;
						font-size: 32rpx;
						margin-bottom: 32rpx;
					}
				}
			}

			.cont-title-box {
				position: relative;
				padding: 0rpx 32rpx;

				.title-box-address {
					position: relative;
					padding: 20rpx 0;
					min-height: 134rpx;

					.cont-title-icons {
						font-size: 24rpx;
						color: #ffffff;
						font-weight: 400;
						width: 40rpx;
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						background: #f9833b;
						vertical-align: middle;
						border-radius: 50%;
						margin-top: 14rpx;
					}

					.cont-title-icons-bg {
						background-color: #1bb367;
					}

					.cont-title {
						flex: 1;
						padding-left: 20rpx;

						.cont-title-name {
							color: $font-my-color-3;
							padding: 10rpx 0;
						}

						.cont-title-label {
							font-size: $font-my-size-28;
							color: $font-my-color-6;
							font-weight: 400;
						}
					}

					.cont-info {
						font-size: $font-my-size-24;
						position: absolute;
						bottom: -5rpx;
						width: 40rpx;
						text-align: center;

						&::after {
							display: block;
							font-size: $font-my-size-24;
							padding: 0rpx;
							margin: 0rpx;
							font-weight: 400;
							color: #666;
							line-height: 24rpx;
							content: 'km';
						}
					}

					.navigation {
						min-width: 100rpx;
						text-align: center;
						padding: 10rpx 0;
					}
				}
			}

			.contTitle-info {
				font-weight: bold;
				font-size: 32rpx;
			}

			/deep/.border-none {
				&::after {
					border: none;
				}
			}

			.cont-title {
				font-size: 32rpx;
				font-weight: bold;
			}

			.styltwo {
				&::before {
					content: '送';
					background: #1bb367;
				}
			}

			// 提示文字
			.label-cont {
				padding-left: 60rpx;
			}

			// 商品清单
			.shopInfo {
				font-size: 36rpx;
				font-weight: bold;
				color: $font-my-color-3;
			}

			.shopInfo-label {
				width: 686rpx;
				min-width: 686rpx;
				max-width: 686rpx;
				padding: 8rpx 0;
				font-size: 30rpx;
				color: $font-my-color-3;

				.list-cont {
					flex: 1;
					text-align: right;
					color: $font-my-color-3;
				}
			}

			.remarks {
				background: $uni-bg-color-grey;
				color: #666;
				padding: 10rpx 32rpx 10rpx;
				margin: 30rpx 0rpx 0;
				border-radius: 10rpx;
				letter-spacing: 3rpx;
				font-size: 24rpx;
			}

			.trip-info-styl {
				text-align: center;
				background: #f7f7f7;
				border-radius: 8rpx;
				font-size: 22rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #999999;
			}

			.footerbtn {
				width: 686rpx;
				background: #fec246;
				border-radius: 45rpx;
				font-size: 32rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #784007;
				outline: none;
				border: none;
			}
		}

		.footer-btn {
			position: fixed;
			z-index: 2;
			bottom: 0rpx;
			left: 0rpx;
			right: 0rpx;
			padding: 10rpx 32rpx 10rpx;
			box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
			background: #fff;

			.footer-btn-icon {
				width: 360rpx;
				padding-right: 32rpx;
				
				.icon-btn {
					text-align: center;
					height: 82rpx;
					// padding-top: 40rpx;
					padding-bottom: 10rpx;
					padding-left: 10rpx;
					padding-right: 60rpx;
					.icon-pic {
						display: block;
						height: 42rpx;
						line-height: 42rpx;
					}
				}
			}
		}
	}
	.msg2 {
		width: 90%;
		padding:20rpx 30rpx;
		border-radius: 10rpx;
		background-color: #f5f5f5;
		margin: auto;
		margin-top: 20rpx;
		color: #fa000a;
		font-weight: 700;
		font-size: 30rpx;
	}
</style>
