<template>
	<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false }">
		<view class="monthDay">
			<template v-for="(item, index) of dataList">
				<view class="list-data" :key="index">
					<view class="list-title">
						<text class="title-main">{{ item.mounth }}月</text>
						<text class="title-label">
							{{ item.mounth >= 10 ? item.mounth + '-01' : '0' + item.mounth + '-01' }}~{{
								item.mounth >= 10 ? item.mounth + '-' + item.days : '0' + item.mounth + '-' + item.days
							}}
						</text>
					</view>
					<view class="list-cont flex flex-align-center justify-space-between">
						<view class="list-cont-ls">
							<view class="list-cont-ls-title">完成单量(单)</view>
							<text class="list-cont-ls-num">{{ item.completeNum }}</text>
						</view>
						<view class="list-cont-ls">
							<view class="list-cont-ls-title">行驶里程(Km)</view>
							<text class="list-cont-ls-num">{{ item.distance }}</text>
						</view>
						<view class="list-cont-ls">
							<view class="list-cont-ls-title">所得收入(元)</view>
							<text class="list-cont-ls-num">{{ item.revenue }}</text>
						</view>
					</view>
				</view>
			</template>
		</view>
	</mescroll-uni>
</template>

<script>
export default {
	data() {
		return {
			dataList: []
		};
	},
	methods: {
		// 初始化
		mescrollInit() {},
		// 下拉刷新
		downCallback(e) {
			e.resetUpScroll();
		},
		// 上拉加载
		upCallback(msg) {
			this.getMonthOrderlist(msg.num);
		},
		getMonthOrderlist(msg) {
			this.$request({
				url: '/rider-bill/month-quantity',
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					if (msg == 1) {
						this.dataList = res.data;
					} else {
						Object.assign(this.dataList, res.data);
					}
					setTimeout(() => {
						this.mescroll.endSuccess(res.data.length, res.data.length >= 10 ? true : false);
					}, 100);
				} else {
					setTimeout(() => {
						this.mescroll.endSuccess(0, false);
					}, 100);
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
/deep/.mescroll-uni-fixed {
	top: 0rpx !important;
}
.monthDay {
	height: 100%;
	padding: 0rpx 20rpx;
	overflow-y: scroll;

	.list-data {
		background: #ffffff;
		padding: 20rpx 32rpx;
		margin-bottom: 20rpx;

		.list-title {
			.title-main {
				color: #333;
				font-size: $font-my-size-36;
				font-weight: bold;
				vertical-align: bottom;
			}
			.title-label {
				font-size: 28rpx;
				color: #999;
				vertical-align: bottom;
				margin-left: 10rpx;
			}
		}
		.list-cont {
			padding: 20rpx 0;

			.list-cont-ls {
				flex: 1;

				.list-cont-ls-title {
					padding: 10rpx 0;
					font-weight: bold;
					font-size: $font-my-size-30;
				}
				.list-cont-ls-num {
					font-size: 28rpx;
					color: #666;
				}
			}
		}
	}
}
</style>
