<template>
	<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false }">
		<view class="order-list-box">
			<view class="order-list" v-for="(item, index) of orderList" :key="index" @click="routePage(item.id)">
				<view class="order-time flex flex-align-center justify-space-between">
					<view style="flex: 1;">
						<view style="font-weight: bold; margin-right: 10rpx; color: #333;  font-size: 32rpx;">
							<text>#</text>
							<text style="font-size: 52rpx;margin-right: 20rpx;">{{ index + 1 }}</text>
							<text style="font-size: 34rpx;">外卖配送</text>
						</view>
						<!-- <text style="flex: 1;">时间：{{ item.createdAt.substring(11,16) }}</text> -->
						<text>订单号：{{ item.outTradeNo }}</text>
					</view>
					<text :style="{ color: item.status ? stateName[item.status].color : '#999', fontSize: '30rpx' }">{{ item.status ? stateName[item.status].title : '' }}</text>
					<uni-icons type="forward" color="#999"></uni-icons>
				</view>
				<my-line :margin="[10, 0, 0, 0]"></my-line>
				<view class="flex flex-align-center">
					<view class="order-list-info">
						<view class="order-info-name">
							<view class="flex flex-align-center">
								<section class="spot"></section>
								<text class="order-info-name-title">{{ item.shopName }}</text>
							</view>
						</view>
						<view class="order-info-name">
							<view class="flex flex-align-center">
								<section class="spot spot-t"></section>
								<text class="order-info-name-title">{{ item.endAddress }}</text>
							</view>
							<view class="trips">{{ item.endUsername + '：' + item.endTel && item.endTel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || '' }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</mescroll-uni>
</template>

<script>
export default {
	props: {
		state: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			orderList: [],
			time: this.$moment(new Date()).format('YYYY-MM-DD'),
			stateName: {
				1: {
					color: '#ec1613',
					title: '待接单'
				},
				2: {
					color: '#48b36b',
					title: '待到店'
				},
				3: {
					color: '#48b36b',
					title: '待取货'
				},
				4: {
					color: '#2196f3',
					title: '配送中'
				},
				5: {
					color: '#999',
					title: '已完成'
				},
				6: {
					color: '#ec1613',
					title: '取消'
				}
			}
		};
	},
	methods: {
		// 初始化
		mescrollInit() {},
		// 下拉刷新
		downCallback(e) {
			e.resetUpScroll();
		},
		// 上拉加载
		upCallback(msg) {
			this.getTodayList(msg.num);
		},
		changeTime(msg) {
			this.time = msg;
			this.getTodayList(1);
		},
		getTodayList(msg) {
			let status = this.state;
			// todo: state mapping
			if (this.state === '0') {
				status = null;
			}
			this.$request({
				url: '/api-order/rest/merchant/order/list',
				data: {
					// state: this.state,
					status,
					pageNo: msg,
					pageSize: 10,
					queryDate: this.time
				}
			}).then(result => {
				// #ifdef MP-WEIXIN
					const res = JSON.parse(result)
				// #endif
				if (res.code === 200) {
					if (msg === 1) {
						this.orderList = res.data.records;
					} else {
						Object.assign(this.orderList, res.data.records);
					}
					setTimeout(() => {
						this.mescroll.endSuccess(res.data.total, res.data.total >= 10);
					}, 300);
				} else {
					setTimeout(() => {
						this.mescroll.endSuccess(0, false);
					}, 300);
				}
			});
		},
		routePage(id) {
			uni.navigateTo({
				url: '/pages/detailsPage/orderDetails?id=' + id
			});
		}
	}
};
</script>

<style lang="scss" scoped>
/deep/.mescroll-uni-fixed {
	top: 0rpx !important;
}
.order-list-box {
	padding: 0rpx 20rpx 20rpx;

	.order-list {
		background: #ffffff;
		margin-top: 20rpx;
		border-radius: 10rpx;
		padding: 20rpx 32rpx;

		.order-time {
			padding: 10rpx 0;
			font-size: $font-my-size-24;
			color: $font-my-color-9;
		}

		.order-list-info {
			flex: 1;

			.order-info-name {
				padding-right: 20rpx;

				.order-info-name-title {
					font-size: $font-my-size-32;
					font-weight: bold;
					padding: 14rpx 0;
					color: $font-my-color-3;
				}

				.spot {
					margin-right: 10rpx;
					width: 14rpx;
					min-width: 14rpx;
					height: 14rpx;
					border-radius: 50%;
					background-color: #f9833b;
				}

				.spot-t {
					background-color: #1bb367;
				}

				.trips {
					padding-left: 24rpx;
					color: $font-my-color-9;
					font-size: $font-my-size-24;
				}
			}
		}
	}
}
</style>
