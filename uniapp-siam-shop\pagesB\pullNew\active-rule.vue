<template>
	<view class="active-rule">
		<u-navbar title-size="36" :border-bottom="false" title="活动规则"></u-navbar>
		<view class="active-rule-cont">
			<view class="active-rule-title">奖励规则</view>
			<view class="title-h1 flex flex-align-center">配送中心</view>
			<view class="title-h2">武汉配送中心</view>
			<view class="title-h1 flex flex-align-center">奖励信息</view>
			<view class="text-p">
				1.当前拉新奖励对被推荐人的完单量要求按业务类型区分
				(完成订单量按1:1计算)：（1)拉新活动一（最高完单量为200单），)：（1)拉新活动一（最高完单量为200单），被推荐人的完单业务类型必须为即刻送（急送A），急送B（中小商家）、急送C（不含大网单））；
				(2)拉新活动二（最高完单量为1200单），被推荐人完单业务类型必须为大网单（包含模式二、落地配）；
			</view>
			<view class="text-p">
				<text>2. 社会 兼职骑士</text>
				<br />
				<text style="color: #999999;">活动时间：2020.10.01-2020.10.31</text>
			</view>
			<view class="active-rule-cont-footer">
				<u-row class="active-rule-cont-footer-list">
					<u-col span="4"><view class="table-title">奖励条件</view></u-col>
					<u-col span="4"><view class="table-title">推荐奖励</view></u-col>
					<u-col span="4"><view class="table-title">新人奖励</view></u-col>
				</u-row>
				<u-row class="active-rule-cont-footer-list">
					<u-col span="4"><view class="table-cont">审核通过后10天内完成1 单</view></u-col>
					<u-col span="4"><view class="table-cont">12元</view></u-col>
					<u-col span="4"><view class="table-cont">12元</view></u-col>
				</u-row>
				<u-row class="active-rule-cont-footer-list">
					<u-col span="4"><view class="table-cont">前一任务完成后14天内再 完成60单</view></u-col>
					<u-col span="4"><view class="table-cont">388元</view></u-col>
					<u-col span="4"><view class="table-cont">188元</view></u-col>
				</u-row>
				<u-row class="active-rule-cont-footer-list">
					<u-col span="4"><view class="table-cont">前一任务完成后14天内再 完成200单</view></u-col>
					<u-col span="4"><view class="table-cont">688元</view></u-col>
					<u-col span="4"><view class="table-cont">388元</view></u-col>
				</u-row>
			</view>
		</view>
		<view class="active-trip">——活动最终解释权归云贝科技所有——</view>
	</view>
</template>

<script></script>

<style lang="scss" scoped>
.active-rule {
	background: #e94043;
	min-height: 100vh;
	padding: 40rpx 40rpx 0;

	.active-rule-cont {
		background: #ffffff;
		border-radius: 10rpx;
		padding: 60rpx 44rpx;

		.active-rule-title {
			text-align: center;
			font-size: 40rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #fe0000;
			// background: url(../static/image/<EMAIL>) no-repeat;
			// background-size: 250rpx auto;
			// background-position: center;
		}

		.title-h1 {
			font-size: 28rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #333333;
			padding-bottom: 20rpx;

			&::before {
				display: inline-block;
				content: '';
				width: 10rpx;
				height: 10rpx;
				background: #666666;
				border-radius: 50%;
				margin-right: 20rpx;
			}
		}

		.title-h2 {
			font-size: 28rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #666666;
			padding: 0rpx 30rpx 50rpx;
		}

		.text-p {
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #666666;
			line-height: 48rpx;
			padding: 0 30rpx;
		}

		.active-rule-cont-footer {
			background: #fef2f2;
			border-radius: 2rpx;
			padding: 20rpx 16rpx;
			margin-top: 40rpx;

			.active-rule-cont-footer-list {
				padding-bottom: 20rpx;

				.table-title {
					font-size: 24rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #fe0000;
					text-align: center;
				}

				.table-cont {
					font-size: 24rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #666666;
					line-height: 40rpx;
					text-align: center;
				}
			}
		}
	}

	.active-trip {
		font-size: 24rpx;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #ffffff;
		text-align: center;
		padding: 40rpx 0rpx;
	}
}
</style>
