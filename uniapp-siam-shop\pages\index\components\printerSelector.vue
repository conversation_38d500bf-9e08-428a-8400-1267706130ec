<template>
  <u-popup v-model="show" mode="bottom" height="60%" :mask-close-able="false">
    <view class="printer-main">
      <view class="printer-title">
        <view class="printer-title-actions">
          <text class="cancel-btn" @click="close">取消</text>
          <text class="title-text">选择打印机</text>
          <text class="confirm-btn" @click="confirm">确定</text>
        </view>
      </view>
      <view class="printer-list-container">
        <view class="printer-list">
          <view
            v-for="printer in printerList"
            :key="printer.id"
            class="printer-item"
            :class="{ selected: selectedPrinter && selectedPrinter.id === printer.id  }"
            @click="selectPrinter(printer)"
          >
            <view class="printer-info">
              <text class="printer-name">{{ printer.name }}</text>
            </view>
          </view>
          <view v-if="printerList.length === 0" class="no-printer">
            暂无打印机
          </view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    currentPrinter: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      show: false,
      printerList: [],
      selectedPrinter: {},
    };
  },
  watch: {
    value(newVal) {
      this.show = newVal;
      if (newVal) {
        this.loadPrinterList();
      }
    },
	currentPrinter(newVal) {
		console.log('===============', newVal)
		this.selectedPrinter = newVal;
	}
  },
  methods: {
    loadPrinterList() {
      this.$request({
        url: "/api-util/rest/merchant/printer/list",
        data: {
          pageNo: 1,
          pageSize: 2000,
          type: 1,
        },
      }).then((res) => {
        // #ifdef MP-WEIXIN
        // res = JSON.parse(res);
        // #endif
        if (res.code === 200) {
          this.printerList = res.data.records || [];
        }
      });
    },
    selectPrinter(printer) {
      this.selectedPrinter = printer;
    },
    confirm() {
      this.$emit("confirm", this.selectedPrinter);
      this.close();
    },
    close() {
      this.show = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.printer-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .printer-title {
    padding: 30rpx 0;
    background: #ffffff;
    text-align: center;
    font-size: $font-my-size-34;
    position: relative;
    
    .printer-title-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 32rpx;
      
      .title-text {
        font-size: $font-my-size-34;
        font-weight: bold;
      }
      
      .confirm-btn,
      .cancel-btn {
        font-size: $font-my-size-28;
        padding: 10rpx 20rpx;
        background: none;
        border: none;
      }
      
      .confirm-btn {
        color: #fa7c25;
      }
      
      .cancel-btn {
        color: $font-my-color-9;
      }
    }
  }

  .printer-list-container {
    flex: 1;
    overflow: hidden;
    
    .printer-list {
      height: 100%;
      overflow-y: auto;
      padding: 10rpx 0;

      .printer-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 32rpx;
        border-bottom: 1rpx solid #f5f5f5;

        &.selected {
          background-color: #fffaf5;
        }

        .printer-info {
          .printer-name {
            font-size: $font-my-size-30;
            color: $font-my-color-3;
            font-weight: bold;
            display: block;
            margin-bottom: 10rpx;
          }

          .printer-model {
            font-size: $font-my-size-26;
            color: $font-my-color-9;
          }
        }

        .printer-status {
          .status-text {
            font-size: $font-my-size-26;
            padding: 5rpx 15rpx;
            border-radius: 8rpx;
          }

          .status-online {
            color: #07c160;
            background-color: #f0fff6;
          }

          .status-offline {
            color: #999;
            background-color: #f5f5f5;
          }
        }
      }

      .no-printer {
        text-align: center;
        padding: 50rpx 0;
        color: $font-my-color-9;
        font-size: $font-my-size-28;
      }
    }
  }
}
</style>