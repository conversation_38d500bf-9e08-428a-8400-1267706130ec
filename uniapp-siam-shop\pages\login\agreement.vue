<template>
	<view class="agreement">
		<view class="agreement-cont">

			<div class="Section0" style="layout-grid: 15.6pt">
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">温馨提醒：</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">欢迎您使用</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 宋体;
			            mso-ascii-font-family: 'Segoe UI';
			            mso-hansi-font-family: 'Segoe UI';
			            mso-bidi-font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="宋体">湖南易达脉联电子商务有限公司开发的</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">外卖接单商家版提供的技术服务，在您使用外卖接单商家版平台技术服务之前，您应当认真阅读并遵守《外卖接单商家版服务协议》（以下简称</font>
						<font face="Segoe UI">“本协议”），请您务必审慎阅读、充分理解各条款内容，特别是免除或者限制责任的条款。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">
							当您按照外卖接单商家版发布者入驻申请页面提示填写信息、阅读并同意本协议、提交入驻申请资料，或您以其他外卖接单商家版允许的方式实际使用外卖接单商家版平台技术服务时，即表示您已充分阅读、理解并接受本协议的全部内容，并与外卖接单商家版达成协议。您承诺接受并遵守本协议的约定，届时您不应以未阅读本协议的内容或者未获得外卖接单商家版对您问询的解答等理由，主张本协议无效，或要求撤销本协议。
						</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">您点击接受本协议即意味着您同意受本协议约束。如果您不同意本协议的任何内容，或者无法准确理解条款的含义，请不要进行后续操作。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">外卖接单商家版发布者服务协议</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">您同意根据指示以电子签约方式完成本协议签署，您在外卖接单商家版平台网络页面（或外卖接单商家版</font>
						APP）申请入驻、点击签署本协议、提交资料，或者您已经使用外卖接单商家版平台技术服务，即表示您同意接受本协议的全部内容，本协议即在您与外卖接单商家版之间发生法律效力。
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h3 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 13.5pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第一条：定义</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 13.5pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">外卖接单商家版平台技术服务，指由外卖接单商家版及</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 宋体;
			            mso-ascii-font-family: 'Segoe UI';
			            mso-hansi-font-family: 'Segoe UI';
			            mso-bidi-font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="宋体">湖南易达脉联电子商务有限公司</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">基于外卖接单商家版平台旨在为外卖接单商家版发布者提供的互联网、移动互联网信息服务以及相关客户端软件（如</font>
						<font face="Segoe UI">“外卖接单商家版APP”）技术支持服务（包括但不限于发布者发布、预订等），以上服务在本协议中统称“外卖接单商家版服务”。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">外卖接单商家版发布者规则（以下简称</font>
						<font face="Segoe UI">
							“发布者规则”），指外卖接单商家版在外卖接单商家版平台或外卖接单商家版其他渠道上已发布或将来发布的适用于发布者且发布者应遵守的各类规则及/或规则条款的统称，本协议中统称“发布者规则”，发布者规则是本协议的重要组成部分，外卖接单商家版有权不时调整。
						</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">“发布者”指于外卖接单商家版平台上使用外卖接单商家版服务并提供的法律实体，本协议可简称“您”。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">“用户”指于外卖接单商家版平台上发布者提供的对象。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第二条：协议内容及效力</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">
							本协议内容包括协议正文及所有外卖接单商家版已经发布的或将来可能发布的发布者规则。所有发布者规则为本协议不可分割的一部分，与协议正文具有同等法律效力。您在使用外卖接单商家版提供时，承诺接受并遵守各项相关发布者规则的规定。
						</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">
							外卖接单商家版有权根据需要不时地修改本协议或制定、修改各类发布者规则，并以外卖接单商家版平台公示的方式或其他合理方式进行变更公告，无需另行单独通知。任何对本协议的修订和对发布者规则的制定或修改一经公布即构成协议各方权利义务的补充，成为本协议的一部分。如您不同意相关变更，须立即以书面通知的方式终止本协议，并立即停止使用外卖接单商家版服务。登录或继续使用外卖接单商家版服务将表示您接受经修订的协议或发布者规则。除另行明确声明或另行签署协议外，任何扩大的服务范围或软件功能的增强均受本协议约束。
						</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第三条：发布者的陈述和保证</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">发布者在外卖接单商家版服务使用过程中，做出如下陈述与保证：</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.1</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者如需使用外卖接单商家版服务，应符合发布者规则的要求，并且在今后使用外卖接单商家版服务的过程中将保持一直符合发布者规则的要求。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.2</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者承诺其向外卖接单商家版提交的所有材料（包括但不限于个人基本信息、信息、优惠信息）真实、准确、有效，并且不侵犯任何人的权利。若发布者提供的信息不真实、不合法、不详尽的，其需承担因此引起的相应责任和后果。若外卖接单商家版要求发布者提供相关证明文件，发布者需要按照要求提供。在相关信息发生实际变更时，发布者需及时进行更新。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.3</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者承诺其向用户提供的服务符合《中华人民共和国食品安全法》，其他国家法律、法规、部门规章以及国家强制性标准。发布者需提供有效期内的健康证。任何用户或其他人士因使用发布者提供的服务造成其人身、财产等方面损失的，发布者应负责赔偿并使外卖接单商家版免责。外卖接单商家版如因此而遭受损失的，外卖接单商家版有权进行追偿。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.4</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者承诺不在外卖接单商家版平台上从事与提供服务无关的行为，不会利用外卖接单商家版服务开展或协助开展赌博、色情、暴力、洗钱、偷税、毒品、诈骗等不合法、不健康内容的业务。发布者不得以虚假交易、虚假结算、虚增交易金额等方式为用户或发布者增加积分、骗取包括但不限于评价等。若在外卖接单商家版平台上发布任何不符合本协议要求的信息，发布者承诺立即将其删除。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.5</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者明示只接受外卖接单商家版平台指定的支付方式作为其在通过外卖接单商家版提供服务过程中可能产生的费用的支付工具，外卖接单商家版平台可以根据需要不断变更支付方式。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.6</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者同意不对外卖接单商家版上任何数据作商业性利用，包括但不限于在未经外卖接单商家版事先书面批准的情况下，以复制、传播或向他方披露等方式使用在外卖接单商家版平台上其他用户展示的任何资料。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">3.7</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者同意在使用外卖接单商家版服务的同时接受外卖接单商家版平台向其登记的电子邮件、手机、通信地址发送各类信息服务，包括广告信息。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第四条：上门服务者的保证</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">
							上门服务提供者必须保证其自身符合国家法律法规的所有要求，其员工在上门服务时的所有行为与后果由服务提供者承担。如外卖接单因其上门服务遭受诉讼或其它处罚的，所有处罚及外卖接单因诉讼及处罚而发生的费用（包括但不限于律师费、诉讼费、交通费）都由服务提供者承担。
						</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第五条：外卖接单商家版的权利</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">5.1</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;外卖接单商家版有权不定期对发布者提供的情况进行抽查，并接受媒体监督。如一旦经抽查发现或经举报证实发布者提供的情况不符合发布者规则或相关法律法规要求的，外卖接单商家版有权采取任何必要的处罚措施，包括但不限于：删除发布的内容，关闭厨房。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">5.2</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;外卖接单商家版可将发布者发布的信息在外卖接单商家版平台和外卖接单商家版合作渠道进行推广宣传，以增加用户对发布者的认知度。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">5.3</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;如发布者发布含有任何违反法律、法规、本协议约定的信息或链接的，外卖接单商家版有权进行屏蔽或删除等适当的处理。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">5.4</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;为了保障发布者权益，在发现发布者账号、账户、终端设备存在异常活动时，外卖接单商家版有权暂停服务的提供。发布者违反本协议约定的，外卖接单商家版也可以单方暂停或终止提供服务。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">5.5</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;若发布者未能依照承诺的服务内容为用户提供服务或优惠权益，或违反本协议其他约定的，将极大伤害外卖接单商家版和发布者的信誉。外卖接单商家版有权在外卖接单商家版平台、相关网络页面或通过其他方式公开发布者的行为，同时外卖接单商家版有权自行向用户支付同等价值的优惠或费用作为补救。发布者应当承担外卖接单商家版因补救行为产生的全部费用，包括但不限于采购费用、邮寄费用、补偿费用以及为挽回企业声誉和形象支出的宣传费用等。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">5.6</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;外卖接单商家版有权根据其经营状况自行决定终止向全部或部分发布者提供服务而无需承担违约责任，届时外卖接单商家版将通过外卖接单商家版平台、相关网络页面发布通知或其他方式通知发布者。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 宋体;
			              font-weight: bold;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<font face="宋体">第六条：有限责任</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 宋体;
			              font-weight: bold;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 0pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-pagination: widow-orphan;
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: Calibri;
			              mso-fareast-font-family: 宋体;
			              mso-bidi-font-family: 'Times New Roman';
			              font-weight: bold;
			            ">6.1</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">&nbsp;<font face="宋体">外卖接单商家版仅为用户和发布者进行交易提供相关信息服务和技术服务，外卖接单商家版并非用户和发布者之间服务的参与者。外卖接单商家版不对发布者和</font>
						<font face="Calibri">/</font>
						<font face="宋体">或用户行为的合法性、有效性，发布者服务的真实性、合法性和有效性作任何明示或暗示的担保，也不承担任何责任。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 0pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-pagination: widow-orphan;
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: Calibri;
			              mso-fareast-font-family: 宋体;
			              mso-bidi-font-family: 'Times New Roman';
			              font-weight: bold;
			            ">6.2</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">&nbsp;<font face="宋体">
							发布者可以自行聘请第三方为其进行管理账号、发布信息、发布优惠权益、提供服务等操作，该等第三方使用发布者账号、账户的行为后果应由发布者承担。外卖接单商家版可能对第三方的信息进行披露，以便有需要的发布者可以从该等第三方获得所需要的服务，但是外卖接单商家版不对发布者与第三方发生的纠纷、争议和后果负责。发布者如因使用第三方服务而遭受损失的，应由发布者或第三方依照其协议约定承担和负责，外卖接单商家版不负责赔偿。
						</font></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 0pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-pagination: widow-orphan;
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: Calibri;
			              mso-fareast-font-family: 宋体;
			              mso-bidi-font-family: 'Times New Roman';
			              font-weight: bold;
			            ">6.3</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">&nbsp;<font face="宋体">
							为提供更丰富的服务，外卖接单商家版服务可能为发布者提供同时安装第三方软件的选择。发布者可选择是否同时安装第三方软件。安装第三方软件后，如果发布者不需要第三方软件，可以从系统管理选项中删除第三方软件。外卖接单商家版不承担由第三方软件引起的直接和间接责任。
						</font></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 0pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-pagination: widow-orphan;
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: Calibri;
			              mso-fareast-font-family: 宋体;
			              mso-bidi-font-family: 'Times New Roman';
			              font-weight: bold;
			            ">6.4</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">&nbsp;<font face="宋体">
							在任何情况下，外卖接单商家版均不对源于、基于或因使用外卖接单商家版平台而导致的数据丢失或任何损害赔偿负责，包括但不限于直接的、间接的、特殊的、附带性的损害赔偿或其他任何形式的损害赔偿。基于上述原因，外卖接单商家版提醒发布者自行对相关数据进行定期存储保存。
						</font></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 0pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-pagination: widow-orphan;
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: Calibri;
			              mso-fareast-font-family: 宋体;
			              mso-bidi-font-family: 'Times New Roman';
			              font-weight: bold;
			            ">6.5</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">&nbsp;<font face="宋体">外卖接单商家版向发布者承担的最大的总体责任和赔偿不超过外卖接单商家版收取的该发布者支付的技术服务费用的总额。</font></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 0pt;
			          margin-right: 0pt;
			          margin-bottom: 0pt;
			          margin-left: 0pt;
			          mso-pagination: widow-orphan;
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: Calibri;
			              mso-fareast-font-family: 宋体;
			              mso-bidi-font-family: 'Times New Roman';
			              font-weight: bold;
			            ">6.6</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">&nbsp;<font face="宋体">对下列情形，外卖接单商家版不承担任何责任：</font></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">(1) <font face="宋体">系统停机维护；</font></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">(2)
						<font face="宋体">终端设备、通讯或任何设备出现故障不能进行数据传输或迟延、不准确、错误、疏漏等的；</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">(3)
						<font face="宋体">因台风、地震、海啸、洪水、停电、战争、恐怖袭击等不可抗力因素，造成系统障碍不能执行业务的；</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">(4)
						<font face="宋体">由于黑客攻击、电信部门技术调整或故障、网站升级、第三方问题等原因造成的服务中断或者延迟。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="MsoNormal" style="
			          margin-bottom: 15pt;
			          padding: 0pt 0pt 0pt 0pt;
			          mso-pagination: widow-orphan;
			          text-align: left;
			          line-height: 21pt;
			        ">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            color: rgb(77, 107, 254);
			            font-size: 10.5pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p>&nbsp;</o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第七条：用户保障</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">7.1</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者同意为外卖接单商家版用户提供优质的服务，保障外卖接单商家版用户的合法权益，包括但不限于采取以下措施：</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">（</font>
						1）发布者应在其外卖接单商家版主页公布联系电话，承担因发布者原因引发的各类用户咨询和投诉，发布者应负责安排专人解答用户咨询、及时回复用户提问或用户信息。
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">（</font>
						2）不论因何种原因导致本协议解除或提前终止，发布者有义务配合外卖接单商家版进行相关的善后工作。本协议终止后，如发布者和用户之间的服务仍在履行期限内的，发布者应当保证将剩余服务期限履行完毕或自行与用户达成合理的解决方案。
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">（</font>3）如用户在支付费用后要求发布者退款的，发布者应当依照用户在订购服务时事先说明的退款方案或根据与用户的协商方案安排退款。
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">（</font>
						4）发布者收费项目应与服务的实际用途有关，不得为不合法业务提供便利或支持。收费项目运作必须规范，在用户支付之前必须向用户进行明显的提示且获得用户的同意，不得引诱、欺诈用户进行不合理的消费。
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">7.2</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者如通过外卖接单商家版服务发布的现金券、抵价券以及其他类型的具有预付价值的优惠权益，发布者应遵守相关法律规定，详细说明该等优惠权益的使用条件、使用方法、使用限制、有效期等信息，且相关条件、限制应当合理，不得违反法律法规、发布者规则的规定。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">7.3</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者应当严格遵守通过各种方式直接或间接向用户作出的承诺，包括但不限于通过本协议、外卖接单商家版相关规则以及线上厨房、详情页面、外卖接单商家版站内信沟通等方式向用户作出的承诺。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">7.4</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者通过外卖接单商家版服务提供过程中如有违反法律、法规、政策，或违反发布者规则，或违反对用户的承诺致其受损的，发布者同意外卖接单商家版有权依照本协议及发布者规则约定的流程和方式，并根据外卖接单商家版或外卖接单商家版授权的第三方或发布者与外卖接单商家版一致同意的第三方的判断和调处决定进行处理，包括但不限于外卖接单商家版有权直接使用发布者缴纳的保证金（如有）对用户进行退款或赔付。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">7.5</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者明确了解并同意发布者是用户保障的责任主体。当发生发布者应当履行用户保障义务的情形时，发布者应保证用户的合法权益。对于用户因其用户权益问题提出的要求，发布者应积极处理。发布者同意当外卖接单商家版受理买家提出的用户保障维权和赔付申请时，发布者应积极配合，在外卖接单商家版要求的时间期限内提供相关证据材料，以证明向用户提供的服务中不存在用户提出的问题或符合双方的约定，并保证所提交的证据材料真实、合法。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第八条：服务费和保证金</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">8.1</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者需要向外卖接单商家版支付使用外卖接单商家版服务的技术服务费用。为了扶持发布者的发展，在外卖接单商家版认定的外卖接单商家版平台拓展期间，发布者使用外卖接单商家版服务无需支付技术服务费用。在外卖接单商家版认定的外卖接单商家版平台拓展期间终止后，外卖接单商家版保留向发布者收取技术费用的权利。外卖接单商家版实施收费前将公布有关收费政策及规则，届时，如果发布者不同意付费的，则发布者需要停止使用外卖接单商家版服务和相关服务。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">8.2</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者如通过外卖接单商家版服务使用外卖接单商家版或外卖接单商家版关联公司提供的其他收费服务，则发布者应另行支付费用。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">8.3</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者需要向外卖接单商家版缴存保证金以担保其对本协议的履行和遵守。为了扶持发布者的发展，在外卖接单商家版认定的外卖接单商家版平台拓展期间，发布者无需缴纳保证金。在外卖接单商家版认定的外卖接单商家版平台拓展期间终止后，外卖接单商家版保留要求发布者缴纳保证金的权利。外卖接单商家版实施保证金政策前将公布有关保证金的具体政策及/或规则，届时如果发布者不同意缴纳保证金的，则发布者应当停止使用外卖接单商家版相关服务。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第九条：知识产权和相关权益</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">9.1</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者接受本协议，即表明该发布者主动将其在任何时间段在本站发表的任何形式的信息的著作财产权，包括并不限于：复制权、发行权、出租权、展览权、表演权、放映权、广播权、信息网络传播权、摄制权、改编权、翻译权、汇编权等，以及应当由著作权人享有的其他可转让权利无偿独家转让给外卖接单商家版所有，同时表明该发布者许可外卖接单商家版有权就任何主体侵权而单独提起诉讼，并获得全部赔偿。本协议已经构成《著作权法》第二十五条所规定的书面协议，其效力及于发布者在外卖接单商家版发布的任何受著作权法保护的作品内容，无论该内容形成于本协议签订前还是本协议签订后。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">9.2</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;外卖接单商家版拥有此网站内容及资源的版权，受国家知识产权保护，享有对本网站内容的修改权；未经外卖接单商家版的明确书面许可，任何第三方不得为任何非私人或商业目的获取或使用本网站的任何部分或通过本网站可直接或间接获得的任何内容、服务或资料。任何第三方违反本协议的规定以任何方式，和/或以任何文字对本网站的任何部分进行发表、复制、转载、更改、引用、链接、下载或者其他方式进行使用，或向任何其他第三方提供获取本网站任何内容的渠道，则对本网站的使用权将立即终止，且任何第三方必须按照外卖接单商家版的要求，归还或销毁使用本网站任何部分的内容所创建的资料的任何副本。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">9.3</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;外卖接单商家版未向任何第三方转让本网站或其中的任何内容所相关的任何权益或所有权，且一切未明确向任何第三方授予的权利均归外卖接单商家版所有。未经本协议明确允许而擅自使用本网站任何内容、服务或资料的，构成对本协议的违约行为，且可能触犯著作权、商标、专利和/或其他方面的法律法规，外卖接单商家版保留对任何违反本协议规定的第三方（包括单位或个人等）提起法律诉讼的权利。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">9.4</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;未经外卖接单商家版另行书面授权，发布者不得以外卖接单商家版等名义发布任何信息，对外开展合作或宣称具有代理、代表权限或标明为"赞助商"、"合作伙伴"等商业合作关系。</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第十条：违约责任</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">10.1</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;在使用外卖接单商家版服务的过程中，如发布者存在侵犯第三方（如用户、知识产权权利人）权益、违反国家法律法规或其他违反本协议的行为时，除根据本协议应向外卖接单商家版支付违约金（如有）外，还应赔偿外卖接单商家版因此遭受的损失（如有），发布者须赔偿的损失包括但不限于：</span><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">(1) 外卖接单商家版被判决、裁决须对外支付的违约金或赔偿额；</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">(2) 外卖接单商家版代发布者向用户支付的退款、违约金或赔偿额；</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">(3) 外卖接单商家版被执行行政处罚的金额；</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          "><br /></span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">(4) 外卖接单商家版支付的律师费用、消除影响所支出的必要费用。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">10.2</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;发布者对外卖接单商家版存在任何期限届至而未付的款项时，除外卖接单商家版规则另有约定外，发布者应自逾期之日起向外卖接单商家版支付未付款项每日千分之五的违约金。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">10.3</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;协议各方根据本协议的规定行使单方解约权可不承担违约责任。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">10.4</span></b><span
						style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;当第三方以发布者侵权为由要求外卖接单商家版删除发布者信息时，发布者同意外卖接单商家版对删除行为不承担任何责任，如最终证明第三方为恶意或缺乏权利凭证，发布者同意自行向第三方进行追责。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
			          margin-left: 0pt;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          mso-line-height-alt: 12pt;
			          background: rgb(255, 255, 255);
			        ">
					<b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">
							<font face="Segoe UI">第十一条：适用法律及争议解决</font>
						</span></b><b><span style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 18pt;
			              mso-font-kerning: 0pt;
			            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">11.1</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;本协议适用中华人民共和国大陆地区法律。如遇本协议有关的某一特定事项缺乏明确法律规定，则应参照通用国际商业惯例和（或）行业惯例。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">11.2</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;因双方就本协议的签订、履行或解释发生争议，双方应努力友好协商解决。如协商不成，外卖接单商家版和发布者同意由外卖接单商家版住所地（</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 宋体;
			            mso-ascii-font-family: 'Segoe UI';
			            mso-hansi-font-family: 'Segoe UI';
			            mso-bidi-font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="宋体">湘潭市岳塘区</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">
						<font face="Segoe UI">）法院管辖审理双方的纠纷或争议。</font>
					</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">11.3</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;如本协议的任何条款被视作无效或无法执行，则该等条款可被分离，其余部分仍具有法律效力。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
			          margin-top: 5pt;
			          margin-right: 0pt;
			          margin-bottom: 5pt;
			          margin-left: 0pt;
			          mso-margin-top-alt: auto;
			          mso-margin-bottom-alt: auto;
			          text-indent: 0pt;
			          mso-pagination: widow-orphan;
			          background: rgb(255, 255, 255);
			        ">
					<b><span class="15" style="
			              mso-spacerun: 'yes';
			              font-family: 'Segoe UI';
			              color: rgb(64, 64, 64);
			              letter-spacing: 0pt;
			              font-weight: bold;
			              text-transform: none;
			              font-style: normal;
			              font-size: 12pt;
			              background: rgb(255, 255, 255);
			              mso-shading: rgb(255, 255, 255);
			            ">11.4</span></b><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			            background: rgb(255, 255, 255);
			            mso-shading: rgb(255, 255, 255);
			          ">&nbsp;外卖接单商家版于发布者过失或违约时放弃本协议规定的权利，不应视为其对发布者的其他或以后同类之过失或违约行为弃权。</span><span style="
			            mso-spacerun: 'yes';
			            font-family: 'Segoe UI';
			            color: rgb(64, 64, 64);
			            letter-spacing: 0pt;
			            text-transform: none;
			            font-style: normal;
			            font-size: 12pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="MsoNormal">
					<span style="
			            mso-spacerun: 'yes';
			            font-family: Calibri;
			            mso-fareast-font-family: 宋体;
			            mso-bidi-font-family: 'Times New Roman';
			            font-size: 10.5pt;
			            mso-font-kerning: 1pt;
			          ">
						<o:p>&nbsp;</o:p>
					</span>
				</p>
			</div>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				agreement: '',
				title: ''
			};
		},
		onLoad(e) {
			if (e.agreementKey == 'registrationAgreement') {
				this.title = '易达脉联平台注册协议'
				uni.setNavigationBarTitle({
					title: '易达脉联平台注册协议'
				});
			} else if (e.agreementKey == 'management') {
				this.title = '易达脉联平台服务管理规定'
				uni.setNavigationBarTitle({
					title: '易达脉联平台服务管理规定'
				});
			}
			this.getAgreement(e.agreementKey);
		},
		methods: {
			getAgreement(msg) {
				this.$request({
					url: '/config/config',
					method: 'GET',
					data: {
						ident: 'system'
					},
					IsGetStorg: false
				}).then(res => {
					res = JSON.parse(res)
					if (res.code == 1) {
						this.show = true;
						this.agreement = res.data[msg];
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
      @font-face {
        font-family: "Times New Roman";
      }

      @font-face {
        font-family: "宋体";
      }

      @font-face {
        font-family: "Wingdings";
      }

      @font-face {
        font-family: "Calibri";
      }

      @font-face {
        font-family: "Segoe UI";
      }

      p.MsoNormal {
        mso-style-name: 正文;
        mso-style-parent: "";
        margin: 0pt;
        margin-bottom: 0.0001pt;
        mso-pagination: none;
        text-align: justify;
        text-justify: inter-ideograph;
        font-family: Calibri;
        mso-fareast-font-family: 宋体;
        mso-bidi-font-family: "Times New Roman";
        font-size: 10.5pt;
        mso-font-kerning: 1pt;
      }

      h1 {
        mso-style-name: "标题 1";
        mso-style-next: 正文;
        margin-top: 5pt;
        margin-bottom: 5pt;
        mso-margin-top-alt: auto;
        mso-margin-bottom-alt: auto;
        mso-pagination: none;
        text-align: left;
        font-family: 宋体;
        font-weight: bold;
        font-size: 24pt;
        mso-font-kerning: 22pt;
      }

      h2 {
        mso-style-name: "标题 2";
        mso-style-noshow: yes;
        mso-style-next: 正文;
        margin-top: 5pt;
        margin-bottom: 5pt;
        mso-margin-top-alt: auto;
        mso-margin-bottom-alt: auto;
        mso-pagination: none;
        text-align: left;
        font-family: 宋体;
        font-weight: bold;
        font-size: 18pt;
      }

      h3 {
        mso-style-name: "标题 3";
        mso-style-noshow: yes;
        mso-style-next: 正文;
        margin-top: 5pt;
        margin-bottom: 5pt;
        mso-margin-top-alt: auto;
        mso-margin-bottom-alt: auto;
        mso-pagination: none;
        text-align: left;
        font-family: 宋体;
        font-weight: bold;
        font-size: 13.5pt;
      }

      span.10 {
        font-family: "Times New Roman";
      }

      span.15 {
        font-family: "Times New Roman";
        mso-ansi-font-weight: bold;
      }

      p.p {
        mso-style-name: "普通\(网站\)";
        margin: 0pt;
        margin-bottom: 0.0001pt;
        mso-pagination: none;
        text-align: justify;
        text-justify: inter-ideograph;
        font-family: Calibri;
        mso-fareast-font-family: 宋体;
        mso-bidi-font-family: "Times New Roman";
        font-size: 12pt;
        mso-font-kerning: 1pt;
      }

      span.msoIns {
        mso-style-type: export-only;
        mso-style-name: "";
        text-decoration: underline;
        text-underline: single;
        color: blue;
      }

      span.msoDel {
        mso-style-type: export-only;
        mso-style-name: "";
        text-decoration: line-through;
        color: red;
      }

      table.MsoNormalTable {
        mso-style-name: 普通表格;
        mso-style-parent: "";
        mso-style-noshow: yes;
        mso-tstyle-rowband-size: 0;
        mso-tstyle-colband-size: 0;
        mso-padding-alt: 0pt 5.4pt 0pt 5.4pt;
        mso-para-margin: 0pt;
        mso-para-margin-bottom: 0.0001pt;
        mso-pagination: widow-orphan;
        font-family: "Times New Roman";
        font-size: 10pt;
        mso-ansi-language: #0400;
        mso-fareast-language: #0400;
        mso-bidi-language: #0400;
      }
      @page {
        mso-page-border-surround-header: no;
        mso-page-border-surround-footer: no;
      }
      @page Section0 {
        margin-top: 72pt;
        margin-bottom: 72pt;
        margin-left: 90pt;
        margin-right: 90pt;
        size: 595.3pt 841.9pt;
        layout-grid: 15.6pt;
        mso-header-margin: 42.55pt;
        mso-footer-margin: 49.6pt;
      }
      div.Section0 {
        page: Section0;
      }
	.agreement {
		height: 100%;
		padding: 0 32rpx;

		.agreement-title {
			padding: 60rpx 0;
			font-size: $font-my-size-44;
			font-weight: bold;
		}

		.agreement-cont {
			width: 100%;
			font-size: 32rpx;
			overflow-y: scroll;

			/deep/p {
				span {
					font-size: 32rpx !important;
				}
			}
		}
	}
</style>