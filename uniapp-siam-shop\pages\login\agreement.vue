<template>
	<view class="agreement">
		<view class="agreement-title">{{ title }}</view>
		<view class="agreement-cont" v-html="agreement"></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			agreement: '',
			title: ''
		};
	},
	onLoad(e) {
		if (e.agreementKey == 'registrationAgreement') {
			this.title = '易达脉联平台注册协议'
			uni.setNavigationBarTitle({
				title: '易达脉联平台注册协议'
			});
		} else if (e.agreementKey == 'management') {
			this.title = '易达脉联平台服务管理规定'
			uni.setNavigationBarTitle({
				title: '易达脉联平台服务管理规定'
			});
		}
		this.getAgreement(e.agreementKey);
	},
	methods: {
		getAgreement(msg) {
			this.$request({
				url: '/config/config',
				method: 'GET',
				data: { ident: 'system' },
				IsGetStorg: false
			}).then(res => {
				res = JSON.parse(res)
				if (res.code == 1) {
					this.show = true;
					this.agreement = res.data[msg];
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.agreement {
	height: 100%;
	padding: 0 32rpx;
	
	.agreement-title{
		padding: 60rpx 0;
		font-size: $font-my-size-44;
		font-weight: bold;
	}
	
	.agreement-cont {
		width: 100%;
		font-size: 32rpx;
		overflow-y: scroll;
		
		/deep/p {
			span {
				font-size: 32rpx !important;
			}
		}
	}
}
</style>
