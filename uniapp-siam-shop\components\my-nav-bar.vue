<template>
	<uni-nav-bar
		:title="title"
		:topDistance="topDistance"
		:backgroundColor="background"
		:color="color"
		:left-icon="leftIcon"
		:left-icon-my="leftIconMy"
		:right-icon="rightIconFun"
		:border="borderBottom"
		:shadow="shadow"
		:status-bar="statusBar"
		:fixed="fixed"
		:left-text="leftText"
		:icon-color="IconColor"
		:right-text="rightText"
		:btn-width="btnWidth"
		@clickLeft="$common.handBack"
		@clickRight="clickRight"
	>
		<view slot="left"><slot name="left-cont"></slot></view>
		<view slot="default"><slot name="center-cont"></slot></view>
		<view slot="right">
			<slot name="right-cont"></slot>
		</view>
	</uni-nav-bar>
</template>

<script>
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';

export default {
	components: {
		uniNavBar
	},
	props: {
		topDistance:{
			type: String,
			default: '0'
		},
		btnWidth:{
			type: String,
			default: '120'
		},
		title: {
			type: String,
			default: ''
		},
		leftText: {
			type: String,
			default: ''
		},
		rightText: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: '#fff'
		},
		color: {
			type: String,
			default: '#333'
		},
		IconColor:{
			type: String,
			default: '#333'
		},
		leftIconMy: {
			type: String,
			default: 'icon-fanhui-left'
		},
		leftIcon: {
			type: String,
			default: ''
		},
		rightIcon: {
			type: String,
			default: 'arrowright'
		},
		borderBottom: {
			type: Boolean,
			default: false
		},
		shadow: {
			type: Boolean,
			default: false
		},
		fixed: {
			type: Boolean,
			default: true
		},
		statusBar: {
			type: Boolean,
			default: true
		},
		arrowRight: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		rightIconFun() {
			const arrowRights = this.arrowRight ? this.rightIcon : '';
			return arrowRights;
		}
	},
	methods: {
		clickRight() {
			this.$emit('clickRight');
		}
	}
};
</script>

<style lang="scss" scoped></style>
