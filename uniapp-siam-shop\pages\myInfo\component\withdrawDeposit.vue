<template>
	<u-popup v-model="show" mode="bottom" height="700rpx" border-radius="20" :safe-area-inset-bottom="true" :closeable="true" close-icon-pos="top-left">
		<view class="text-cont">
			<view class="text-title">可提现金额(元)</view>
			<view class="flex flex-align-center">
				<text class="moneyicon">￥</text>
				<input class="text-cont-input" type="number" v-model="money" placeholder="输入提现金额" placeholder-style="font-weight: 400; font-size: 40rpx;" />
			</view>
			<view class="text-title">支付密码</view>
			<view class="flex flex-align-center">
				<input class="text-cont-input" type="password" v-model="password" placeholder="输入密码" placeholder-style="font-weight: 400; font-size: 40rpx;" />
			</view>
			<my-line></my-line>
			<view class="text-title-two">{{ parseFloat(money) > parseFloat(maxMoney) ? '输入金额超过可提现金额' : '' }}</view>
			<my-button border-radius="10" width="670" color="#fff" :fiexd="true" @confirm="submit">确认提现</my-button>
		</view>
	</u-popup>
</template>

<script>
import encodeDecode from "@/utils/encodeDecode/index.js"
export default {
	data() {
		return {
			show: false,
			heiShow: false,
			money: '',
			password: '',
			maxMoney: 0,
			num: 0,
			styleShow: false
		};
	},
	watch: {
		money() {
			// setTimeout(() => {
			// 	this.money = parseFloat(this.money).toFixed(2);
			// }, 10);
		}
	},
	methods: {
		init(msg) {
			console.log(msg)
			this.money = msg;
			this.maxMoney = msg;
			this.show = true;
		},
		handClickShow() {
			this.heiShow = !this.heiShow;
		},
		submit() {
			if (parseFloat(this.money) > parseFloat(this.maxMoney)) {
				this.$interactive.ShowToast(
					{
						title: '金额超过可提现金额!'
					},
					false
				);
			} else {
				this.$request({
                    url: '/api-merchant/rest/merchant/withdrawal',
					data: {
						withdrawAmount: this.money,
						paymentMode: 1,
						paymentPassword: encodeDecode.wxStrToBase64(this.password)
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
						res = JSON.parse(res)
					// #endif
					if (res.code === 200) {
						this.$emit('click');
						uni.$emit('RefreshUserInfo');
						this.$interactive
							.ShowToast({
								title: '提现成功！'
							})
							.then(res => {
								this.show = false;
							});
					} else {
						this.$interactive.ShowToast(
							{
								title: res.message
							},
							false
						);
					}
				});
			}
		}
	}
};
</script>

<style lang="scss">
.text-cont {
	position: relative;
	top: 0rpx;
	left: 0rpx;
	padding: 80rpx 40rpx 40rpx;
	height: 100%;
	overflow: hidden;

	.scroll-Y {
		height: 80%;
	}

	.text-title {
		font-size: 38rpx;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #333333;
	}
	.moneyicon {
		font-size: 50rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		padding-right: 20rpx;
	}

	.text-cont-input {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 50rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
	}
	.text-title-two {
		padding: 20rpx 0rpx;
		font-size: $font-my-size-28;
		font-family: Source Han Sans CN;
		font-weight: 400;
		height: 78rpx;
		color: #fa3534;
		transition: all 0.1 ease;
	}

	.text-cont-info {
		background: #f5f5f5;
		border-radius: 2px;
		padding: 0rpx 20rpx 0rpx;

		.text-cont-info-menu {
			padding: 20rpx 0;
			border-bottom: 2rpx dashed #c2c2c2;
		}

		.text-cont-info-list {
			width: 630rpx;
			padding: 40rpx 0rpx;
			overflow: hidden;
			transition: all 0.1s ease;

			.u-section {
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0rpx;
				}
			}

			.gripInfo {
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				margin-top: 60rpx;
			}
		}

		.text-cont-info-list-height {
			height: 0rpx;
			padding: 0rpx;
		}
	}
	.confirmBtn {
		position: relative;
		height: 20%;

		.confirm {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 600rpx;
			height: 92rpx;
			min-height: 92rpx;
			line-height: 92rpx;
			background: #252b3b;
			border-radius: 46rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
		}
	}
}
</style>
