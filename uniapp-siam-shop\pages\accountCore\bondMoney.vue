<template>
	<view class="bondMoney">
		<uni-status-bar background="#3c82ca"></uni-status-bar>
		<view style="height: 60rpx;background-color: #3c82ca;margin-bottom: -60rpx;"></view>
		<my-nav-bar title="保证金" background="#3c82ca" icon-color="#fff" color="#fff" topDistance="60" :status-bar="false">
			<view slot="right-cont">
				<view class="title-info"><navigator url="/pages/accountCore/detailed" hover-class="none">明细</navigator></view>
			</view>
		</my-nav-bar>
		<view class="main-cont">
			<view class="title-box">当前金额</view>
			<view class="title-cont">0</view>
			<view class="trip">
				保证金低于100不能接单，详情请见规则
				<u-icon class="icon" name="info-circle" color="#FFFFFF" size="30"></u-icon>
			</view>
		</view>
		<u-cell-group :border="false">
			<u-cell-item title="保证金缴纳" :arrow="false" @click="openPop(1)">
				<view slot="icon"><my-icon size="48" style="margin-right: 10rpx;">&#xe61b;</my-icon></view>
				<view slot="right-icon"><uni-icons type="forward"></uni-icons></view>
			</u-cell-item>
			<u-cell-item title="提取" :arrow="false" @click="openPop(2)">
				<view slot="icon"><my-icon size="36" style="margin-right: 20rpx;">&#xe62e;</my-icon></view>
				<view slot="right-icon"><uni-icons type="forward"></uni-icons></view>
			</u-cell-item>
		</u-cell-group>
		<u-modal v-model="telShow" :title="config.title" :show-cancel-button="true" confirm-text="确定" @confirm="submit">
			<template slot="default">
				<view class="model flex flex-align-center justify-space-between">
					<text>{{ config.conent }}</text>
					<uni-icons v-if="config.type == 1" type="checkbox-filled" color="#3c82ca" size="40"></uni-icons>
				</view>
			</template>
		</u-modal>
	</view>
</template>

<script>
export default {
	data() {
		return {
			telShow: false,
			config: {
				title: '保证金充值',
				conent: '确认充值100元',
				type: 1
			}
		};
	},
	methods: {
		openPop(msg) {
			if(msg == 1){
				this.config.title = "请选择您要充值的金额";
				this.config.conent = "充值100元，可接普通订单";
			}else{
				this.config.title = "保证金提取";
				this.config.conent = "确认提取保证金吗？提取后您将无法正常接单哦！"
			}
			this.config.type = msg;
			this.telShow = true;
		},
		submit() {
			this.telShow = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.bondMoney {
	/deep/.u-cell {
		&::after {
			width: 190%;
			left: 32rpx;
			border-color: #f1f1f1;
		}
	}

	/deep/.u-cell_title {
		font-size: 32rpx;
		color: #333;
	}
	.title-info {
		font-size: 32rpx;
	}

	.main-cont {
		background: #3c82ca;
		padding: 20rpx 0rpx 0rpx;

		.title-box {
			position: relative;
			text-align: center;
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
		}

		.title-cont {
			font-size: 80rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			text-align: center;
			padding-bottom: 70rpx;
		}

		.trip {
			position: relative;
			padding: 0 20rpx;
			height: 90rpx;
			line-height: 90rpx;
			background: rgba(0, 0, 0, 0.2);
			font-size: 30rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;

			.icon {
				position: absolute;
				top: 50%;
				right: 20rpx;
				transform: translateY(-50%);
			}
		}
	}
	
	.model{
		padding: 40rpx 32rpx;
	}
}
</style>
