<template>
	<view class="billDetails">
		<custom-tabs-swiper :list="list" :head-icon-show="true" :status-bar-show="true" font-size="36" head-right-icon="help" :show-line="false" style="position: fixed">
			<template slot="swiper-item-1">
				<bill-detail-l></bill-detail-l>
			</template>
			<template slot="swiper-item-2">
				<bill-detail-withdraw></bill-detail-withdraw>
			</template>
			<template slot="swiper-item-3">
				<bill-detail-r></bill-detail-r>
			</template>
		</custom-tabs-swiper>
	</view>
</template>

<script>
// 全屏联动
import CustomTabsSwiper from '../../components/custom-tabs-page-linkge/tab-page-linkage.vue';
import BillDetailL from './component/bill-detail-l.vue';
import BillDetailWithdraw from './component/bill-detail-withdraw.vue';
import BillDetailR from './component/bill-detail-r.vue';
export default {
	components: {
		CustomTabsSwiper,
		<PERSON><PERSON><PERSON><PERSON><PERSON>,
		BillDetailWithdraw,
		Bill<PERSON><PERSON><PERSON><PERSON>
	},
	data() {
		return {
			list: [
				{
					name: '账单'
				},
				{
					name: '提现'
				},
				{
					name: '统计'
				}
			]
		};
	}
};
</script>

<style lang="scss" scoped>
.billDetails {
	height: 100%;
	//#ifndef H5
	height: 100vh;
	//#endif
}
</style>
