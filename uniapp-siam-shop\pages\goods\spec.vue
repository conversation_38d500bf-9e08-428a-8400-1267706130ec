<template>
  <view class="content">
    <uni-status-bar></uni-status-bar>

    <view class="spec-container">

      <!-- 添加规格类型 -->
      <view class="add-spec-type" v-for="(item, index) in specTypes">
        <view class="spec-type-inputs">
          <input type="text" v-model="item.specificationName" placeholder="规格名称" class="spec-input" />
        </view>
        <view class="spec-type-inputs">
          <input type="text" v-model="item.name" placeholder="规格选项名称" class="spec-input" />
        </view>
        <view class="spec-type-inputs">
          <input type="number" v-model="item.price" placeholder="加价金额" class="spec-input" />
        </view>
        <!-- <view v-if="item.id" class="action-btn add-btn" @click="addSpecType(item)">编辑</view>
        <view v-if="item.id" class="action-btn add-btn" @click="addSpecType(item)">删除</view>
        <view v-if="!item.id" class="action-btn add-btn" @click="addSpecType(item)">添加</view> -->
        <u-button v-if="!item.id" type="primary" size="mini" plain @click="addSpecType(item)">添加</u-button>
        <u-button v-if="item.id" type="primary" size="mini" plain @click="addSpecType(item)">保存</u-button>
        <u-button v-if="item.id" type="error" size="mini" plain style="margin-left: 6rpx;" @click="deleteSpecType(item)">删除</u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      goodsId: '',
      goodsName: '',
      specTypes: [{}],
    }
  },
  onLoad (options) {
    if (options.id) {
      this.goodsId = options.id;
    }
    // 加载商品规格数据
    this.loadSpecData();
  },

  methods: {
    // 加载商品规格数据
    async loadSpecData () {
      if (!this.goodsId) return;
      try {
        uni.showLoading({
          title: '加载中...'
        });

        const res = await this.$request({
          url: '/api-goods/rest/merchant/goodsSpecificationOption/list',
          method: 'POST',
          data: {
            goodsId: this.goodsId,
            pageNo: -1, pageSize: 1000,
          }
        });

        if (res.code === 200) {
          this.specTypes = res.data?.records;
          this.specTypes.unshift({})
        } else {
          uni.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    testSpec(item) {
      // debugger
       if (!item.specificationName?.trim()) {
        uni.showToast({
          title: '请输入规格名称',
          icon: 'none'
        });
        return;
      }
      if (!item.name?.trim()) {
        uni.showToast({
          title: '请输入规格选项名称',
          icon: 'none'
        });
        return;
      }
      let regEnPrice = /^[0-9]\d*$/;
      if (!regEnPrice.test(item.price)) {
        uni.showToast({
          title: '请输入正确的加价金额',
          icon: 'none'
        });
        return;
      }
      return true
    },
    // 添加规格类型
    async addSpecType(item) {
      if (!this.testSpec(item)) return;

      try {
        uni.showLoading({
          title: '加载中...'
        });
        const url = item.id ?  '/api-goods/rest/merchant/goodsSpecificationOption/update' : '/api-goods/rest/merchant/goodsSpecificationOption/insert';
        const res = await this.$request({
          url,
          method: 'POST',
          data: {
            ...item,
            goodsId: this.goodsId
          }
        });

        if (res.code === 200) {
          this.loadSpecData()
        } else {
          uni.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      } finally {
        uni.hideLoading();
      }
    },


    // 删除规格类型
    deleteSpecType (item) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该规格类型吗？',
        success:async (res) => {
          if (res.confirm) {
            const res = await this.$request({
              url: '/api-goods/rest/merchant/goodsSpecificationOption/delete',
              data: { ids: [item.id] }
            });
            if (res.code === 200) {
              uni.showToast({
                title: '操作成功',
                icon: 'success'
              });
              this.loadSpecData();
            } else {
              uni.showToast({
                title: res.message || '操作失败',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  width: 750rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-x: hidden;
  background-color: $uni-bg-color-grey;

}


.back-btn {
  padding: 10rpx;
}

.title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-right: 44rpx;
  /* 为了平衡back-btn的宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.spec-container {
  flex: 1;
  padding: 30rpx 20rpx;
  overflow-y: auto;
}

.spec-type-list {}

.spec-type-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.spec-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.spec-type-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.spec-values-list {
  margin-bottom: 20rpx;
}

.spec-value-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx dashed #f0f0f0;
}

.spec-value-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.spec-value-name {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.spec-value-stock {
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}

.spec-value-price {
  font-size: 26rpx;
  color: #fa7c25;
}

.spec-value-actions {
  display: flex;
  gap: 16rpx;
}

.add-spec-value {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.spec-value-inputs {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.spec-input {
  flex: 1;
  height: 48rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
}

.add-spec-type {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.spec-type-inputs {
  flex: 1;
  margin-right: 16rpx;
}

.action-btn {
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
  font-weight: 500;
}

.edit-btn {
  background: #007aff;
  color: #fff;
}

.delete-btn {
  background: #fa7c25;
  color: #fff;
}

.add-btn {
  background: #5ac8fa;
  color: #fff;
}
</style>