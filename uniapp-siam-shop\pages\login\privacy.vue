<template>
	<view class="agreement">
		<view class="agreement-cont">

			<div class="Section0" style="layout-grid: 15.6pt">
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">本政策仅适用于商家接单版</font>App产品或服务。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">本政策将帮助您了解以下内容：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">一、我们如何收集和使用您的用户信息</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">二、我们如何使用</font> Cookie 和同类技术
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">三、我们如何共享、转让、公开披露您的用户信息</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">四、我们如何保护您的用户信息</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">五、您的权利</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">六、本政策如何更新</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">七、如何联系我们</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							我们深知用户信息对您的重要性，并会尽全力保护您的用户信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的用户信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。同时，我们承诺，我们将按业界成熟的安全标准，采取相应的安全保护措施来保护您的用户信息。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">请在使用我们的产品（或服务）前，仔细阅读并了解本隐私政策。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">一、我们如何收集和使用您的用户信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（一）您使用我司产品或服务过程中我们收集和使用的信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							我们仅会出于本政策所述的业务功能，收集和使用您的用户信息，收集用户信息的目的是在向您提供产品或服务，您有权自行选择是否提供该信息，但多数情况下，如果您不提供，我们可能无法向您提供相应的服务，也无法回应您遇到的问题：
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">在您使用我们的服务时，允许我们收集您自行向我们提供的或为向您提供服务所必要的信息包括：用户名、手机号码、设备信息、日志信息、</font>
						IP地址、位置信息、订购的商品信息、发单人和收单人信息、地址等。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">为了保障您的账号安全、交易安全以及系统运行安全，满足法律法规和我们协议规则的相关要求，在您访问及使用我们的产品</font>
						/服务过程中，您授权我们会获取您的设备信息、网络状态信息，包括您使用的设备型号、硬件序列号、唯一设备识别码（如IMEI/MEID信息等设备标识符）、传感器信息（点击查看）、WIFI状态（如SSID、BSSID）、电信运营商等软硬件特征信息。未经您的授权，我们不会提前获取以上信息。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">您提供的上述信息，将在您使用本服务期间持续授权我们使用。在您停止使用推送服务时，我们将停止使用并删除上述信息。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							我们保证会依法对收集后的用户信息进行去标识化或匿名化处理，对于无法单独或者与其他信息结合识别自然人个人身份的信息，不属于法律意义上的个人信息。如果我们将非个人信息与其他信息结合识别到您的个人身份时，或者与您的个人信息结合使用时，我们会在结合使用期间，将此类信息作为您的个人信息按照本隐私政策对其进行处理和保护。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							为了更好运营和改善我们的技术和服务，或出于商业策略的变化，当我们提供的产品或服务所需的用户信息收集、使用、处理超出上述范围或者要将收集到的用户信息用于本隐私政策未载明的其他用途，或当我们要将基于特定目的收集而来的信息用于其他目的时，我们会在获取用户信息后的合理期限内或处理用户信息前通知您，并获得您的授权同意。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（二）征得授权同意的例外</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">请您理解，根据法律法规及相关国家标准，以下情形中，我们收集和使用您的用户信息无需征得您的授权同意：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与国家安全、国防安全直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与公共安全、公共卫生、重大公共利益直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">3</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与犯罪侦查、起诉、审判和判决执行等直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">4</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">5</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">所收集的您的用户信息是您自行向社会公众公开的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">6</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">从合法公开披露的信息中收集的您的用户信息，如合法的新闻报道、政府信息公开等渠道；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">7</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">根据您的要求签订或履行合同所必需的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">8</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">用于维护软件及相关服务的安全稳定运行所必需的，例如发现、处置软件及相关服务的故障；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">9</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">个人信息控制者为新闻单位且其在开展合法的新闻报道所必需的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">10</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">学术研究机构基于公共利益开展统计或学术研究所必要，且对外提供学术研究或描述的结果时，对结果中所包含的个人信息进行去标识化处理的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">11</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">法律法规规定的其他情形。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">二、我们如何使用</font> Cookie
							和同类技术
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（一）</font>Cookie
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">为确保网站正常运转，我们会在您的计算机或移动设备上存储名为</font>
						Cookie 的小数据文件。Cookie
						通常包含标识符、站点名称以及一些号码和字符。借助于
						Cookie，网站能够存储您的访问偏好数据。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们不会将</font> Cookie
						用于本政策所述目的之外的任何用途。您可根据自己的偏好管理或删除
						Cookie。您可以清除计算机上保存的所有
						Cookie，大部分网络浏览器都没有阻止 Cookie
						的功能。但如果您这么做，则需要在每一次访问我们的网站时亲自更改用户设置。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（二）网站信标和像素标签</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">除</font> Cookie
						外，我们还会在网站上使用网站信标和像素标签等其他同类技术。例如，我们向您发送的电子邮件可能含有链接至我们网站内容的点击
						URL。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							如果您点击该链接，我们则会跟踪此次点击，帮助我们了解您的产品或服务偏好并改善客户服务。网站信标通常是一种嵌入到网站或电子邮件中的透明图像。借助于电子邮件中的像素标签，我们能够获知电子邮件是否被打开。如果您不希望自己的活动以这种方式被追踪，则可以随时从我们的寄信名单中退订。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（三）</font>Do Not Track（请勿追踪）
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">很多网络浏览器均设有</font> Do Not Track
						功能，该功能可向网站发布 Do Not Track
						请求。目前，主要互联网标准组织尚未设立相关政策来规定网站应如何应对此类请求。但如果您的浏览器启用了
						Do Not Track，那么我们的所有网站都会尊重您的选择。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">三、我们如何共享、转让、公开披露您的用户信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（一）共享</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们不会与其他的任何公司、组织和个人分享您的用户信息，但以下情况除外：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">在获取明确同意的情况下共享：获得您的明确同意后，我们会与其他方共享您的用户信息。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们可能会根据法律法规规定，或按政府主管部门的强制性要求，对外共享您的用户信息。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">3</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							与我们的关联公司共享：您的用户信息可能会与我们的关联公司共享。我们只会共享必要的用户信息，且受本隐私政策中所声明目的约束。关联公司如要改变用户信息的处理目的，将再次征求您的授权同意。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">4</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							与授权合作伙伴共享：仅为实现本政策中声明的目的，我们的某些服务将由授权合作伙伴提供。我们可能会与合作伙伴共享您的某些用户信息，以提供更好的客户服务和用户体验。我们仅会出于合法、正当、必要、特定、明确的目的共享您的用户信息，并且只会共享提供服务所必要的用户信息。为了更好运营和改善技术和服务，您同意我们和授权合作伙伴在符合相关法律法规的前提下可将收集的信息用于其他服务和用途。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">以下列举了具体的授权合作伙伴，并提供了该第三方的隐私政策链接，我们建议您阅读该第三方的隐私政策：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">SDK 名称</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：高德软件有限公司</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">服务类型</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：实现定位</font>/展现地图
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">SDK 收集个人信息类型</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
          mso-list: l2 level1 lfo1;
        ">
					<![if !supportLists]><span style="
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><span style="mso-list: Ignore">1、</span></span>
					<![endif]><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">经纬度</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备信息（如</font>
						IP地址、GNSS信息、网络类型、WiFi状态、WiFi参数、WiFi列表、SSID/BSSID、基站信息、WiFi信号强度）
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备传感器信息（如加速度、压力、方向、陀螺仪）</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备标识信息（</font>IDFA、OAID）
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">当前应用信息（应用名、应用版本号）</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备参数及系统信息（设备品牌及型号、操作系统、运营商信息、屏幕分辨率）</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
          mso-list: l2 level1 lfo1;
        ">
					<![if !supportLists]><span style="
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><span style="mso-list: Ignore">2、</span></span>
					<![endif]><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">高德开放平台政策链接：</font>
					</span><span><a href="https://lbsamap.com/home/<USER>/"><span class="16" style="
                mso-spacerun: 'yes';
                font-family: 'Segoe UI';
                color: rgb(0, 0, 255);
                letter-spacing: 0pt;
                text-transform: none;
                font-style: normal;
                font-size: 12pt;
                background: rgb(255, 255, 255);
                mso-shading: rgb(255, 255, 255);
              ">https://lbsamap.com/home/<USER>/</span></a></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							对我们与之共享用户信息的公司、组织和个人，我们会与其签署严格的保密协定，要求他们按照我们的说明、本隐私政策以及其他任何相关的保密和安全措施来处理用户信息。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（二）转让</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们不会将您的用户信息转让给任何公司、组织和个人，但以下情况除外：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">在获取明确同意的情况下转让：获得您的明确同意后，我们会向其他方转让您的用户信息；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							在涉及合并、收购或破产清算时，如涉及到用户信息转让，我们会要求新的持有您用户信息的公司、组织继续受此隐私政策的约束，否则我们将要求该公司、组织重新向您征求授权同意。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（三）公开披露</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们仅会在以下情况下，公开披露您的用户信息：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">获得您明确同意后；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">基于法律的披露：在法律、法律程序、诉讼或政府主管部门强制性要求的情况下，我们可能会公开披露您的用户信息。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（四）共享、转让、公开披露信息时事先征得授权同意的例外</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">请您理解，根据法律法规及相关国家标准，以下情形中，我们共享、转让、公开披露您的用户信息无需征得您的授权同意：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与国家安全、国防安全直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与公共安全、公共卫生、重大公共利益直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">3</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与犯罪侦查、起诉、审判和判决执行等直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">4</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">5</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">您自行向社会公众公开的信息；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">6</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">从合法公开披露的信息中收集的，如合法的新闻报道、政府信息公开等渠道。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="MsoNormal" style="
          margin-top: 3pt;
          margin-bottom: 5pt;
          margin-left: 72pt;
          mso-margin-bottom-alt: auto;
          text-indent: -18pt;
          tab-stops: left blank 36pt;
          mso-pagination: widow-orphan;
          mso-list: l0 level1 lfo2;
        ">
					<![if !supportLists]><span style="
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "><span style="mso-list: Ignore">1.<span>&nbsp;</span></span></span>
					<![endif]><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 10.5pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（五）</font>iOS系统、Android系统权限调用说明
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">为确保相关业务功能的正常实现，我们需要根据具体的使用场景调用对应的必要权限，并在调用前向您询问，具体的权限调用说明如下：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">1. iOS系统权限调用说明</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">设备权限</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：位置信息</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">使用目的</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用于定位发货</font>/取货/收货地址，使用地图导航等功能
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否询问</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用户触发使用位置信息功能时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否可关闭</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">设备权限</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：通知</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">使用目的</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用于发送提醒</font>/通知
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否询问</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：首次使用时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否可关闭</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">设备权限</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：相机</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">使用目的</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用于拍照需要的凭证，反馈问题等功能</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否询问</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用户主动使用拍照功能时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否可关闭</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">设备权限</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：相册</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">使用目的</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用于从相册中上传照片需要的凭证，反馈问题等功能</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否询问</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用户主动上传照片时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">是否可关闭</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">设备权限</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：拨打电话</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><b><span class="15" style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 12pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">使用目的</font>
						</span></b><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">：用于联系商户</font>/顾客
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：拨号前询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">2、Android系统权限调用说明</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备权限：位置信息</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">使用目的：用于定位发货</font>/取货/收货地址，使用地图导航等功能
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：用户触发使用位置信息功能时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备权限：通知</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">使用目的：用于发送提醒</font>/通知
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：首次使用时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备权限：相机</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">使用目的：用于拍照需要的凭证，反馈问题等功能</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：用户主动使用拍照功能时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备权限：相册</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">使用目的：用于从相册中上传照片需要的凭证，反馈问题等功能</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：用户主动上传照片时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备权限：拨打电话</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">使用目的：用于联系商户</font>/顾客
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：拨号前询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">设备权限：自启动</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">使用目的：用于软件在后台自启动运行，确保</font>APP不被关闭导致无法接收通知
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否询问：检测用户未开启时询问</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><br /></span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">是否可关闭：是</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">四、我们如何保护您的用户信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							（一）我们已经使用符合业界标准的安全防护措施保护您提供的用户信息，防止数据遭到未经授权的访问、公开披露、使用、修改、损坏或丢失。我们会采取一切合理可行的措施，保护您的用户信息。例如：在您的浏览器与
						</font>
						"服务"之间交换数据时受SSL加密保护；对网站提供https安全浏览方式；使用加密技术确保数据的保密性；使用受信赖的保护机制防止数据遭到恶意攻击；部署访问控制机制，确保只有授权人员才可访问用户信息；举办安全和隐私保护培训课程，加强员工对于保护用户信息重要性的认识。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">（二）我们的数据安全能力：采用严格的数据访问权限控制和多重身份认证技术保护个人信息，避免数据被违规使用。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							（三）我们会采取一切合理可行的措施，确保未收集无关的用户信息。我们不会在达成本政策所述目的所需的期限内保留您的用户信息，除非需要延长保留期或受到法律的允许。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">（四）互联网并非绝对安全的环境，而且电子邮件、即时通讯、及与其他用户的交流方式并未加密，我们强烈建议您不要通过此类方式发送用户信息。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">（五）我们将定期更新并公开安全风险、用户信息安全影响评估等报告的有关内容。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">（六）</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							互联网环境并非百分之百安全，我们将尽力确保您发送给我们的任何信息的安全性。即使我们做出了很大努力，采取了一切合理且必要的措施，仍然有可能无法杜绝您的用户信息被非法访问、被非法盗取、被非法篡改或毁坏，导致您的合法权益受损，请您理解信息网络的上述风险并自愿承担。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">（七）</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">在不幸发生用户信息安全事件后，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们已采取或将要采取的处置措施</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">您可自主防范和降低风险的建议</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">对您的补救措施等</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="宋体">。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							我们将及时将事件相关情况以邮件、信函、电话、推送通知等方式告知您。难以逐一告知用户信息主体时，我们会采取合理、有效的方式发布公告。同时，我们还将按照监管部门要求，主动上报用户信息安全事件的处置情况。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">五、您的权利</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">按照中国相关的法律、法规、标准，以及其他国家、地区的通行做法，我们保障您对自己的用户信息行使以下权利：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（一）访问您的用户信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">您有权访问您的用户信息，法律法规规定的例外情况除外。如果您无法通过上述链接访问这些用户信息，您可以随时使用我们的</font>
						Web 表单联系。我们将在30天内回复您的访问请求。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">对于您在使用我们的产品或服务过程中产生的其他用户信息，只要我们不需要过多投入，我们会向您提供。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（二）更正您的用户信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">当您发现我们处理的关于您的用户信息有错误时，您有权要求我们作出更正。您可以通过</font>
						"（一）访问您的用户信息"中罗列的方式提出更正申请。如果您无法通过上述链接更正这些用户信息，您可以随时使用我们的
						Web 表单联系。我们将在30天内回复您的更正请求。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h3 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
          mso-list: l1 level1 lfo3;
        ">
					<![if !supportLists]><span style="
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: bold;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "><span style="mso-list: Ignore">（三）</span></span>
					<![endif]><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">删除您的用户信息</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<o:p></o:p>
						</span></b>
				</h3>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">在以下情形中，您可以向我们提出删除用户信息的请求：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">如果我们处理用户信息的行为违反法律法规；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">如果我们收集、使用您的用户信息，却未征得您的同意；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">3</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">如果我们处理用户信息的行为违反了与您的约定；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">4</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">如果您不再使用我们的产品或服务，或您注销了账号；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">5</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">如果我们不再为您提供产品或服务。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p>&nbsp;</o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							我们将会根据您的删除请求进行评估，若满足相应规定，我们将会采取相应步骤进行处理。当您向我们提出删除请求时，我们可能会要求您进行身份验证，以保障账户的安全。当您从我们的服务中删除信息后，因为适用的法律和安全技术，我们可能不会立即从备份系统中删除相应的信息，我们将安全存储您的信息直到备份可以清除或实现匿名化。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（四）改变您授权同意的范围</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">每个业务功能需要一些基本的用户信息才能得以完成（见本政策</font>"第一部分"）。对于用户信息的收集和使用，您可以随时给予或收回您的授权同意。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							当您收回同意后，我们将不再处理相应的用户信息。同时也请您注意，您撤销授权同意可能会导致某些后果，例如我们可能无法继续为您提供相应的服务或特定的功能，但您收回同意的决定，不会影响此前基于您的授权而开展的用户信息处理。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（五）用户信息主体注销账户</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">您随时可注销此前注册的账户，您可以通过以下方式自行操作：</font>"注销账号"页面（软件设置-账户与安全-注销账号）提交账户注销申请。
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							在注销账户之后，我们将停止为您提供产品或服务并依据您的要求，删除或匿名化您的信息，法律法规另有规定的除外。这也将可能导致您失去对您账户中数据的访问权，请您谨慎操作。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（六）用户信息主体获取用户信息副本</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">在技术可行的前提下，例如数据接口匹配，我们还可按您的要求，直接将您的用户信息副本传输给您指定的第三方。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（七）约束信息系统自动决策</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							在某些业务功能中，我们可能仅依据信息系统、算法等在内的非人工自动决策机制作出决定。如果这些决定显著影响您的合法权益，您有权要求我们作出解释，我们也将提供适当的救济方式。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">（八）响应您的上述请求</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">为保障安全，您可能需要提供书面请求，或以其他方式证明您的身份。我们可能会先要求您验证自己的身份，然后再处理您的请求。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">
							对于您合理的请求，我们原则上不收取费用，但对多次重复、超出合理限度的请求，我们将视情况收取一定成本费用。对于那些无端重复、需要过多技术手段（例如，需要开发新系统或从根本上改变现行惯例）、给他人合法权益带来风险或者非常不切实际的请求，我们可能会予以拒绝。
						</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">也请您理解，出于安全保障的考虑、相关法律法规的要求或技术上的限制，对于您的某些请求我们可能无法做出响应，例如以下情形：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与用户信息控制者履行法律法规规定的义务相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与国家安全、国防安全直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">3</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与公共安全、公共卫生、重大公共利益直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">4</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">与犯罪侦查、起诉、审判和执行判决等直接相关的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">5</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">用户信息控制者有充分证据表明用户信息主体存在主观恶意或滥用权利的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">6</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">出于维护用户信息主体或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">7</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">响应用户信息主体的请求将导致用户信息主体或其他个人、组织的合法权益受到严重损害的；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">8</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">涉及商业秘密的。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<h2 style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        ">
					<b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            ">
							<font face="Segoe UI">六、本政策如何更新</font>
						</span></b><b><span style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            ">
							<o:p></o:p>
						</span></b>
				</h2>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们的隐私政策可能变更。未经您明确同意，我们不会削减您按照本隐私政策所应享有的权利。我们会在本页面上发布对本政策所做的任何变更。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">对于重大变更，我们还会提供更为显著的通知。本政策所指的重大变更包括但不限于：</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">1</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们的服务模式发生重大变化。如处理用户信息的目的、处理的用户信息类型、用户信息的使用方式等；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">2</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们在所有权结构、组织架构等方面发生重大变化。如业务调整、破产并购等引起的所有者变更等；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">3</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">用户信息共享、转让或公开披露的主要对象发生变化；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">4</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">您参与用户信息处理方面的权利及其行使方式发生重大变化；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">5</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">我们负责处理用户信息安全的责任部门、联络方式及投诉渠道发生变化时；</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="p" style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">6</font>
						<font face="宋体">、</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          ">
						<font face="Segoe UI">用户信息安全影响评估报告表明存在高风险时。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="MsoNormal" style="
          margin-top: 3pt;
          margin-bottom: 5pt;
          mso-margin-bottom-alt: auto;
          mso-pagination: widow-orphan;
        ">
					<span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: Calibri;
            mso-hansi-font-family: Calibri;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 10.5pt;
            mso-font-kerning: 1pt;
          ">
						<font face="宋体">我们还会将本政策的旧版本存档，供您查阅。</font>
					</span><span style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: Calibri;
            mso-hansi-font-family: Calibri;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 10.5pt;
            mso-font-kerning: 1pt;
          ">
						<o:p></o:p>
					</span>
				</p>
				<p class="MsoNormal">
					<span style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 10.5pt;
            mso-font-kerning: 1pt;
          ">
						<o:p>&nbsp;</o:p>
					</span>
				</p>
			</div>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				agreement: '',
				title: ''
			};
		},
		onLoad(e) {
			if (e.agreementKey == 'registrationAgreement') {
				this.title = '易达脉联平台注册协议'
				uni.setNavigationBarTitle({
					title: '易达脉联平台注册协议'
				});
			} else if (e.agreementKey == 'management') {
				this.title = '易达脉联平台服务管理规定'
				uni.setNavigationBarTitle({
					title: '易达脉联平台服务管理规定'
				});
			}
			this.getAgreement(e.agreementKey);
		},
		methods: {
			getAgreement(msg) {
				this.$request({
					url: '/config/config',
					method: 'GET',
					data: {
						ident: 'system'
					},
					IsGetStorg: false
				}).then(res => {
					res = JSON.parse(res)
					if (res.code == 1) {
						this.show = true;
						this.agreement = res.data[msg];
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	
	      @font-face {
	        font-family: "Times New Roman";
	      }
	
	      @font-face {
	        font-family: "宋体";
	      }
	
	      @font-face {
	        font-family: "Wingdings";
	      }
	
	      @font-face {
	        font-family: "Calibri";
	      }
	
	      @font-face {
	        font-family: "Segoe UI";
	      }
	
	      @list l0:level1 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%1.";
	        mso-level-tab-stop: 36pt;
	        mso-level-number-position: left;
	        margin-left: 36pt;
	        text-indent: -18pt;
	        tab-stops: blank 36pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level2 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%2.";
	        mso-level-tab-stop: 72pt;
	        mso-level-number-position: left;
	        margin-left: 72pt;
	        text-indent: -18pt;
	        tab-stops: blank 72pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level3 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%3.";
	        mso-level-tab-stop: 108pt;
	        mso-level-number-position: left;
	        margin-left: 108pt;
	        text-indent: -18pt;
	        tab-stops: blank 108pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level4 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%4.";
	        mso-level-tab-stop: 125.85pt;
	        mso-level-number-position: left;
	        margin-left: 144pt;
	        text-indent: -18pt;
	        tab-stops: blank 125.85pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level5 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%5.";
	        mso-level-tab-stop: 161.9pt;
	        mso-level-number-position: left;
	        margin-left: 180pt;
	        text-indent: -18pt;
	        tab-stops: blank 161.9pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level6 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%6.";
	        mso-level-tab-stop: 197.9pt;
	        mso-level-number-position: left;
	        margin-left: 216pt;
	        text-indent: -18pt;
	        tab-stops: blank 197.9pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level7 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%7.";
	        mso-level-tab-stop: 233.9pt;
	        mso-level-number-position: left;
	        margin-left: 252pt;
	        text-indent: -18pt;
	        tab-stops: blank 233.9pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level8 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%8.";
	        mso-level-tab-stop: 269.9pt;
	        mso-level-number-position: left;
	        margin-left: 288pt;
	        text-indent: -18pt;
	        tab-stops: blank 269.9pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l0:level9 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: tab;
	        mso-level-text: "%9.";
	        mso-level-tab-stop: 305.9pt;
	        mso-level-number-position: left;
	        margin-left: 324pt;
	        text-indent: -18pt;
	        tab-stops: blank 305.9pt;
	        font-family: "Times New Roman";
	        font-size: 12pt;
	      }
	
	      @list l1:level1 {
	        mso-level-start-at: 3;
	        mso-level-number-format: chinese-counting;
	        mso-level-suffix: none;
	        mso-level-text: "（%1）";
	        mso-level-tab-stop: none;
	        mso-level-number-position: left;
	        margin-left: 0pt;
	        text-indent: 0pt;
	      }
	
	      @list l2:level1 {
	        mso-level-number-format: decimal;
	        mso-level-suffix: none;
	        mso-level-text: "%1、";
	        mso-level-tab-stop: none;
	        mso-level-number-position: left;
	        margin-left: 0pt;
	        text-indent: 0pt;
	        font-family: "Times New Roman";
	      }
	
	      p.MsoNormal {
	        mso-style-name: 正文;
	        mso-style-parent: "";
	        margin: 0pt;
	        margin-bottom: 0.0001pt;
	        mso-pagination: none;
	        text-align: justify;
	        text-justify: inter-ideograph;
	        font-family: Calibri;
	        mso-fareast-font-family: 宋体;
	        mso-bidi-font-family: "Times New Roman";
	        font-size: 10.5pt;
	        mso-font-kerning: 1pt;
	      }
	
	      h1 {
	        mso-style-name: "标题 1";
	        mso-style-next: 正文;
	        margin-top: 5pt;
	        margin-bottom: 5pt;
	        mso-margin-top-alt: auto;
	        mso-margin-bottom-alt: auto;
	        mso-pagination: none;
	        text-align: left;
	        font-family: 宋体;
	        font-weight: bold;
	        font-size: 24pt;
	        mso-font-kerning: 22pt;
	      }
	
	      h2 {
	        mso-style-name: "标题 2";
	        mso-style-noshow: yes;
	        mso-style-next: 正文;
	        margin-top: 5pt;
	        margin-bottom: 5pt;
	        mso-margin-top-alt: auto;
	        mso-margin-bottom-alt: auto;
	        mso-pagination: none;
	        text-align: left;
	        font-family: 宋体;
	        font-weight: bold;
	        font-size: 18pt;
	      }
	
	      h3 {
	        mso-style-name: "标题 3";
	        mso-style-noshow: yes;
	        mso-style-next: 正文;
	        margin-top: 5pt;
	        margin-bottom: 5pt;
	        mso-margin-top-alt: auto;
	        mso-margin-bottom-alt: auto;
	        mso-pagination: none;
	        text-align: left;
	        font-family: 宋体;
	        font-weight: bold;
	        font-size: 13.5pt;
	      }
	
	      span.10 {
	        font-family: "Times New Roman";
	      }
	
	      span.15 {
	        font-family: "Times New Roman";
	        mso-ansi-font-weight: bold;
	      }
	
	      span.16 {
	        font-family: "Times New Roman";
	        color: rgb(0, 0, 255);
	        text-decoration: underline;
	        text-underline: single;
	      }
	
	      p.p {
	        mso-style-name: "普通\(网站\)";
	        margin: 0pt;
	        margin-bottom: 0.0001pt;
	        mso-pagination: none;
	        text-align: justify;
	        text-justify: inter-ideograph;
	        font-family: Calibri;
	        mso-fareast-font-family: 宋体;
	        mso-bidi-font-family: "Times New Roman";
	        font-size: 12pt;
	        mso-font-kerning: 1pt;
	      }
	
	      span.msoIns {
	        mso-style-type: export-only;
	        mso-style-name: "";
	        text-decoration: underline;
	        text-underline: single;
	        color: blue;
	      }
	
	      span.msoDel {
	        mso-style-type: export-only;
	        mso-style-name: "";
	        text-decoration: line-through;
	        color: red;
	      }
	
	      table.MsoNormalTable {
	        mso-style-name: 普通表格;
	        mso-style-parent: "";
	        mso-style-noshow: yes;
	        mso-tstyle-rowband-size: 0;
	        mso-tstyle-colband-size: 0;
	        mso-padding-alt: 0pt 5.4pt 0pt 5.4pt;
	        mso-para-margin: 0pt;
	        mso-para-margin-bottom: 0.0001pt;
	        mso-pagination: widow-orphan;
	        font-family: "Times New Roman";
	        font-size: 10pt;
	        mso-ansi-language: #0400;
	        mso-fareast-language: #0400;
	        mso-bidi-language: #0400;
	      }
	      @page {
	        mso-page-border-surround-header: no;
	        mso-page-border-surround-footer: no;
	      }
	      @page Section0 {
	        margin-top: 72pt;
	        margin-bottom: 72pt;
	        margin-left: 90pt;
	        margin-right: 90pt;
	        size: 595.3pt 841.9pt;
	        layout-grid: 15.6pt;
	        mso-header-margin: 42.55pt;
	        mso-footer-margin: 49.6pt;
	      }
	      div.Section0 {
	        page: Section0;
	      }

	.agreement {
		height: 100%;
		padding: 0 32rpx;

		.agreement-title {
			padding: 60rpx 0;
			font-size: $font-my-size-44;
			font-weight: bold;
		}

		.agreement-cont {
			width: 100%;
			font-size: 32rpx;
			overflow-y: scroll;

			/deep/p {
				span {
					font-size: 32rpx !important;
				}
			}
		}
	}
</style>