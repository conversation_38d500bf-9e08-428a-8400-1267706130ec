function degreesToRadians(degrees) {
    return degrees * (Math.PI / 180);
}
const R = 6371; // 地球平均半径，单位为公里
function calcDistance(lat1, lon1, lat2, lon2) {
    // 将十进制度数转化为弧度
    const radLat1 = degreesToRadians(lat1);
    const radLon1 = degreesToRadians(lon1);
    const radLat2 = degreesToRadians(lat2);
    const radLon2 = degreesToRadians(lon2);

    // 计算纬度差和经度差
    const dLat = radLat2 - radLat1;
    const dLon = radLon2 - radLon1;

    // haversine公式
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(radLat1) * Math.cos(radLat2) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // 距离，单位为公里

    return distance;
}

const AmapUtil = {
    calcDistance
}

export default AmapUtil
