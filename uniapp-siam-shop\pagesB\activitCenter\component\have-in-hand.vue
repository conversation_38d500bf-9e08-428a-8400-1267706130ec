<template>
	<scroll-view class="scroll-Y" :scroll-top="0" scroll-y="true">
		<view class="haveInHand">
			<view class="haveInHand-cont">
				<navigator url="/pagesB/activitCenter/active-details-firstOrder">
					<u-section title="首单奖" font-size="38" :show-line="false">
						<view slot="right"><text style="font-size: 40rpx; color: #F84C08;">15元</text></view>
					</u-section>
				</navigator>
				<view class="haveInHand-time flex flex-align-center justify-space-between">
					<view>2020.10.23~2020.10.23(剩14小时)</view>
					<view>完成后获得</view>
				</view>
				<my-line></my-line>
				<view class="haveInHand-cont-trip">完成一单即获得首单奖励</view>
			</view>
			<view class="haveInHand-cont">
				<navigator url="/pagesB/activitCenter/active-details-punching">
					<u-section title="冲单奖" font-size="38" :arrow="false" :show-line="false">
						<view slot="right"><text style="font-size: 40rpx; color: #F84C08;">15元</text></view>
					</u-section>
				</navigator>
				<view class="haveInHand-time-stylTwo">
					<view class="haveInHand-time-info">开通了骑士等级的城市根据您的等级对应的权益进行奖励</view>
					<view>2020.10.23~2020.10.23(剩14小时)</view>
				</view>
				<step-bar></step-bar>
				<view class="haveInHand-speed">
					<text>已完成</text>
					<text style="color: #DD723A;">0</text>
					<text>单,再完成</text>
					<text style="color: #DD723A;">50</text>
					<text>单可获得</text>
					<text style="color: #DD723A;">￥150</text>
					<text>奖励！</text>
				</view>
				<view class="haveInHand-condition">
					<view class="haveInHand-condition-title">奖励条件</view>
					<view class="haveInHand-condition-cont">下单时间：00:00:00 - 23:59:59</view>
					<view class="haveInHand-condition-cont">接单区域：取水楼众包商圈</view>
					<view class="haveInHand-condition-cont">订单品类：水产</view>
					<view class="haveInHand-condition-cont">距离区间：>=0km</view>
				</view>
			</view>
		</view>
	</scroll-view>
</template>

<script>
import StepBar from "../compoents/stepBar.vue"

export default {
	components:{
		StepBar
	},
	data() {
		return{
			
		}
	}
};
</script>

<style lang="scss" scoped>
.scroll-Y {
	height: 100%;
}
.haveInHand {
	padding: 20rpx;
	height: 100%;

	.haveInHand-cont {
		padding: 28rpx;
		background: #ffffff;
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0rpx;
		}

		.haveInHand-time {
			height: 80rpx;
			line-height: 80rpx;
			font-size: $font-my-size-26;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: $font-my-color-9;
		}

		.haveInHand-cont-trip {
			font-size: $font-my-size-26;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: $font-my-color-6;
			margin-top: 10rpx;
		}

		.haveInHand-time-stylTwo {
			font-size: $font-my-size-26;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
			margin-top: 10rpx;
			line-height: 48rpx;

			.haveInHand-time-info {
				font-size: $font-my-size-26;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #999999;
			}
		}

		.haveInHand-speed {
			margin-top: 45rpx;
			padding: 20rpx 20rpx;
			background: #f7f7f7;
			border-radius: 10rpx;
		}

		.haveInHand-condition {
			margin-top: 20rpx;
			padding: 20rpx 30rpx;
			background: #f7f7f7;
			border-radius: 10rpx;

			.haveInHand-condition-title {
				font-size: 28rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;
			}

			.haveInHand-condition-cont {
				height: 40rpx;
				line-height: 40rpx;
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #666666;
			}
		}
	}
}
</style>
