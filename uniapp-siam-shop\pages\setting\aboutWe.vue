<template>
	<view class="aboutWe flex flex-direction-column flex-align-center">
		<image class="logo" src="~@/static/image/logotwo.png" mode="widthFix"></image>
		<text>当前版本V{{ version }}</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				version: ""
			}
		},
		onLoad() {
			//#ifdef APP-VUE
			this.version = plus.runtime.version
			//#endif
		}
	}
</script>

<style scoped lang="scss">
	.aboutWe {
		padding-top: 150rpx;

		.logo {
			width: 120rpx;
			padding: 20rpx 0;
		}
	}
</style>
