<template>
	<view class="forget-passWord">
		<view class="register-title">输入需要找回密码的手机号</view>
		<view class="phone">
			<uni-easyinput class="phone-input" type="number" maxlength="11" :inputBorder="false" v-model="tel" placeholder="输入手机号"></uni-easyinput>
		</view>
		<my-button margin-top="60" :bold="true" color="#fff" :disabled="!tel" font-size="40" @confirm="downPage">下一步</my-button>
	</view>
</template>

<script>
export default {
	data() {
		return {
			tel: ''
		};
	},
	methods: {
		downPage() {
			if (this.tel.length === 11) {
				this.$request({
					url: '/login/get-code',
					data: { tel: this.tel, type: 2 },
					IsGetStorg: false
				}).then(res => {
					res = JSON.parse(res)
					if (res.code == 1) {
						uni.navigateTo({
							url: '/pages/login/register-code?type=2&PageType=2&callback=log&tel=' + this.tel
						});
					} else {
						this.$interactive.ShowToast({ title: res.message }, false);
					}
				});
			} else {
				uni.showToast({
					icon: 'none',
					title: '请输入正确的手机号！'
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.forget-passWord {
	.register-title {
		font-size: $font-my-size-30;
		font-weight: bold;
		padding: 40rpx 62rpx 0;
	}

	.phone {
		margin: 40rpx 62rpx 0;
		border-bottom: 1rpx solid $uni-bg-color-grey;

		.phone-input {
			padding: 20rpx 0;
			flex: 1;

			.placeholderStyl {
			}
		}
	}
}
</style>
