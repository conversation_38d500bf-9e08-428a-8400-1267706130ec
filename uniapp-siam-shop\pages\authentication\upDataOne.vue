<template>
	<view class="upDataOne">
		<u-navbar title-size="36" :border-bottom="false" title="核对信息"></u-navbar>
<!--		<view class="upDataID-file">-->
<!--			<image class="upDataID-file-img" v-if="!formData.idCardFrontImg" :src="formData.idCardFrontImg" mode="widthFix" />-->
<!--			<image class="upDataID-file-img" v-else :src="formData.idCardFrontImg" mode="widthFix" />-->
<!--			<view class="upDataID-file-tips">-->
<!--				<text style="font-size: 28rpx; margin-right: 20rpx;">身份证正面</text>-->
<!--				<u-icon name="arrow-right" label="重拍" label-pos="left" label-color="#DB0012" label-size="28" color="#DB0012" size="28" @click="Remake"></u-icon>-->
<!--			</view>-->
<!--		</view>-->
		<view class="upDataOne-cont">
			<view class="title-cont">姓名</view>
			<uni-easyinput class="name-cont" type="text" :inputBorder="false" v-model="formData.name" placeholder="姓名"></uni-easyinput>
		</view>
		<view class="upDataOne-cont">
			<view class="title-cont">身份证号</view>
			<uni-easyinput class="name-cont" type="text" :inputBorder="false" v-model="formData.code" placeholder="身份证号"></uni-easyinput>
		</view>
		<view class="footer-tips">
			<view class="footer-tips-cont">您提交的信息只会用于实名认证审核</view>
			<my-button color="#fff" @confirm="submitData">下一步</my-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				name: '',
				code: '',
				idCardFrontImg: '',
				idCardBackImg: ''
			}
		};
	},
	onLoad(e) {
		const IDInfo = JSON.parse(e.data);
		let obj = {
			name: IDInfo.data ? IDInfo.data['姓名'].words : '',
			code: IDInfo.data ? IDInfo.data['公民身份号码'].words : '',
			idCardFrontImg: IDInfo.IDpicJust ? IDInfo.IDpicJust : '',
			idCardBackImg: IDInfo.IDpicBack ? IDInfo.IDpicBack : ''
		};
		Object.assign(this.formData, obj);
	},
	methods: {
		// 重新拍摄
		Remake() {
			this.$common.file_select().then(res => {
				this.$common.UpLoadFile(res).then(msg => {
					if (msg.length) {
						this.$request({
							url: '/login/distinguish',
							data: {
								image: msg.toString()
							}
						}).then(item => {
							// #ifdef MP-WEIXIN
								item = JSON.parse(item)
							// #endif
							if (item.code == 1) {
								if (item.data.idcard_number_type != -1) {
									this.formData.data = item.data.words_result;
								} else {
									this.formData.data = false;
								}
								this.formData.IDpicJust = msg.toString();
							}
						});
					}
				});
			});
		},
		submitData() {
			console.log(this.formData)
			if(this.formData.name !== '' && this.formData.code !== ''){
				console.log('132456764123123')
				uni.navigateTo({
					url: '/pages/login/perfectInfo?data=' + JSON.stringify(this.formData)
				});
			} else{
				this.$interactive.ShowToast({
					title: '请填写完整'
				},false)
			}

		}
	}
};
</script>

<style lang="scss" scoped>
.upDataOne {
	height: 100vh;

	.upDataID-file {
		width: 626rpx;
		margin: 50rpx auto 0;
		padding: 40rpx 100rpx;
		border: 2rpx dashed #bbbbbb;
		border-radius: 24rpx;

		.upDataID-file-img {
			width: 100%;
			height: 264rpx;
			max-height: 264rpx;
		}

		.upDataID-file-tips {
			padding-top: 20rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			text-align: center;
		}
	}

	.upDataOne-cont {
		padding: 30rpx 70rpx;

		.title-cont {
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: $font-my-color-3;
			margin-bottom: 50rpx;
		}

		.name-cont {
			font-size: 36rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #333333;
		}

		.placeholderStyle {
			font-size: $font-my-size-32;
			color: $font-my-color-9;
		}
	}

	.footer-tips {
		position: fixed;
		bottom: 40rpx;
		left: 0rpx;
		right: 0rpx;

		.footer-tips-cont {
			text-align: center;
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
			margin-bottom: 20rpx;
		}

		.upDataOne-btn {
			width: 650rpx;
			height: 92rpx;
			line-height: 92rpx;
			margin: 0 auto;
			text-align: center;
			background: linear-gradient(90deg, #ff7900 0%, #f01513 100%);
			border-radius: 46rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
		}
	}
}
</style>
