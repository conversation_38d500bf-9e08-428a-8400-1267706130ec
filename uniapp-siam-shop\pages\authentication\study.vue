<template>
	<view class="notice">
		<view class="notice-title">{{ title }}</view>
		<view class="notice-time" v-if="time">发布时间：{{ time }}</view>
		<u-parse :html="content"></u-parse>
		<view class="footer-btn-fiexd"><my-button @confirm="confirm">完成学习</my-button></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: '',
			content: '',
			time: ''
		};
	},
	onLoad(e) {
		this.getNewsInfo(e.id);
	},
	onUnload() {},
	methods: {
		getNewsInfo(id) {
			this.$request({
				url: '/train/learn-info',
				data: { id: id },
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.title = res.data.title;
					this.time = res.data.createdAt ? this.$moment(res.data.createdAt * 1000).format('YYYY-MM-DD HH:mm') : '';
					this.content = res.data.body;
				}
			});
		},
		confirm() {
			uni.navigateTo({
				url: '/pages/authentication/answerOne'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.notice {
	padding: 0rpx 32rpx 120rpx;

	.notice-title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.notice-time {
		padding: 20rpx 0 60rpx;
		color: #999;
	}

	/deep/ p {
		width: 100% !important;
		max-width: 100% !important;
		white-space: pre-wrap !important;
		word-wrap: word-wrap !important;
	}

	/deep/ol,
	/deep/ul,
	/deep/div,
	/deep/a,
	/deep/h3,
	/deep/h4,
	/deep/h5,
	/deep/h6 {
		width: 100%;
		font-size: 32rpx !important;
		padding: 0rpx !important;
		margin: 0rpx !important;

		img {
			width: 100% !important;
			max-width: 100%;
			min-width: 0rpx;
		}

		li {
			font-size: 32rpx !important;
			outline: none;
			list-style: none;
		}
	}
}
</style>
