<template>
	<view class="content">
		<custom-tabs-swiper 
			ref="tabsSwiper" 
			:list="printerTypeList" 
			active-color="#fa7c25" 
			inactive-color="#333" 
			bg-color="#fff"
			:show-line="true" 
			@changePage="changePrinterType">
			<template slot="swiper-item-1">
				<mescroll-uni 
					ref="mescrollRef1" 
					@init="mescrollInit1" 
					@down="downCallback1" 
					@up="upCallback1" 
					:down="{ auto: true }" 
					:up="{ auto: false, hasNext: true }"
					:fixed="false"
					class="mescroll-container">
					<view class="printer-list">
						<view class="printer-card" v-for="(item, index) in printerList1" :key="index">
							<view class="card-header">
								<view class="printer-name">{{ item.name }}</view>
							</view>
							
							<view class="card-body">
								<view class="info-item">
									<text class="label">品牌:</text>
									<text class="value">{{ getBrandName(item.brand) }}</text>
								</view>
								
								<view class="info-item">
									<text class="label">编号:</text>
									<text class="value">{{ item.number }}</text>
								</view>
								
								<view class="info-item">
									<text class="label">识别码:</text>
									<text class="value">{{ item.identifyingCode }}</text>
								</view>
								
								<view class="info-item">
									<text class="label">自动打印:</text>
									<text class="value">{{ item.isAutoPrint ? '是' : '否' }}</text>
								</view>
							</view>
							
							<view class="card-footer">
								<button class="btn-edit" size="mini" @click="editPrinter(item)">编辑</button>
								<button class="btn-delete" size="mini" @click="deletePrinter(item.id)">删除</button>
							</view>
						</view>
					</view>
				</mescroll-uni>
			</template>
			
			<template slot="swiper-item-2">
				<mescroll-uni 
					ref="mescrollRef2" 
					@init="mescrollInit2" 
					@down="downCallback2" 
					@up="upCallback2" 
					:down="{ auto: true }" 
					:up="{ auto: false, hasNext: true }"
					:fixed="false"
					class="mescroll-container">
					<view class="printer-list">
						<view class="printer-card" v-for="(item, index) in printerList2" :key="index">
							<view class="card-header">
								<view class="printer-name">{{ item.name }}</view>
							</view>
							
							<view class="card-body">
								<view class="info-item">
									<text class="label">品牌:</text>
									<text class="value">{{ getBrandName(item.brand) }}</text>
								</view>
								
								<view class="info-item">
									<text class="label">编号:</text>
									<text class="value">{{ item.number }}</text>
								</view>
								
								<view class="info-item">
									<text class="label">识别码:</text>
									<text class="value">{{ item.identifyingCode }}</text>
								</view>
								
								<view class="info-item">
									<text class="label">自动打印:</text>
									<text class="value">{{ item.isAutoPrint === 1 ? '是' : '否' }}</text>
								</view>
							</view>
							
							<view class="card-footer">
								<button class="btn-edit" size="mini" @click="editPrinter(item)">编辑</button>
								<button class="btn-delete" size="mini" @click="deletePrinter(item.id)">删除</button>
							</view>
						</view>
					</view>
				</mescroll-uni>
			</template>
		</custom-tabs-swiper>
		
		<view class="add-button">
			<button type="primary" @click="addPrinter">添加打印机</button>
		</view>
		
		<!-- 删除确认弹窗 -->
		<u-modal v-model="showDeleteModal" title="提示" content="确定要删除这台打印机吗？" @confirm="confirmDelete" @cancel="cancelDelete" :show-cancel-button="true"></u-modal>
	</view>
</template>

<script>
	import CustomTabsSwiper from '../../components/custom-tabs-page-linkge/tab-page-linkage.vue'; // 全屏联动
	
	export default {
		components: {
			CustomTabsSwiper
		},
		data() {
			return {
				printerTypeList: [
					{ name: '小票打印机' },
					{ name: '标签打印机' }
				],
				currentPrinterType: 1, // 1: 小票打印机, 2: 标签打印机
				printerList1: [], // 小票打印机列表
				printerList2: [], // 标签打印机列表
				pageNo1: 1,
				pageSize1: 10,
				total1: 0,
				pageNo2: 1,
				pageSize2: 10,
				total2: 0,
				currentDeleteId: null,
				mescroll1: null,
				mescroll2: null,
				showDeleteModal: false, // u-modal显示控制
				brandList: [
					{ value: '1', label: '飞鹅' },
					{ value: '2', label: '芯烨云' },
					{ value: '3', label: '大趋智能' },
					{ value: '4', label: '商鹏' }
				]
			};
		},
		onLoad() {
			// this.loadPrinterList();
			uni.$on('RefreshPrinterList', () => {
				this.loadPrinterList(1);
				this.loadPrinterList(2);
			})
		},
		destoryed() {
			uni.$off('RefreshPrinterList');
		},
		onShow() {
			// 页面显示时刷新数据
			if (this.mescroll1) {
				this.mescroll1.resetUpScroll();
			}
			if (this.mescroll2) {
				this.mescroll2.resetUpScroll();
			}
		},
		methods: {
			// 切换打印机类型
			changePrinterType(index) {
				this.currentPrinterType = index + 1;
			},
			
			// mescroll1初始化完成的回调
			mescrollInit1(mescroll) {
				this.mescroll1 = mescroll;
			},
			
			// mescroll2初始化完成的回调
			mescrollInit2(mescroll) {
				this.mescroll2 = mescroll;
			},
			
			// 下拉刷新小票打印机
			downCallback1() {
				this.pageNo1 = 1;
				this.loadPrinterList(1).then(() => {
					this.mescroll1.endSuccess();
				}).catch(() => {
					this.mescroll1.endErr();
				});
			},
			
			// 上拉加载小票打印机
			upCallback1() {
				if (this.printerList1.length < this.total1) {
					this.pageNo1++;
					this.loadPrinterList(1).then(() => {
						this.mescroll1.endSuccess();
					}).catch(() => {
						this.mescroll1.endErr();
					});
				} else {
					this.mescroll1.endSuccess(false);
				}
			},
			
			// 下拉刷新标签打印机
			downCallback2() {
				this.pageNo2 = 1;
				this.loadPrinterList(2).then(() => {
					this.mescroll2.endSuccess();
				}).catch(() => {
					this.mescroll2.endErr();
				});
			},
			
			// 上拉加载标签打印机
			upCallback2() {
				if (this.printerList2.length < this.total2) {
					this.pageNo2++;
					this.loadPrinterList(2).then(() => {
						this.mescroll2.endSuccess();
					}).catch(() => {
						this.mescroll2.endErr();
					});
				} else {
					this.mescroll2.endSuccess(false);
				}
			},
			
			loadPrinterList(type) {
				const pageNo = type === 1 ? this.pageNo1 : this.pageNo2;
				const pageSize = type === 1 ? this.pageSize1 : this.pageSize2;
				
				return this.$request({
					url: '/api-util/rest/merchant/printer/list',
					method: 'POST',
					data: {
						pageNo: pageNo,
						pageSize: pageSize,
						type: type // 1: 小票打印机, 2: 标签打印机
					}
				}).then(res => {
					if (res.code === 200) {
						if (type === 1) {
							if (this.pageNo1 === 1) {
								this.printerList1 = res.data.records;
							} else {
								this.printerList1 = this.printerList1.concat(res.data.records);
							}
							this.total1 = res.data.total;
						} else {
							if (this.pageNo2 === 1) {
								this.printerList2 = res.data.records;
							} else {
								this.printerList2 = this.printerList2.concat(res.data.records);
							}
							this.total2 = res.data.total;
						}
						// 通知mescroll数据加载完毕
						return res.data.records;
					} else {
						uni.showToast({
							title: res.message || '获取打印机列表失败',
							icon: 'none'
						});
						return [];
					}
				}).catch(err => {
					uni.showToast({
						title: '网络请求异常',
						icon: 'none'
					});
					return [];
				});
			},
			
			addPrinter() {
				uni.navigateTo({
					url: `/pages/printer/add?type=${this.currentPrinterType}`
				});
			},
			
			editPrinter(printer) {
				// 修改：直接将打印机对象传递到编辑页面，而不是只传递ID
				uni.navigateTo({
					url: `/pages/printer/edit?printerData=${encodeURIComponent(JSON.stringify(printer))}&type=${this.currentPrinterType}`
				});
			},
			
			deletePrinter(id) {
				this.currentDeleteId = id;
				this.showDeleteModal = true; // 显示u-modal
			},
			
			confirmDelete() {
				this.$request({
					url: `/api-util/rest/merchant/printer/delete`,
					data: {
						ids: [this.currentDeleteId]
					},
					method: 'DELETE'
				}).then(res => {
					if (res.code === 200) {
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						// 刷新当前类型列表
						if (this.currentPrinterType === 1) {
							this.pageNo1 = 1;
							this.loadPrinterList(1);
							if (this.mescroll1) {
								this.mescroll1.resetUpScroll();
							}
						} else {
							this.pageNo2 = 1;
							this.loadPrinterList(2);
							if (this.mescroll2) {
								this.mescroll2.resetUpScroll();
							}
						}
					} else {
						uni.showToast({
							title: res.message || '删除失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '网络请求异常',
						icon: 'none'
					});
				});
			},
			
			// 取消删除操作
			cancelDelete() {
				this.currentDeleteId = null;
				this.showDeleteModal = false;
			},
			
			// 获取品牌名称
			getBrandName(value) {
				const brand = this.brandList.find(item => item.value == value);
				return brand ? brand.label : value;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.content {
		padding: 0;
		background-color: #f5f5f5;
		height: 100%;
		position: relative;
	}
	
	.mescroll-container {
		min-height: 100vh;
	}
	
	.printer-list {
		padding: 20rpx;
		padding-bottom: 20rpx; // 确保最后一条数据不会被遮挡
		
		.printer-card {
			background: #fff;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;
			
			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 30rpx;
				border-bottom: 1rpx solid #eee;
				
				.printer-name {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.printer-status {
					font-size: 24rpx;
					padding: 6rpx 16rpx;
					border-radius: 30rpx;
					background-color: #ccc;
					color: #fff;
					
					&.online {
						background-color: #07c160;
					}
				}
			}
			
			.card-body {
				padding: 24rpx 30rpx;
				
				.info-item {
					display: flex;
					margin-bottom: 16rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.label {
						font-size: 26rpx;
						color: #666;
						width: 160rpx;
					}
					
					.value {
						font-size: 26rpx;
						color: #333;
						flex: 1;
					}
				}
			}
			
			.card-footer {
				text-align: right;
				padding: 20rpx 30rpx;
				border-top: 1rpx solid #eee;
				
				.btn-edit {
					background-color: #fff;
					color: #007AFF;
					border: 1rpx solid #007AFF;
					margin-left: 20rpx;
					font-size: 24rpx;
					padding: 10rpx 20rpx;
					height: auto;
					line-height: normal;
					border-radius: 8rpx;
				}
				
				.btn-delete {
					background-color: #fff;
					color: #ff3b30;
					border: 1rpx solid #ff3b30;
					margin-left: 20rpx;
					font-size: 24rpx;
					padding: 10rpx 20rpx;
					height: auto;
					line-height: normal;
					border-radius: 8rpx;
				}
			}
		}
	}
	
	.add-button {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 99;
		
		button {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 32rpx;
		}
	}
</style>