<template>
	<view class="register-code">
		<view class="tips">验证码发送至{{ tel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</view>
		<view class="code-num">
			<my-message-input v-model="code" :maxlength="6" :focus="true" :bold="false" width="100" @finish="finish"></my-message-input>
			<view class="getCode" @click="countDown(true)">{{ seconds == 60 || seconds == 0 ? '获取验证码' : seconds + '秒后获取' }}</view>
		</view>
	</view>
</template>

<script>
import MyMessageInput from '../../components/my-message-input.vue';
import {mapMutations} from 'vuex';

export default {
	components: {
		MyMessageInput
	},
	data() {
		return {
			seconds: 60,
			code: '',
			time: '',
			tel: '',
			type: '',
			PageType: '',
			callback: ''
		};
	},
	/**
	 * @param  e 页面参数 根据不同的参数配置页面信息
	 * @param  e.callback 决定是从设置中心的修改密码页面跳转而来还是登陆页面的忘记密码
	 * @param  e.type type决定验证接口的类型，1-注册验证，2-修改密码验证、手机号登陆时验证，3-修改手机号验证
	 * @param  e.PageType 当type=2时，PageType决定验证完成后是跳转首页还是修改密码页，1-调转首页，2-调转修改密码页
	 * @param  e.callback 当type=2、PageType=2时，callback决定修改密码完成后回返的页面，log返回登录页，set返回设置页面
	 * */
	onLoad(e) {
		this.callback = e.callback;
		this.type = e.type;
		this.tel = e.tel;
		this.PageType = e.PageType;

		if (this.PageType == '1') {
			uni.setNavigationBarTitle({
				title: '输入验证码'
			});
		} else {
			uni.setNavigationBarTitle({
				title: '身份验证(2/3)'
			});
		}
		this.countDown(false);
	},
	onUnload() {
		clearInterval(this.time);
		this.seconds = 60;
	},
	methods: {
		...mapMutations(['setAccount']),
		registrationCodeFinished(mobileCode) {
			uni.navigateTo({
				url: `/pages/login/new-password?tel=${this.tel}&mobileCode=${mobileCode}`
			});
		},
		finish(mobileCode) {
			const obj = {
				mobileCode: mobileCode,
				type: this.type
			};
			if (this.type === '1') {
				obj.mobile = this.tel;
			} else if (this.type === '2') {
				if (this.PageType === '1') {
					obj.registrationId = uni.getStorageSync('registrationID');
				} else {
					obj.mobile = this.tel;
				}
			}
			this.$request({
				url: '/api-merchant/rest/merchant/registerByMobile',
				data: obj,
				IsGetStorg: false
			}).then(res => {
				if (res.code === 200) {
					if (this.type === '1') {
						console.log(res.data.token)
						this.setAccount({
							authorization: res.data.token,
						})

						uni.$emit('RefreshUserInfo');
						uni.$emit('IsOpenGps');
						// 注册
						this.$interactive.ShowToast({ title: '验证成功！' }).then(() => {
							uni.navigateTo({
								// add registration code to url
								url: `/pages/login/new-password?tel=${this.tel}&mobileCode=${mobileCode}`
							});
						});
					} else if (this.type === '2') {
						// 密码修改与手机号登陆
						this.$interactive.ShowToast({ title: this.PageType == '1' ? '登录成功！' : '验证成功！' }).then(() => {
							if (this.PageType === '1') {
								// 绑定极光推送的回调方法
								uni.$emit('SetCalllBackFun');
								// 验证是否打开GPS，获取地理信息
								uni.$emit('IsOpenGps');
								uni.setStorage({
									key: 'RunKey',
									data: res.data,
									success: () => {
										uni.$emit('RefreshUserInfo');
										uni.reLaunch({
											url: '/pages/index/index'
										});
									},
									fail: err => {
										console.log(err);
									}
								});
							} else {
								uni.navigateTo({
									url: '/pages/login/modify-password?callback=' + this.callback + '&tel=' + this.tel
								});
							}
						});
					} else if (this.type === '3') {
						// 修改手机号
						this.$interactive.ShowToast({ title: '验证成功！' }).then(() => {
							uni.navigateTo({
								url: '/pages/setting/new-phone?tel=' + this.tel
							});
						});
					}
				} else {
					this.$interactive.ShowToast(
						{
							title: res.message
						},
						false
					);
				}
			});
		},
		countDown(msg) {
			if (this.seconds == 60) {
				if (msg) {
					const type = ['register', 'findpwd', 'verification']
					this.$request({
						url: '/api-util/rest/smsLog/sendMobileCode',
						data: { mobile: this.tel, type: type[this.type - 1] },
						IsGetStorg: false
					}).then(res => {
						if (res.code == 1) {
							this.seconds = 59;
							this.time = setInterval(() => {
								this.seconds--;
								if (this.seconds === 0) {
									clearInterval(this.time);
									this.seconds = 60;
								}
							}, 1000);
						}
					});
				} else {
					this.seconds = 59;
					this.time = setInterval(() => {
						this.seconds--;
						if (this.seconds === 0) {
							clearInterval(this.time);
							this.seconds = 60;
						}
					}, 1000);
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.register-code {
	text-align: center;
	padding-top: 100rpx;

	.login-pic {
		width: 90rpx;
	}

	.tips {
		padding-top: 40rpx;
	}

	.code-num {
		padding-top: 40rpx;

		.getCode {
			font-size: $font-my-size-23;
			color: $font-my-color-main;
			margin: 0 auto;
			padding: 10rpx 0;
			width: 470rpx;
			text-align: right;
		}
	}
}
</style>
