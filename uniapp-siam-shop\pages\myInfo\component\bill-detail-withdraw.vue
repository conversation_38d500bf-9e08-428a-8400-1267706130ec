<template>
	<view class="bill-l">
		<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false, empty: {tip: '无更多数据' } }">
			<view class="bill-cont">
				<view class="bill-cont-list">
					<template v-for="(item, index) in riderWithdrawBillList">
						<view class="list-cont" :key="index">
							<view class="cont-title flex flex-align-center justify-space-between">
								<text class="cont-title-tirp">{{paymentModeToStr(item.paymentMode)}}</text>
								<text class="cont-title-money">提取:{{item.withdrawAmount}},到账:{{item.actualAmount}},状态:{{auditStatusToStr(item.auditStatus)}}</text>
							</view>
							<view class="cont-label flex flex-align-center justify-space-between">
								<text>派单号:{{item.orderNo || ''}}</text>
								<text>服务费:{{item.platformFee || ''}}</text>
							</view>
							<view class="cont-label flex flex-align-center justify-space-between">
								<text>创建:{{item.createTime || ''}}</text>
								<text>审核时间:{{item.auditTime || ''}}</text>
							</view>
							<view class="cont-label flex flex-align-center justify-space-between">
								<text>审核备注:{{item.auditReason || ''}}</text>
							</view>
							<my-line></my-line>
						</view>
					</template>
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
import JSONbig from 'json-bigint';
export default {
	data() {
		return {
			noMoreDataflag: false,
			riderWithdrawBillList: [
				{
					orderNo: '订单编号',
					withdrawAmount: 1,
					platformFee: 1,
					actualAmount: 10.00,
					auditStatus: 1,
					auditReason: '审核不通过原因',
					auditTime: '审核时间',
					paymentMode: 1,
					createTime: 'xxx'
				}
			]
		};
	},
	methods:{
		auditStatusToStr(auditStatus) {
			let str = null;
			switch (auditStatus) {
				case 1:
					str = '平台处理中';
					break;
				case 2:
					str = '到账成功';
					break;
				case 3:
					str = '审核不通过';
					break;
				default:
					str = '';
			}
			return str;
		},
		paymentModeToStr(paymentMode) {
			let str = null;
			switch (paymentMode) {
				case 1:
					str = '微信';
					break;
				case 2:
					str = '支付宝';
					break;
				case 3:
					str = '银行';
					break;
				default:
					str = '';
			}
			return str;
		},
		mescrollInit(mescroll) {
		  this.mescroll = mescroll;
		},
		// 下拉回调函数
		downCallback(e) {
			this.mescroll.resetUpScroll();
		},
		// 上拉加载更多
		upCallback(e) {
			this.getRiderWithdrawBillList(e.num)
		},
		getRiderWithdrawBillList(page = 1) {
			this.$request({
                url: '/api-merchant/rest/merchant/merchantWithdrawRecord/list',
				data: {
					pageNo: page,
					pageSize: 10
				}
			}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSONbig.parse(res)
				// #endif
				if (res.code === 200) {
					if (page === 1) {
						this.riderWithdrawBillList = res.data.records;
					} else {
						this.riderWithdrawBillList = this.riderWithdrawBillList.concat(res.data.records);
					}
					if (this.riderWithdrawBillList && this.riderWithdrawBillList.length >= res.data.total) {
						this.noMoreDataflag = true;
					} else {
						this.noMoreDataflag = false;
					}
					this.mescroll.endSuccess(res.data.total, res.data.total > page * 10 ? true : false);
				} else {
					this.mescroll.endSuccess(0, false);
					this.$interactive.ShowToast(
							{
								title: res.message
							},
							false
					);
				}
			});
		},
	}
};
</script>

<style lang="scss" scoped>
.bill-l {
	height: 100%;
	background: $uni-bg-color-grey;

	.bill-cont {
		margin-bottom: 300rpx;

		.bill-cont-list {
			.list-title {
				padding: 20rpx 32rpx;

				font-size: 28rpx;

				.list-title-time {
					color: #333;
				}
				.list-title-label {
					color: #999;
					font-size: 24rpx;
				}
			}
			.list-cont {
				padding: 20rpx 32rpx;
				background: #ffffff;
				.cont-title {
					font-size: 32rpx;
					font-weight: bold;
					.cont-title-tirp {
						color: #333;
					}
					.cont-title-money {
						color: #f0ad4e;
					}
				}
				.cont-label {
					font-size: 28rpx;
					padding: 10rpx 0;
					color: #999;
				}
			}
		}
	}
}
</style>
