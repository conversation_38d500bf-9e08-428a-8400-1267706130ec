<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no">
    <title>百度地图选择位置</title>
    <style>
        body, html {
            width: 100%;
            height: 100%;
            margin: 0;
            font-family: "微软雅黑";
        }
        #container {
            width: 100%;
            height: calc(100% - 50px);
        }
        #searchBox {
            padding: 10px;
            font-size: 14px;
            display: flex;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        #searchInput {
            flex: 1;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        #searchButton {
            padding: 8px 15px;
            margin-left: 10px;
            background: #409EFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #confirmButton {
            padding: 8px 15px;
            margin-left: 10px;
            background: #67C23A;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            float: right;
        }
        #resultBox {
            position: absolute;
            top: 60px;
            left: 10px;
            right: 10px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 999;
            display: none;
        }
        .result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        .result-item:hover {
            background: #f5f5f5;
        }
    </style>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=y4vTf7gKLrCglOQGoYVjiwXt4m4pguXj"></script>
    <script type="text/javascript" src="./js/uni.js"></script>
</head>
<body>
    <div id="searchBox">
        <input type="text" id="searchInput" placeholder="请输入地址搜索位置">
        <button id="confirmButton">确认选择</button>
    </div>
    <div id="resultBox"></div>
    <div id="container"></div>
    <script type="text/javascript">
        // 初始化地图
        function initMap(point) {
            map = new BMap.Map("container");
            var point = new BMap.Point(point.lng, point.lat); // 默认北京天安门
            map.centerAndZoom(point, 15);
            map.enableScrollWheelZoom(true);

            // 添加地图控件
            map.addControl(new BMap.NavigationControl());
            map.addControl(new BMap.ScaleControl());
            
            // 地图点击事件
            map.addEventListener("click", function(e) {
                setMarker(e.point);
            });
        }

        // 当前选中的点
        var currentMarker = null;
        var selectedLocation = null;
        
        // 搜索定时器
        var searchTimer = null;


        // 确认选择按钮事件
        document.getElementById("confirmButton").addEventListener("click", confirmSelection);

        // 设置标记点
        function setMarker(point) {
            // 清除之前的标记
            if (currentMarker) {
                map.removeOverlay(currentMarker);
            }
            
            // 添加新标记
            currentMarker = new BMap.Marker(point);
            map.addOverlay(currentMarker);
            
            // 保存选中位置信息
            selectedLocation = {
                lng: point.lng,
                lat: point.lat,
                address: ''
            };
            
            // 获取地址信息
            var geoc = new BMap.Geocoder();
            geoc.getLocation(point, function(rs) {
                if (rs) {
                    selectedLocation.address = rs.address;
                    selectedLocation.province = rs.addressComponents.province;
                    selectedLocation.city = rs.addressComponents.city;
                    selectedLocation.district = rs.addressComponents.district;
                    selectedLocation.street = rs.addressComponents.street;
                    selectedLocation.streetNumber = rs.addressComponents.streetNumber;
                }
            });
            
            map.panTo(point);
        }

        // 搜索位置

        // 使用百度地图自动完成搜索功能
        function initAutocomplete() {
            var autocomplete = new BMap.Autocomplete({
                "input": "searchInput",
                "location": map
            });
            
            autocomplete.addEventListener("onconfirm", function(e) {
                var value = e.item.value;
                var address = value.province + value.city + value.district + value.street + value.business;
                
                // 使用地理编码获取具体坐标
                var geoc = new BMap.Geocoder();
                geoc.getPoint(address, function(point) {
                    if (point) {
                        map.centerAndZoom(point, 16);
                        setMarker(point);
                        document.getElementById("resultBox").style.display = "none";
                    }
                });
            });
        }

        // 确认选择
        function confirmSelection() {
            if (!selectedLocation) {
                alert("请先在地图上选择位置");
                return;
            }
            // 使用uni.webView.navigateTo通信
            try {
                uni.postMessage({
                    data: {
                        type: 'selectedLocation',
                        location: selectedLocation
                    }
                });
            } catch (e) {
                console.error('uni.webView.postMessage failed', e);
                // 降级使用postMessage
                window.parent.postMessage({
                    type: 'selectedLocation',
                    location: selectedLocation
                }, '*');
            }
        }

        // 获取用户当前位置
        function getCurrentPosition() {
            var geolocation = new BMap.Geolocation();
            geolocation.getCurrentPosition(function(r) {
                initMap(r.point || {lng: 112.95750536884121, lat: 27.784982534656113});
                initAutocomplete(); // 初始化自动完成搜索
            }, {
                enableHighAccuracy: true
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function(){
            getCurrentPosition();
        });
    </script>
</body>
</html>
