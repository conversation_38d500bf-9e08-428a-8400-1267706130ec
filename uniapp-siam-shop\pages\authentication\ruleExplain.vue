<!-- <template>
	<view class="ruleExplain">
		<u-navbar title-size="36" :border-bottom="false" title="规则说明-已经弃用"></u-navbar>
		<view class="ruleExplain-cont">
			<view class="ruleExplain-cont-title">检测规则说明-已经弃用</view>
			<view class="ruleExplain-cont-title-one">一、为什么要检测</view>
			<view class="ruleExplain-cont-info">1.为了确保骑士在跑单时由于本人和账号一致，减少由此类问题导致的理赔困难问题。</view>
			<view class="ruleExplain-cont-info">2.确保跑单的骑士都是经过平台培训、有配送资质的骑士，提高平台整体形象和配送质量。</view>
			<view class="ruleExplain-cont-title-one">二、检测指导</view>
			<view class="ruleExplain-cont-info">1.为了确保骑士在跑单时由于本人和账号一致，减少由此类问题导致的理赔困难问题。</view>
			<view class="ruleExplain-cont-info">2.确保跑单的骑士都是经过平台培训、有配送资质的骑士，提高平台整体形象和配送质量。</view>
			<view class="ruleExplain-cont-title-one">三、审核规则</view>
			<view class="ruleExplain-cont-info">1.使用账号的骑士需与该账号注册时提交的身份证保持一致</view>
			<view class="ruleExplain-cont-info">2.确保跑单的骑士都是经过平台培训、有配送资质的骑士，提高平台整体形象和配送质量。</view>
		</view>
	</view>
</template>

<script></script>

<style lang="scss" scoped>
.ruleExplain {
	.ruleExplain-cont {
		padding: 40rpx 40rpx;
		
		.ruleExplain-cont-title {
			font-size: 42rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #333333;
		}
		
		.ruleExplain-cont-title-one{
			font-size: 34rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #333333;
		}
		
		.ruleExplain-cont-info{
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #666666;
			line-height: 50rpx;
			text-align: justify;
			margin-bottom: 20rpx;
		}
	}
}
</style>
 -->