<script>
// import mp3 from '@/static/audio/newsUrl.mp3';
import { mapMutations } from 'vuex';
import siteInfo from '@/common/siteinfo'
import utils from '@/common/utils.js'
import store from "./store";
import JSONbig from 'json-bigint';

// #ifndef H5 || MP-WEIXIN
var jyJPush = uni.requireNativePlugin('JY-JPush');
// #endif

var RunKey = uni.getStorageSync('RunKey');
export default {
	data() {
		return { time: null }
	},
	globalData: {
		siteInfo
	},
	onLaunch() {
		this.isLogin(); // 检测是否登录
		// this.getConfigInfoFun(); // 获取全局设置信息
		this.getUserInfo(); // 获取全局个人信息
		uni.$on('RefreshUserInfo', this.getUserInfo); //
		// uni.$on('RefreshConfigInfo', this.getConfigInfoFun);
		// uni.$on('RefreshAddressInfo', this.isOpenGps);
		// uni.$on('SetCalllBackFun', this.setCalllBackFun);
		// uni.$on('IsOpenGps', this.isOpenGps);
		// // #ifndef APP-PLUS
		// uni.$on('openLoactionUpdate', this.openLoactionUpdate);
		// // this.registBackgroundMusic(); // 初始化消息通知音频
		// this.openLoactionUpdate();
		// // #endif

		// todo: for test only, to be deleted
		this.getAddress();
	},
	onShow: function() {
		if (RunKey) {
			this.isOpenGps();
		}
	},
	onHide: function() {
		console.log('App Hide');
	},
	methods: {
		...mapMutations(['setAccount','getAddressInfo', 'setUserInfo', 'getConfigInfo']),
		isLogin() { // 是否登录
			this.setAccount({
				isDev: this.globalData.siteInfo.isDev,
				domainUrl: this.globalData.siteInfo.siteroot,
				uniacid: this.globalData.siteInfo.uniacid,
				picRoot: this.globalData.siteInfo.picRoot
			});
			uni.getStorage({
				key: 'account',
				complete: res => {
					console.log(res)
					if (res.data.userId) {
						uni.reLaunch({ url: '/pages/index/index' });
					} else {
						var loginUrl = '/pages/login/login'
						uni.reLaunch({ url: loginUrl });
					}
					// #ifndef H5 || MP-WEIXIN
					setTimeout(() => { plus.navigator.closeSplashscreen(); }, 2500);
					//#endif
				}
			});
			// #ifdef APP-PLUS
			this.setCalllBackFun() // 绑定回调方法
			setTimeout(() => { plus.navigator.closeSplashscreen(); }, 2000)
			// #endif
		},
		isOpenGps() { // 是否打开
			/* #ifdef APP-PLUS */
			var context = plus.android.importClass('android.content.Context');
			var locationManager = plus.android.importClass('android.location.LocationManager');
			var main = plus.android.runtimeMainActivity();
			var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
			if (!mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)) {
				uni.showModal({
					title: '提示',
					content: '请打开定位服务功能',
					showCancel: false, // 不显示取消按钮
					success() {
						if (!mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)) {
							var Intent = plus.android.importClass('android.content.Intent');
							var Settings = plus.android.importClass('android.provider.Settings');
							var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
							main.startActivity(intent); // 打开系统设置GPS服务页面
						} else {
							this.getAddress();
						}
					}
				});
			} else {
				this.getAddress();
			}
			/* #endif */
		},
		getAddress() { // 获取位置信息
			// for local test: 地理位置有变化，就去更新订单信息
			/*let i = 0;
			setInterval(() => {
				i = i + 0.001
				this.getAddressInfo({
					city: '长沙',
					lat: 28.232363 + i,
					lng: 112.885538
				});
			}, 1000);
			return*/

			if (!this.time) {
				this.time = setInterval(() => {
					uni.getLocation({
						type: 'gcj02',
						geocode: true,
						complete: msg => {
							console.log('getLocation:', msg)
							if (msg.errMsg == 'getLocation:ok') {
								// this.setLatLng(msg); todo: update server rider's location
								const obj = {
									//#ifdef APP-PLUS
									address: msg.address.province + msg.address.city + msg.address.district + msg.address.street + msg.address.poiName,
									city: msg.address.city,
									//#endif
									lat: msg.latitude,
									lng: msg.longitude
								};
								this.getAddressInfo(obj);
								this.getNewOrderInfo();
							}
						}
					});
				}, 10000);
			}
		},
		setLatLng(msg) { // 设置位置信息
			const account = uni.getStorageSync("account");
			if(!account.userId) return;
			this.$request({
				url: '/login/modify-coordinate',
				method: 'POST',
				data: {
					type: 'coordinate',
					lat: msg.latitude,
					lng: msg.longitude
				}
			}).then(res => {
				// console.log(res);
			}).catch(e => {
				console.error('setLatLng:', e)
			});
		},
		getConfigInfoFun() { // 获取骑手设置信息
			this.$request({ url: '/config/config', data: { ident: 'rider' }, method: 'get' }).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.getConfigInfo(res.data);
				}
			});
		},
		getUserInfo(msg) { // 获取个人信息
			this.$request({url: '/api-merchant/rest/merchant/getLoginMerchantInfo', data: {} }).then(res => {
				// #ifdef MP-WEIXIN
				res = JSONbig.parse(res)
				// #endif
				if (res.code === 200) {
					// 这是门店账号ID
					const userInfo = Object.assign({}, res.data, {userId: res.data.id});
					this.setAccount(userInfo);

					res.data.callbackType = msg; // 保存个人信息到全局
					this.setUserInfo(res.data);

					// for test only
					// res.data = Object.assign(res.data, {isWork: true})
					// this.setUserInfo(res.data);

					// for test only
					// this.setUserInfo({
					// 	"state": 2,
					// 	"ranking": 1,
					// 	"todayIncome": "0.00",
					// 	"orderNum": 0,
					// 	iswork: '1'
					// });
				} else {
					this.$interactive.ShowToast({ title: '获取用户信息失败，请重新登录！'}) .then(() => {
						uni.reLaunch({ url: '/pages/login/login' });
					});
				}
			});
		},
		registBackgroundMusic(){ // 订单推送
			const account = uni.getStorageSync('account')
			//if
			this.mp3Url = 'https://' + account.domainUrl +(account.isDev == 1 ? '/addons/yb_o2ov2/' : '/') + 'web/static/audio/'
			this.bgAudioMannager = uni.getBackgroundAudioManager();
			this.bgAudioMannager.title = '订单通知';
			this.bgAudioMannager.src = this.mp3Url + 'void.mp3';
			this.bgAudioMannager.onEnded(()=>{
				this.bgAudioMannager.src = this.mp3Url + 'void.mp3';
			})
		},
		// #ifdef MP-WEIXIN
		openLoactionUpdate(){ // 判断后台定位功能是否打开 用来避免小程序切后台被杀进程
			let that = this
			function getOrder(res){
				wx.onLocationChange(data=>{ that.getNewOrderInfo() })
			}
			uni.getSetting({
				success:res=>{
					console.log(res);
					if(!res.authSetting['scope.userLocationBackground']){
						uni.showModal({
							title: '提示',
							content: '位置信息授权时需要选择,使用小程序期间和离开小程序后选项',
							showCancel: false, // 不显示取消按钮
							success() {
								uni.openSetting({
									complete:res=>{
										if(res.authSetting['scope.userLocationBackground']){
											that.getAddress();
											wx.startLocationUpdateBackground({success:getOrder})
										}
									}
								})
							}
						});
					}else{
						that.getAddress();
						wx.startLocationUpdateBackground({success:getOrder})
					}
				}
			})
		},
		// #endif
		async fetchDataAndPalyMusic(data){
			// #ifdef APP-PLUS
			// this.getSystem();
			await this.getOrderConfigInfo()
			const autop = uni.createInnerAudioContext();
			autop.src = mp3
			autop.onCanplay(a => {
				autop.loop = true;
				autop.play();
				if (this.ConfigInfo.newOrder != 0) {
					let leng = autop.duration * 1000 * this.ConfigInfo.newOrder;
					if (this.ConfigInfo.newOrder != 3) {
						setTimeout(() => {
							autop.loop = false;
							autop.stop();
						}, leng);
					}
				}
			});
			// #endif

			// #ifndef APP-PLUS
			if(data.voiceType === 'newOrder'){
				this.bgAudioMannager.src =  this.mp3Url + 'newsUrl.mp3'
			}else if(data.voiceType === 'cancelOrder'){
				this.bgAudioMannager.src = this.mp3Url + 'cancelOrderUrl.mp3';
			}else{
				// this.bgAudioMannager.src =  this.mp3Url + 'newsUrl.mp3'
				// 不提示声音
				this.bgAudioMannager.src =  this.mp3Url + 'void.mp3'
			}
			this.bgAudioMannager.play()
			setTimeout(()=>{
				this.bgAudioMannager.src = this.mp3Url + 'void.mp3';
				this.bgAudioMannager.pause()
			},6000)
			// #endif

		},
		getNewOrderInfo: utils.throttle(async function(e) { // 获取新订单info
			var that=this;
			const account = uni.getStorageSync("account");
			if(!account || !account.data || !account.data.userId) return;	// 未登录不请求

			uni.$emit('RefreshOrderlist')

			// long-term: 这里的播放音乐先注释掉
			return;
			let params =  {
				page: 1,
				state: 1,
				lat: that.$store.state.address.lat,
				lng: that.$store.state.address.lng
			};
			that.$request({ url: '/rider-order/new-order-info', method: 'GET', data: params }).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					that.fetchDataAndPalyMusic(res.data)
				}
			}).catch(err => {
				console.log('neworderinfo',err)
			});
		}, 8000),
		setCalllBackFun() { // 绑定回调方法
			//#ifndef H5
			this.generateRandomNum(); // 生成随机数，连接极光推送
			//#endif
			if (!jyJPush) return;
			// 获取 registrationID， 返回的数据会有registrationID，errorCode
			jyJPush.getRegistrationID(result => {
				if (result.errorCode == 0) {
					let params =  {
						type: 'registration',
						registrationId: result.registrationID
					};
					this.$request({ url: '/login/modify-coordinate', method: 'POST', data: params }).then(res => {
						console.log(res);
					});
				}
			});
			//监听成功后，若收到推送，会在result返回对应的数据；数据格式保持极光返回的安卓/iOS数据一致
			jyJPush.addJYJPushReceiveNotificationListener(result => {
				uni.$emit('RefreshOrderlist');
				const autop = uni.createInnerAudioContext();
				autop.src = mp3;
				autop.onCanplay(a => {
					autop.loop = true;
					autop.play();
					let leng = autop.duration * 3000;
					setTimeout(() => {
						autop.loop = false;
						autop.stop();
					}, leng);
				});
				console.log(result);
			});
			// 监听成功后，若点击推送消息，会触发result；数据格式保持极光返回的安卓/iOS数据一致
			jyJPush.addJYJPushReceiveOpenNotificationListener(result => {
				uni.reLaunch({ url: '/pages/index/index' });
			});
		},
		generateRandomNum() { // 生成随机签名
			uni.getStorage({
				key: 'jyJPushUuid',
				success: res => {
					// #ifndef H5
					this.getConnectjyJPush(); // 调用极光推送
					// #endif
				},
				fail: err => {
					const s = [];
					const hexDigits = '0123456789abcdef';
					for (let i = 0; i < 36; i++) {
						s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
					}
					s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
					s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
					s[8] = s[13] = s[18] = s[23] = '-';
					const uuid = s.join('');
					const uuid1 = uuid.replace(/\-/g, '');
					uni.setStorage({
						key: 'jyJPushUuid',
						data: uuid1,
						complete: () => {
							// #ifndef H5
							this.getConnectjyJPush();
							// #endif
						}
					});
				}
			});
		},
		getConnectjyJPush() { // 连接极光推送
			const uuid = uni.getStorageSync('jyJPushUuid');
			if (!jyJPush) return;
			jyJPush.setJYJPushAlias(
				{ userAlias: uuid },
				result => {
					console.log(result);
					if (result.iResCode != 0) {
						setTimeout(() => { this.getConnectjyJPush(); }, 1000);
					}
				}
			);
		}
	}
};
</script>

<style lang="scss">
	/* #ifndef APP-NVUE */
	/*每个页面公共css */
	@import '@/common/iconfont/iconfont.css';
	@import 'uview-ui/index.scss';

	* {
		padding: 0rpx;
		margin: 0rpx;
	}
	/* #endif */
</style>
