<template>
	<view class="activitCenter">
		<custom-tab-swiper :pageLength="3" :list="list" active-color="#FA4C07" :showLine="false">
			<template slot="swiper-item-1">
				<have-in-hand></have-in-hand>
			</template>
			<template slot="swiper-item-2">
				<event-pre-sale></event-pre-sale>
			</template>
			<template slot="swiper-item-3">
				<history-ativit></history-ativit>
			</template>
		</custom-tab-swiper>
	</view>
</template>

<script>
import CustomTabSwiper from "@/components/custom-tabs-page-linkge/tab-page-linkage.vue"
import HaveInHand from './component/have-in-hand.vue';
import EventPreSale from './component/event-pre-sale.vue';
import HistoryAtivit from './component/history-activit.vue';

export default {
	components: {
		CustomTabSwiper,
		HaveInHand,
		EventPreSale,
		HistoryAtivit
	},
	data() {
		return {
			current: 0,
			swiperCurrent: 0,
			list: [
				{
					name: '进行中'
				},
				{
					name: '活动预告'
				},
				{
					name: '我的活动'
				}
			]
		};
	},
	methods: {
	}
};
</script>

<style lang="scss" scoped>
.activitCenter {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: $uni-bg-color-grey;

	.swiper {
		flex: 1;
	}
}
</style>
