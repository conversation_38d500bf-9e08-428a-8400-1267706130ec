<template>
	<view class="upDataID">
		<view class="upDataID-tips">为保证配送无法安全监管，请上传真实身份证件</view>
		<view class="upDataID-file">
			<image class="upDataID-file-img" v-if="!IDpicJust" src="./static/<EMAIL>" mode="widthFix"></image>
			<image class="upDataID-file-img" v-else :src="IDpicJustPath" mode="widthFix" />
			<view class="upDataID-file-tips">
				<text style="font-size: 28rpx; margin-right: 20rpx;">身份证正面</text>
				<u-icon
					name="arrow-right"
					:label="IDpicJust ? '重拍' : '去拍摄'"
					label-pos="left"
					label-color="#DB0012"
					label-size="28"
					color="#DB0012"
					size="28"
					@click="handClickShot('IDpicJust')"
				></u-icon>
			</view>
		</view>
		<view class="upDataID-file">
			<image class="upDataID-file-img" v-if="!IDpicBack" src="./static/<EMAIL>" mode="widthFix"></image>
			<image class="upDataID-file-img" v-else :src="IDpicBackPath" mode="widthFix" />
			<view class="upDataID-file-tips">
				<text style="font-size: 28rpx; margin-right: 20rpx;">身份证反面</text>
				<u-icon
					name="arrow-right"
					:label="IDpicBack ? '重拍' : '去拍摄'"
					label-pos="left"
					label-color="#DB0012"
					label-size="28"
					color="#DB0012"
					size="28"
					@click="handClickShot('IDpicBack')"
				></u-icon>
			</view>
		</view>
		<my-button margin-top="100" color="#fff" :disabled="IDpicJust == '' || IDpicBack == ''" @confirm="pageDowm">下一步</my-button>
	</view>
</template>

<script>
import { pathToBase64 } from '@/common/image-tools/index.js';
var lyBDOCR;
export default {
	data() {
		return {
			IDpicJust: '',
			IDpicJustPath: '',
			IDpicBack: '',
			IDpicBackPath: '',
			ObjData: {}
		};
	},
	onLoad() {
		// lyBDOCR = uni.requireNativePlugin('longyoung-BDOCR');
	},
	methods: {
		handClickShot(key) {
			this.$common.file_select().then(res => {
				// 图片回显
				this[key + 'Path'] = res.toString();
				this.$common.UpLoadFile(res).then(uploadRes => {
					console.log(key,'-uploadRes:', uploadRes)
					if (uploadRes.length) {
						this[key] = uploadRes[0];
						console.log(key, this[key])
						// 正面去识别反面不识别
						// if (key == 'IDpicJust') {
						// 	this.$request({
						// 		url: '/login/distinguish',
						// 		data: {
						// 			image: this[key]
						// 		}
						// 	}).then(item => {
						// 		// #ifdef MP-WEIXIN
						// 			item = JSON.parse(item)
						// 		// #endif
						// 		if (item.code == 1) {
						// 			if (item.data.idcard_number_type != -1) {
						// 				this.ObjData.data = item.data.words_result;
						// 			} else {
						// 				this.ObjData.data = false;
						// 			}
						// 		}
						// 	});
						// }
					}
				});
			});
		},
		// 下一页
		pageDowm() {
			this.ObjData.IDpicJust = this.IDpicJust;
			this.ObjData.IDpicBack = this.IDpicBack;
			uni.navigateTo({
				url: '/pages/authentication/upDataOne?data=' + JSON.stringify(this.ObjData)
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.upDataID {
	padding-top: 40rpx;

	.upDataID-tips {
		text-align: center;
		font-size: $font-my-size-28;
		font-weight: bold;
		color: $font-my-color-3;
	}

	.upDataID-file {
		width: 626rpx;
		margin: 50rpx auto 0;
		padding: 40rpx 100rpx;
		border: 2rpx dashed #e1e1e1;
		border-radius: 24rpx;

		.upDataID-file-img {
			width: 100%;
			height: 264rpx;
			max-height: 264rpx;
		}

		.upDataID-file-tips {
			padding-top: 20rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			text-align: center;
		}
	}
}
</style>
