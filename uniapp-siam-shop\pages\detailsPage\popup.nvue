<template>
	<view class="box">
		<view class="title-name">取件</view>
		<template v-if="orderInfo.state == 3">
			<view class="contbox" v-if="configInfo.pickUpType == 1">
				<input class="input-styl" type="number" v-model="code" max-length="10" placeholder="请输入取件码取货" placeholder-style="font-size: 16rpx;" />
				<text class="trip">请输入取件码取货</text>
			</view>
			<view class="contbox" v-else>
				<view class="photograph" v-if="!pickImg" @click="plusempty('pickImg')">
					<uni-icons class="photograph-icon" type="plusempty" size="40" @click.stop="plusempty('pickImg')"></uni-icons>
				</view>
				<image class="photograph photographTwo" :src="pickImg" mode="center" v-else @click="plusempty('pickImg')"></image>
				<text class="trip">请拍照取货</text>
			</view>
		</template>
		<template v-else-if="orderInfo.state == 4">
			<view class="contbox" v-if="configInfo.serviceType == 1">
				<input class="input-styl" type="number" v-model="code" max-length="10" placeholder="请输入收货码收货" placeholder-style="font-size: 16rpx;" />
				<text class="trip">请输入收货码收货</text>
			</view>
			<view class="contbox" v-else>
				<view class="photograph" v-if="!endImg" @click="plusempty('endImg')">
					<uni-icons class="photograph-icon" type="plusempty" size="40" @click.stop="plusempty('endImg')"></uni-icons>
				</view>
				<image class="photograph photographTwo" :src="endImg" mode="center" v-else @click="plusempty('endImg')"></image>
				<text class="trip">请拍照收货</text>
			</view>
		</template>
		<view class="footer-box">
			<button class="footer-btn" @click="submitData"><text class="btn-text">确认收货</text></button>
		</view>
	</view>
</template>

<script>
import { file_select, UpLoadFile } from '@/common/method/common.js';
import { ShowToast } from '@/utils/interactive/interactive.js';
import { mapState } from 'vuex';
export default {
	data() {
		return {
			key: '',
			code: '',
			pickImg: '',
			endImg: '',
			orderInfo: {}
		};
	},
	computed: {
		...mapState(['configInfo'])
	},
	onLoad() {
		uni.$on('OrderInfo', this.getOrderInfo);
	},
	onUnload() {
		uni.$off('OrderInfo');
	},
	methods: {
		getOrderInfo(msg) {
			this.orderInfo = msg;
		},
		init() {
			this.$refs.popRes.open();
		},
		close() {
			this.$refs.popRes.close();
		},
		submitData() {
			if (this.orderInfo.state == 3) {
				if (this.configInfo.pickUpType == 1) {
					this.submitTwo('pickCode', 1);
				}else{
					this.submitTwo('', 2);
				}
			} else if (this.orderInfo.state == 4) {
				if (this.configInfo.serviceType == 1) {
					this.submitTwo('receiveCcode', 1);
				} else {
					this.submitTwo('', 2);
				}
			}
		},
		submitTwo(msg, type) {
			if (type == 1) {
				if (this.orderInfo[msg] == this.code) {
					uni.$emit('callback');
				} else {
					ShowToast(
						{
							title: '取货码不准确，请重新输入！'
						},
						false
					);
				}
			} else if (type == 2) {
				if (this.orderInfo.state == 3) {
					if (this.pickImg) {
						uni.$emit('callback', this.pickImg);
					} else {
						ShowToast(
							{
								title: '请先上传图片！'
							},
							false
						);
					}
				} else if (this.orderInfo.state == 4) {
					if (this.endImg) {
						uni.$emit('callback', this.endImg);
					} else {
						ShowToast(
							{
								title: '请先上传图片！'
							},
							false
						);
					}
				}
			}
		},
		plusempty(key) {
			file_select({ sourceType: ['album', 'camera'] }).then(res => {
				UpLoadFile(res).then(msg => {
					this[key] = msg.toString();
				});
			});
		}
	}
};
</script>

<style scoped>
.box {
	flex: 1;
	height: 600rpx;
	width: 750rpx;
	padding-bottom: 50rpx;
	background-color: #ffffff;
}
.title-name {
	font-size: 42rpx;
	padding-top: 20rpx;
	padding-left: 20rpx;
	padding-bottom: 20rpx;
	padding-right: 20rpx;
	font-weight: bold;
}

.contbox {
	align-items: center;
}

.input-styl {
	width: 600rpx;
	margin-top: 40rpx;
	border-width: 1rpx;
	border-color: #999;
	border-style: solid;
	height: 80rpx;
	padding-left: 30rpx;
	border-radius: 10rpx;
	font-size: 30rpx;
	color: #333333;
	font-weight: 400;
}
.trip {
	text-align: center;
	font-size: 24rpx;
	margin-top: 30rpx;
	color: #999;
}

.photograph {
	width: 200rpx;
	height: 200rpx;
	justify-content: center;
	border-style: solid;
	border-radius: 10rpx;
	border-width: 1rpx;
	border-color: #999;
}
.photographTwo {
	border-width: 0rpx;
}

.photograph-icon {
	margin-right: 12rpx;
	margin-top: 10rpx;
}

.footer-box {
	flex-direction: row;
	width: 750rpx;
	align-items: center;
	justify-content: center;
	margin-top: 40rpx;
}

.footer-btn {
	background-color: rgb(236, 22, 19);
	width: 600rpx;
	height: 86rpx;
	border-radius: 60rpx;
	justify-content: center;
	align-items: center;
	border-width: 0rpx;
}

.btn-text {
	font-size: 36rpx;
	color: #ffffff;
}
</style>
