import { isEmpty } from "@/utils/validate/validate.js"

import moment from "moment";

import { ShowToast } from "@/utils/interactive/interactive.js"

/**
 * @description 扫描二维码
 * @param { function } fun 这是一个回调函数
 * */
export function scanCode(fun) {
	uni.scanCode({
		onlyFromCamera: true,
		success: (msg) => {
			fun(msg)
		}
	})
}

export function keyHide() {
	uni.hideKeyboard()
}

/**
 * 返回上一页
 * */
export function handBack() {
	uni.navigateBack({ delta: 1 })
}

/**
 * 拨打电话
 * */
export function dialFun(msg) {
	// #ifndef MP-WEIXIN
	if (msg) {
		plus.device.dial(msg, false);
	}
	// #endif
	// #ifdef MP-WEIXIN
	if (msg) {
		uni.makePhoneCall({
			phoneNumber: msg
		});
	}
	// #endif
}

/**
 * 清除本地信息
 * */
export function logOut() {
	uni.showLoading()
	uni.removeStorage({
		key: 'account',
		complete: suc => {
			ShowToast({ title: '退出成功！' }).then(res => {
				uni.hideLoading()
				uni.reLaunch({ url: '/pages/login/login' });
			})
		}
	});
}

/**
 * 文件上传
 * @param {Array} filePath 上传的文件列表
 * */
let uploadPicBaseURL = '';
export function UpLoadFile(filePath = []) {
	return new Promise((resove, reject) => {
		let PicList = [];
		const account = uni.getStorageSync("account");
		// 检查domainUrl是否已经包含http或https前缀
		if (account.domainUrl.startsWith('http://') || account.domainUrl.startsWith('https://')) {
			uploadPicBaseURL = account.domainUrl;
		} else if (account.isDev == 1) { //http
			uploadPicBaseURL = 'http://' + account.domainUrl;
		} else { // https
			uploadPicBaseURL = 'https://' + account.domainUrl;
		}
		// 获取url 微擎版或独立版
		const url = uploadPicBaseURL + '/api-util/rest/merchant/uploadSingleImage';
		filePath.forEach(item => {
			console.log(item)
			uni.uploadFile({
				url,
				filePath: item,
				name: 'file',
				fileType: 'image',
				header: {
					'Authorization': account.authorization,
					// 'uniacid': account ? account.uniacid : "43",
					'module': "yb_ps",
					'userId': account ? account.userId : "",
					'appType': 'weChat'
				},
				success: res => {
					console.log(res)
					if (res.statusCode === 200) {
						const data = JSON.parse(res.data);
						PicList.push(data.data)

						if (PicList.length === filePath.length) {
							resove(PicList)
						} else {
							reject(PicList);
						}
					} else {
						reject(res.errMsg);
					}
				},
				fail: err => {
					console.log(err)
				}
			});
		})
	})
}

/**
 * get full path to download files
 */
export function getFullFilePath(subPath) {
	const account = uni.getStorageSync("account");
	return account.picRoot + subPath;
}

/**
 * 文件选择或者拍照
 *
 * */

export function file_select(data = {}) {
	let obj = {
		count: data.count || 1,
		sourceType: data.sourceType || ['album', 'camera'],
		quality: data.quality || 30
	}
	return new Promise((resove, reject) => {
		const data = uni.chooseImage({
			count: obj.count,
			sizeType: ['compressed'],
			sourceType: obj.sourceType,
			complete: async res => {
				console.log(res)
				if (res.errMsg == 'chooseImage:ok') {
					resove(await Promise.all(res.tempFiles.map(async item => {
						return await new Promise((res, rej) => {
							// #ifdef APP-PLUS
							void plus.zip.compressImage({
									src: item.path,
									overwrite: false,
									quality: obj.quality,
								},
								msg => {
									res(msg.target)
								},
								err => {
									res(item.path)
								}
							)
							// #endif

							// #ifndef APP-PLUS
							res(item.path)
							// #endif
						})
					})))
				}
			}
		})
	})
}

/**
 * 距离计算、从当前自身位置到目的地位置
 * @param {Object} destination 目的地
 * */
function Rad(d) {
	//根据经纬度判断距离
	return d * Math.PI / 180.0;
}

export function DistanceCalc(destination, flag) {
	return new Promise((resove, reject) => {
		// #ifdef APP-PLUS
		uni.getLocation({
			type: 'gcj02',
			geocode: true,
			complete: msg => {
				if (msg.errMsg == 'getLocation:ok') {
					// 开始点
					const startPiot = new plus.maps.Point(msg.longitude, msg.latitude);
					// 结束点
					const endPiot = new plus.maps.Point(destination.endLng, destination.endLat);
					// 计算方法
					plus.maps.Map.calculateDistance(
						startPiot,
						endPiot,
						e => {
							const distance = (e.distance).toFixed(2);
							resove(distance)
						},
						err => {
							console.log(err)
							reject(err);
						});
				} else {
					ShowToast({
						title: '获取当前位置失败，请检查当前手机是否打开GPS！'
					}, false)
				}
			}
		});
		// #endif
		// #ifndef APP-PLUS
		uni.getLocation({
			type: 'gcj02',
			geocode: true,
			complete: msg => {
				console.log(msg)
				if (msg.errMsg == 'getLocation:ok') {
					// 计算方法
					let radLat1 = Rad(msg.latitude);
					let radLat2 = Rad(destination.endLat);
					let a = radLat1 - radLat2;
					let b = Rad(msg.longitude) - Rad(destination.endLng);
					let distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(
						radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
					distance = distance * 6378.137;
					distance = Math.round(distance * 10000) / 10000;
					distance = distance.toFixed(2)
					resove(distance)
				} else {
					ShowToast({
						title: '获取当前位置失败，请检查当前手机是否打开GPS！'
					}, false)
				}
			}
		});
		// #endif
	})
}


/**
 * @property {function} calcTime 订单时间计算
 * @param {String} start 创建时间
 * @param {start} timeSlot 时长
 * @param {start} type 时间计算类型 ==1是剩余时间计算，2是预计时间计算
 * */
export function calcTime(start, timeSlot, type = 1) {
	// timeSlot时长后的时间，也就是预计时间
	let strTime = moment(start * 1000).add(timeSlot, 'minutes');
	// 预计送达时间戳
	const stamp = new Date(strTime.format()).getTime();
	// 当前实际时间戳
	const currentTime = new Date().getTime();
	// 剩余时间
	const time = parseInt((stamp - currentTime) / 1000 / 60);
	if (type === 1) {
		if (time >= 0) {
			return time
		} else {
			return '--'
		}

	} else if (type === 2) {
		const day = moment.duration(time, 'minutes').days();
		const times = strTime.format("HH:mm");
		if (time >= 0) {
			if (day > 0) {
				return `${day}天后${times}`
			} else {
				return times
			}
		} else {
			return '--'
		}
	}

}
