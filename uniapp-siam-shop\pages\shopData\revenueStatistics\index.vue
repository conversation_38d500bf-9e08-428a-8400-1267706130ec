<template>
  <view class="revenue-statistics-container">
    <!-- 顶部标签切换 -->
    <view class="tab-container">
      <view class="tab-wrapper">
        <view class="tab-item" :class="{ active: activeTab === 'amount' }" @click="switchTab('amount')">
          <text>成交金额</text>
        </view>
        <view class="tab-item" :class="{ active: activeTab === 'order' }" @click="switchTab('order')">
          <text>成交订单</text>
        </view>
      </view>
      
      <!-- 按下单时间/按完成时间 -->
      <view class="time-type-wrapper">
        <view class="time-type-item" :class="{ active: timeType === 'order' }" @click="switchTimeType('order')">
          <text>按下单时间</text>
        </view>
        <view class="time-type-item" :class="{ active: timeType === 'complete' }" @click="switchTimeType('complete')">
          <text>按完成时间</text>
        </view>
      </view>
    </view>

    <!-- 日期筛选 -->
    <view class="date-filter">
      <view class="date-item" :class="{ active: activeDays === 3 }" @click="changeDays(3)">3天</view>
      <view class="date-item" :class="{ active: activeDays === 7 }" @click="changeDays(7)">7天</view>
      <view class="date-item" :class="{ active: activeDays === 15 }" @click="changeDays(15)">15天</view>
      <view class="date-item" :class="{ active: activeDays === 30 }" @click="changeDays(30)">30天</view>
      <view class="date-item" :class="{ active: activeDays === 'custom' }" @click="changeDays('custom')">自定义</view>
    </view>

    <!-- 图表区域 -->
    <view class="chart-container">
      <view class="chart-area">
        <!-- 这里可以集成图表组件，如 uCharts 或 echarts -->
        <view class="chart-placeholder">
          <!-- 图表占位，实际开发中替换为真实图表 -->
          <view class="chart-line">
            <view class="chart-point" v-for="(item, index) in chartData" :key="index" 
                  :style="{ left: `${index * 33}%`, bottom: `${item.value / 4}%` }">
              <text class="point-value">{{ item.value }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 总成交金额 -->
      <view class="total-amount">
        <text>总成交金额：</text>
        <text class="amount">¥{{ totalAmount }}</text>
      </view>

      <!-- 日期和金额列表 -->
      <view class="data-list">
        <view class="list-header">
          <text class="header-time">时间</text>
          <text class="header-amount">成交金额(元)</text>
        </view>
        <view class="list-item" v-for="(item, index) in revenueList" :key="index">
          <text class="item-time">{{ item.date }}</text>
          <text class="item-amount">{{ item.amount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
  computed: {
    ...mapState(['UserInfo'])
  },
  data() {
    return {
      activeTab: 'amount', // 当前激活的标签：amount-成交金额，order-成交订单
      timeType: 'order', // 当前时间类型：order-按下单时间，complete-按完成时间
      activeDays: 3, // 当前选中的天数
      totalAmount: '721.8', // 总成交金额
      chartData: [
        { date: '08-12', value: 329.56 },
        { date: '08-13', value: 172.28 },
        { date: '08-14', value: 219.96 }
      ],
      revenueList: [
        { date: '08-14', amount: '219.96' },
        { date: '08-13', amount: '172.28' },
        { date: '08-12', amount: '329.56' }
      ]
    };
  },
  onLoad() {
    // 页面加载时获取数据
    this.fetchRevenueData();
  },
  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
      this.fetchRevenueData();
    },

    // 切换时间类型
    switchTimeType(type) {
      this.timeType = type;
      this.fetchRevenueData();
    },

    // 切换天数
    changeDays(days) {
      this.activeDays = days;
      this.fetchRevenueData();
    },

    // 获取营收数据
    async fetchRevenueData() {
      uni.showLoading({
        title: '加载中'
      });

      try {
        // 这里应该调用接口获取数据
        // 模拟接口调用
        setTimeout(() => {
          // 模拟数据，实际开发中替换为真实接口
          if (this.activeTab === 'amount') {
            // 成交金额数据
            this.totalAmount = '721.8';
            this.chartData = [
              { date: '08-12', value: 329.56 },
              { date: '08-13', value: 172.28 },
              { date: '08-14', value: 219.96 }
            ];
            this.revenueList = [
              { date: '08-14', amount: '219.96' },
              { date: '08-13', amount: '172.28' },
              { date: '08-12', amount: '329.56' }
            ];
          } else {
            // 成交订单数据
            this.totalAmount = '24';
            this.chartData = [
              { date: '08-12', value: 10 },
              { date: '08-13', value: 6 },
              { date: '08-14', value: 8 }
            ];
            this.revenueList = [
              { date: '08-14', amount: '8' },
              { date: '08-13', amount: '6' },
              { date: '08-12', amount: '10' }
            ];
          }
          uni.hideLoading();
        }, 500);
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
        uni.hideLoading();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.revenue-statistics-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .tab-container {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx;

    .tab-wrapper {
      display: flex;
      // margin-bottom: 20rpx;
      
      .tab-item {
        height: 64rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        color: #333;
        border-radius: 40rpx;
        padding: 0 20rpx;
        margin-right: 16rpx;
        background-color: #f5f5f5;

        &.active {
          color: #fff;
          background-color: #007AFF;
          font-weight: 500;
        }
      }
    }

    .time-type-wrapper {
      display: flex;
      // background-color: #fff;
      background-color: #f5f5f5;
      .time-type-item {
        height: 60rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24rpx;
        color: #333;
        padding: 0 16rpx;
        width: 50%;
        border: 1px solid #eee;

        &.active {
          color: #333;
          background-color: #fff;
          border: 1px solid #ddd;
          box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .date-filter {
    display: flex;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin-top: 2rpx;

    .date-item {
      padding: 10rpx 20rpx;
      margin-right: 20rpx;
      font-size: 26rpx;
      color: #666;
      border-radius: 4rpx;

      &.active {
        background-color: #007AFF;
        color: #fff;
      }
    }
  }

  .chart-container {
    flex: 1;
    background-color: #fff;
    padding: 30rpx;

    .chart-area {
      height: 400rpx;
      position: relative;
      margin-bottom: 30rpx;
      border-bottom: 1px solid #eee;

      .chart-placeholder {
        height: 100%;
        position: relative;
        display: flex;
        align-items: flex-end;

        .chart-line {
          width: 100%;
          height: 2px;
          background-color: #eee;
          position: relative;

          .chart-point {
            position: absolute;
            width: 10rpx;
            height: 10rpx;
            background-color: #FF6320;
            border-radius: 50%;
            transform: translate(-50%, 50%);

            &::before {
              content: '';
              position: absolute;
              bottom: 0;
              left: 50%;
              width: 2px;
              height: 200rpx;
              background-color: #FF6320;
              transform: translateX(-50%);
            }

            .point-value {
              position: absolute;
              top: -40rpx;
              left: 50%;
              transform: translateX(-50%);
              font-size: 24rpx;
              color: #FF6320;
            }
          }
        }
      }
    }

    .total-amount {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 30rpx;

      .amount {
        font-size: 36rpx;
        font-weight: bold;
        color: #FF6320;
      }
    }

    .data-list {
      .list-header {
        display: flex;
        padding: 20rpx 0;
        border-bottom: 1px solid #eee;
        font-size: 28rpx;
        color: #999;

        .header-time {
          flex: 1;
        }

        .header-amount {
          flex: 1;
          text-align: right;
        }
      }

      .list-item {
        display: flex;
        padding: 30rpx 0;
        border-bottom: 1px solid #f5f5f5;
        font-size: 30rpx;

        .item-time {
          flex: 1;
          color: #333;
        }

        .item-amount {
          flex: 1;
          text-align: right;
          color: #FF6320;
          font-weight: 500;
        }
      }
    }
  }
}
</style>