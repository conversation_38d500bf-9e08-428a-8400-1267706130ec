<template>
	<view class="exan">
		<template v-if="exanList.length">
			<view class="exan-list" v-for="(item, index) of exanList" :key="index">
				<view class="list-title">{{ index + 1 + '.' + item.title }}</view>
				<view class="cont">
					<view class="cont-ls flex flex-align-center" @click="changeExan(item.id, 'A', index)">
						<view class="exan-icon" :class="{ 'exan-icon-bg': item.xzv == 'A' }">A</view>
						<text class="exan-title">{{ item.selectA }}</text>
					</view>
					<view class="cont-ls flex flex-align-center" @click="changeExan(item.id, 'B', index)">
						<view class="exan-icon" :class="{ 'exan-icon-bg': item.xzv == 'B' }">B</view>
						<text class="exan-title">{{ item.selectB }}</text>
					</view>
					<view class="cont-ls flex flex-align-center" @click="changeExan(item.id, 'C', index)">
						<view class="exan-icon" :class="{ 'exan-icon-bg': item.xzv == 'C' }">C</view>
						<text class="exan-title">{{ item.selectC }}</text>
					</view>
					<view class="cont-ls flex flex-align-center" @click="changeExan(item.id, 'D', index)">
						<view class="exan-icon" :class="{ 'exan-icon-bg': item.xzv == 'D' }">D</view>
						<text class="exan-title">{{ item.selectD }}</text>
					</view>
				</view>
			</view>
		</template>
		<view v-else style="background: #FFFFFF; height: 100%;"><u-empty text="暂无题目" mode="list"></u-empty></view>
		<view class="footer-btn-fiexd"><my-button @confirm="submitData">完成考试</my-button></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			exanList: [],
			answerList: [],
			answerListTwo: {}
		};
	},
	onLoad(e) {
		this.getTopiClist(e.id);
	},
	methods: {
		changeExan(id, type, index) {
			this.exanList[index].xzv = type;
		},
		getTopiClist(id) {
			this.$request({
				url: '/train/topic-list',
				method: 'GET',
				data: {
					typeId: id
				}
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					res.data.forEach(item => {
						item.xzv = '';
					});
					this.exanList = res.data;
				}
			});
		},
		submitData() {
			let data = {
				data: []
			};
			this.exanList.forEach(item => {
				data.data.push({
					id: item.id,
					answer: item.xzv
				});
			});
			this.$request({
				url: '/train/save-answer',
				data
			}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.$interactive
						.ShowToast({
							title: '提交成功！'
						})
						.then(() => {
							uni.navigateBack({
								delta: 2
							});
						});
				} else {
					this.$interactive.ShowToast(
						{
							title: res.message
						},
						false
					);
				}
			});
		},
		calc(item) {
			console.log(this.answerList.indexOf(item));
		}
	}
};
</script>

<style lang="scss" scoped>
.exan {
	height: 100vh;
	padding: 20rpx 20rpx 120rpx;
	overflow-y: scroll;
	background: $uni-bg-color-grey;

	.exan-list {
		background: #ffffff;
		padding: 20rpx 32rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		.list-title {
			font-size: 32rpx;
		}

		.cont {
			.cont-ls {
				padding: 20rpx 0rpx;

				.exan-icon {
					border: 1rpx solid #333;
					width: 44rpx;
					height: 44rpx;
					text-align: center;
					line-height: 40rpx;
					border-radius: 50%;
					transition: all 0.1s ease;
					margin-right: 20rpx;
				}

				.exan-icon-bg {
					background: #333;
					color: #fff;
				}

				.exan-title {
					font-size: 32rpx;
					margin-bottom: 4rpx;
				}
			}
		}
	}
}
</style>
