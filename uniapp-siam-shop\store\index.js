import Vue from 'vue'
import Vuex from 'vuex'
import request from "@/common/method/request.js"
import { ShowToast } from "@/utils/interactive/interactive.js"

Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		address: { city: '成都' },
		UserInfo: {},
		configInfo:{},
		account: uni.getStorageSync('account') || {},
	},
	mutations: {
		getAddressInfo(state, address) {
			state.address = address;
			console.log('stateaddress',state.address)
		},
		setUserInfo(state, UserInfo) {
			state.UserInfo = UserInfo;
		},
		getConfigInfo(state, configInfo){
			state.configInfo = configInfo;
		},
		setAccount(state, data) {
			let account = Object.assign(state.account, data)
			state.account = account;
			uni.setStorageSync('account',account)
		},
		delAccount(state, data){
			uni.setStorageSync('account', {
				domainUrl: uni.getStorageSync('account').domainUrl,
				isDev: uni.getStorageSync('account').isDev,
				uniacid: uni.getStorageSync('account').uniacid,
			})
			state.account = uni.getStorageSync('account')
		}
	},
	actions:{
		//获取openid临时登录信息
		async getLoginInfo({ commit, state }, params) {
			// if (state.user.id) {
			// 	return
			// } else {
				return await new Promise(async (resolve, reject) => {
					// #ifndef H5
					// util.showLoading()
					uni.login({
						// #ifdef MP-ALIPAY
						scopes: 'auth_user', //支付宝小程序需设置授权类型
						// #endif
						success: async (lres) => {
							console.log(lres)
							// console.log('uni.login', res)
							let res = await request({
								'url': '/login/mini-login',
								'method': 'GET',
								'data': {
									code: lres.code,
									inviteId: params.inviteId || '',
									loginType: params.loginType
								}
							}).catch(e=>{return e})
							res = JSON.parse(res)
							console.log(res)
							if (res.code == 1) {
								// console.timeEnd('login')
								// commit('setUser', res.data)
								resolve(res)
								// if (res.data && res.data.session_key) {
								// 	getApp().globalData.session_key = res.data
								// 		.session_key
								// }
							}else if(res.code == 2){
								resolve(res)
							} else {
								reject()
								ShowToast({ title: '请检查小程序秘钥等相关配置' }, false );
							}
						},
						fail: (err) => {
							console.log('接口调用失败，将无法正常使用开放接口等服务', err)
							reject(err)
						}
					})
					// #endif
				})
			// }
		},
	}
})


export default store
