<template>
	<view class="line" :style="'height:' + style.height + ';' + 'background:' + style.background + ';' + 'margin:' + style.margin + ';'"></view>
</template>

<script>
	export default {
		props:{
			height:{
				type: [Number,String],
				default: 1
			},
			background:{
				type: [Number,String],
				default: '#f8f8f8'
			},
			margin: {
				type: Array,
				default: ()=>{
					return [0,0]
				}
			}
		},
		computed:{
			style(){
				const mar = []
				this.margin.forEach(item=>{
					mar.push(item + 'rpx')
				})
				return {
					height: this.height + 'rpx',
					background: this.background,
					margin: mar.toString().replace(/\,/g," ")
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.line{
}
</style>
