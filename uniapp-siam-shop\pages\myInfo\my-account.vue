<template>
	<view class="my-account">
		<view class="my-account-info">
<!-- 			<text class="my-account-mian-title">我的账户</text> -->
			<view class="my-account-cont">
				<view class="my-account-cont-title">账户余额</view>
				<view class="wal-box flex flex-align-center justify-space-between">
					<view class="wal-title">
						<text class="wal-title-num">{{ UserInfo.balance || '0.00' }}</text>
						<text class="wal-title-cpy">元</text>
					</view>
					<view class="wal-btn" @click="handGoToGell" v-show="configInfo.open==1">提现</view>
				</view>
			</view>
			<view class="my-account-menu flex flex-align-center">
				<view class="my-account-menu-ls">
					<navigator class="flex flex-direction-column flex-align-center" url="/pages/myInfo/bill-details" hover-class="none">
						<image class="icon-pic" src="~@/static/icon/mx.png" mode="widthFix"></image>
						<text class="title-name">账户明细</text>
					</navigator>
				</view>
				<!-- <view class="my-account-menu-ls flex flex-direction-column flex-align-center">
					<image class="icon-pic" src="~@/static/icon/tj.png" mode="widthFix"></image>
					<text class="title-name">收支统计</text>
				</view>
				<view class="my-account-menu-ls">
					<navigator class="flex flex-direction-column flex-align-center" url="/pages/accountCore/bondMoney" hover-class="none">
						<image class="icon-pic" src="~@/static/icon/bzj.png" mode="widthFix"></image>
						<text class="title-name">保证金</text>
					</navigator>
				</view> -->

				<view class="my-account-menu-ls" v-show="configInfo.open==1">
					<navigator class="flex flex-direction-column flex-align-center" url="/pages/myInfo/withdrawalSet" hover-class="none">
						<image class="icon-pic" src="~@/static/icon/txset.png" mode="widthFix"></image>
						<text class="title-name">账户设置</text>
					</navigator>
				</view>
			</view>
			<my-line></my-line>
			<view class="my-account-footer">
				<view class=" flex flex-align-center justify-space-between">
					<view class="footer-title">今日账单</view>
					<!-- <view class="footer-info">
						<uni-icons type="help"></uni-icons>
						<text>数据说明</text>
					</view> -->
				</view>
				<view class="footer-box">
					<view class="flex flex-align-center">
						<view class="footer-box-ls">
							<text class="monry-num">+{{ UserInfo.todaySumMerchantIncome || '0.00' }}</text>
							<view class="footer-box-ls-icon"><text>今日收入</text></view>
						</view>
						<view class="footer-box-ls">
							<text class="monry-num">+{{ UserInfo.todayCountPaid || '0.00' }}</text>
							<view class="footer-box-ls-icon"><text>今日单量</text></view>
						</view>
						<view class="footer-box-ls">
							<text class="monry-num">+{{ UserInfo.todayCountCancel || '0.00' }}</text>
							<view class="footer-box-ls-icon"><text>今日取消</text></view>
						</view>
					</view>
				</view>
			</view>
			<view class="bootom-tirp">暂无更多数据</view>
		</view>
		<withdraw-deposit ref="handClickOpen"></withdraw-deposit>
	</view>
</template>

<script>
import WithdrawDeposit from './component/withdrawDeposit.vue';
import { mapState } from 'vuex';
export default {
	components: {
		WithdrawDeposit
	},
	data() {
		return {
			// the info is contained in user info
			myInfo: {},
			// TBC
			configInfo: {
				open: 1
			},
		};
	},
	onLoad() {
		// this.getMyInfo();
		// this.getConfigInfo();
		uni.$emit('RefreshUserInfo');
	},
	computed: {
		...mapState(['UserInfo'])
	},
	methods: {
		getMyInfo() {
			this.$request({
				url: '/rider-bill/my-account',
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.myInfo = res.data;
				}
			});
		},
		getConfigInfo() {
			this.$request({
				url: '/config/config',
				data: {
					ident: 'withdrawal'
				},
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.configInfo = res.data;

					console.log(this.configInfo.open==1);
				}
			});
		},
		handGoToGell() {
			this.$nextTick(() => {
				this.$refs.handClickOpen.init(this.UserInfo.balance);
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.my-account {
	.my-account-info {
		padding: 0 32rpx;

		.my-account-mian-title {
			font-size: $font-my-size-48;
			font-weight: bold;
		}

		.my-account-cont {
			padding: 60rpx 32rpx;
			// background: linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19));
			background: #ff6320;
			border-radius: 10rpx;
			margin-top: 20rpx;

			.my-account-cont-title {
				color: $font-my-color-3;
				font-size: $font-my-size-36;
				color: #ffffff;
			}

			.wal-box {
				.wal-title {
					.wal-title-num {
						font-size: $font-my-size-52;
						font-weight: bold;
						color: #ffffff;
						margin-right: 10rpx;
					}
					.wal-title-cpy {
						color: #ffffff;
					}
				}
				.wal-btn {
					background: #3a3a3a;
					border-radius: 5rpx;
					padding: 10rpx 30rpx;
					color: #ffffff;
				}
			}
		}

		.my-account-menu {
			padding: 50rpx 0;

			.my-account-menu-ls {
				flex: 1;

				.icon-pic {
					width: 90rpx;
					max-height: 86rpx;
					min-height: 86rpx;
				}

				.title-name {
					color: $font-my-color-3;
					font-size: $font-my-size-28;
					padding: 10rpx 0;
				}
			}
		}

		.my-account-footer {
			padding: 30rpx 0;

			.footer-title {
				font-size: $font-my-size-36;
				font-weight: bold;
			}

			.footer-box {
				padding: 40rpx 0rpx;
				border: 1rpx solid $uni-bg-color-grey;
				border-radius: 10rpx;
				margin-top: 40rpx;

				.footer-box-ls {
					flex: 1;
					text-align: center;

					.monry-num {
						font-weight: bold;
						font-size: $font-my-size-36;
					}

					.footer-box-ls-icon {
						font-size: $font-my-size-26;
					}
				}

				.footer-tirp {
					color: $font-my-color-9;
					font-size: $font-my-size-24;
					padding: 0 32rpx;
				}
			}
		}

		.bootom-tirp {
			color: $font-my-color-9;
			font-size: $font-my-size-24;
			text-align: center;
		}
	}
}
</style>
