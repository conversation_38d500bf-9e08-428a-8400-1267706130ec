<template>
	<view class="billDetailsR">
		<view class="billmenu flex flex-align-start justify-space-between">
			<view class="flex flex-align-start">
				<text class="menu-name" :class="{ 'menu-name-activ': activeIndex == 1 }" @click="changerIndex(1)">日统计</text>
				<view class="line-c"></view>
				<text class="menu-name" :class="{ 'menu-name-activ': activeIndex == 2 }" @click="changerIndex(2)">月统计</text>
			</view>
			<view class="t-n-b">
				<text class="t-r-n">收入</text>
				<text class="t-r-n t-r-n-c">支出</text>
			</view>
		</view>
		<view class="qiun-charts"><canvas canvas-id="canvasColumn" id="canvasColumn" class="charts" @touchstart="touchColumn"></canvas></view>
		<view class="arrow"></view>
		<my-line></my-line>
		<view class="footer-cont">
			<view class="footer-title">今天收入(0)</view>
			<view class="cont-money">+0.00</view>
			<view class="footer-menu flex flex-align-center flex-wrap">
				<view class="menu-item">
					<view class="menu-item-title">配送收入</view>
					<text class="menu-item-money">0</text>
				</view>
				<view class="menu-item">
					<view class="menu-item-title">活动收入</view>
					<text class="menu-item-money">0</text>
				</view>
				<view class="menu-item">
					<view class="menu-item-title">乐跑计划</view>
					<text class="menu-item-money">0</text>
				</view>
				<view class="menu-item">
					<view class="menu-item-title">申诉补款</view>
					<text class="menu-item-money">0</text>
				</view>
				<view class="menu-item">
					<view class="menu-item-title">其他收入</view>
					<text class="menu-item-money">0</text>
				</view>
			</view>
			<view class="footer-title">今天支出(0)</view>
			<view class="cont-money">+0.00</view>
			<view class="footer-menu flex flex-align-center flex-wrap">
				<view class="menu-item">
					<view class="menu-item-title">违规扣款</view>
					<text class="menu-item-money">0</text>
				</view>
				<view class="menu-item">
					<view class="menu-item-title">消费支出</view>
					<text class="menu-item-money">0</text>
				</view>
				<view class="menu-item">
					<view class="menu-item-title">其他支出</view>
					<text class="menu-item-money">0</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import data from '../data.js';
import uCharts from '@/components/u-charts/u-charts.js';
var _self;
var canvaColumn = null;

export default {
	data() {
		return {
			cWidth: '',
			cHeight: '',
			pixelRatio: 1,
			serverData: '',
			activeIndex: 1
		};
	},
	created() {
		_self = this;
		this.cWidth = uni.upx2px(750);
		this.cHeight = uni.upx2px(500);
		this.getServerData();
	},
	methods: {
		changerIndex(index) {
			this.activeIndex = index;
		},
		getServerData() {
			//下面这个根据需要保存后台数据，我是为了模拟更新柱状图，所以存下来了
			let Column = { categories: [], series: [] };
			//这里我后台返回的是数组，所以用等于，如果您后台返回的是单条数据，需要push进去
			Column.categories = data.Column.categories;
			Column.series = data.Column.series;
			this.showColumn(Column);
		},
		showColumn(chartData) {
			canvaColumn = new uCharts({
				$this: this,
				canvasId: 'canvasColumn',
				type: 'column',
				legend: {
					show: false
				},
				fontSize: 10,
				pixelRatio: 1,
				animation: true,
				categories: chartData.categories,
				series: [
					{
						name: '收入',
						data: chartData.series[0].data,
						color: '#f04864'
					},
					{
						name: '支出',
						data: chartData.series[1].data,
						color: '#facc14'
					}
				],
				xAxis: {
					disableGrid: true
				},
				yAxis: {
					disabled: false
				},
				dataLabel: true,
				width: _self.cWidth * _self.pixelRatio,
				height: _self.cHeight * _self.pixelRatio,
				extra: {
					column: {
						type: 'group',
						width: '750'
					}
				}
			});
		},
		touchColumn(e) {
			canvaColumn.showToolTip(e, {
				format: function(item, category) {
					if (typeof item.data === 'object') {
						return category + ' ' + item.name + ':' + item.data.value;
					} else {
						return category + ' ' + item.name + ':' + item.data;
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.billDetailsR {
	height: 100%;

	.billmenu {
		background: #ffffff;
		padding: 20rpx 0;

		.menu-name {
			width: 120rpx;
			text-align: center;
			font-size: 30rpx;
		}

		.menu-name-activ {
			&::after {
				display: block;
				content: '';
				height: 6rpx;
				width: 50rpx;
				background: linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19));
				margin: 6rpx auto 0;
			}
		}

		.line-c {
			width: 2rpx;
			height: 20rpx;
			background-color: #ddd;
			margin-top: 14rpx;
		}

		.t-n-b {
			.t-r-n {
				width: 150rpx;
				padding-right: 20rpx;
				font-size: 30rpx;

				&::before {
					content: '';
					display: inline-block;
					width: 14rpx;
					height: 14rpx;
					background: #f04864;
					vertical-align: middle;
					margin-right: 10rpx;
					margin-bottom: 4rpx;
				}
			}

			.t-r-n-c {
				&::before {
					background: #facc14;
				}
			}
		}
	}

	.qiun-charts {
		width: 750rpx;
		height: 500rpx;
		background-color: #ffffff;

		.charts {
			width: 750rpx;
			height: 500rpx;
			background-color: #ffffff;
		}
	}

	.arrow {
		width: 40rpx;
		height: 40rpx;
		border-style: solid;
		border-color: #f8f8f8 #f8f8f8 transparent transparent;
		transform: translateY(50%) rotate(-45deg);
		background-color: #ffffff;
		margin: 0 auto;
	}

	.footer-cont {
		padding: 0 32rpx;
		font-weight: bold;

		.footer-title {
			padding: 30rpx 0 10rpx;
			font-size: 32rpx;

			color: #333;
		}
		.cont-money {
			font-size: 40rpx;
		}

		.footer-menu {
			.menu-item {
				padding: 10rpx 32rpx;
				width: 33.33%;
				color: #666;
				font-weight: 400;

				.menu-item-title {
					padding: 10rpx 0;
				}
			}
		}
	}
}
</style>
