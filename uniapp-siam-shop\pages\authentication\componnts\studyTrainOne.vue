<template>
	<view class="studyTrain-box">
		<mescroll-uni :style="'height:' + windowheight + 'rpx;'" @init="mescrollInit" @down="downCallback" @up="upCallback"
			:down="{ auto: true }" :up="{ auto: false,empty:{tip: '暂无培训'} }">
			<view class="studyTrain-cont">
				<view class="studyTrain-cont-title">培训类型说明</view>
				<view class="studyTrain-cont-info">1.入职培训（新手必学）：针对新加入的骑手进行配送基础知识的培训，通过此项培训方可获得基础接单资格。</view>
				<view class="studyTrain-cont-info">2.专项培训:面向不同类型骑手的专项培训，通过此项培训可以获得特定业务配送的接单资格。</view>
				<view class="studyTrain-cont-title">培训注意事项</view>
				<view class="studyTrain-cont-info">1.参训人员需携带身份证以便信息核实。</view>
				<view class="studyTrain-cont-info">2.参训人员需在培训开始时间前到达，并遵守培训纪律要求(迟到将拒绝入场)。</view>
				<view class="studyTrain-cont-info">3.入职培训请提前注册骑手。</view>
				<view class="studyTrain-cont-title">培训流程</view>
				<view class="studyTrain-cont-info">选择培训类型-线下场次报名培训签到-培训讲解培训考试-获得接单资格。</view>
				<view class="studyTrain-footer" v-for="(item, index) of trainList" @click="handCheck(index, item.state)"
					:key="item.id">
					<view class="studyTrain-footer-title flex flex-align-center justify-space-between">
						<view class="tetle-name">
							<text class="tetle-name-text">{{ item.name }}</text>
							<u-tag text="入职培训" mode="dark" size="mini" bg-color="#D8EFFD" color="#138ADB" />
						</view>
						<view class="tetle-type">{{ item.people }}人</view>
					</view>
					<view class="studyTrain-cont-info">培训地址：{{ item.address }}</view>
					<view class="studyTrain-cont-info">联系人：{{ item.linkMan }}</view>
					<view class="studyTrain-cont-info">联系人电话：{{ item.tel }}</view>
					<u-radio-group class="studyTrain-footer-btn" v-model="value">
						<u-radio :disabled="item.state != 0" :name="index"></u-radio>
					</u-radio-group>
				</view>
			</view>
		</mescroll-uni>
<!-- 		<view class="kong"></view> -->
		<view class="footer-btn-fiexd">
			<my-button width="686" border-radius="50" height="80" color="#fff" @confirm="submitData">立即报名</my-button>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	export default {
		data() {
			return {
				value: null,
				trainList: [],
				windowheight: 0
			};
		},
		computed: {
			...mapState(['address'])
		},
		created() {
			uni.getSystemInfo({
				success: (res) => {
					this.windowheight = 2 * res.windowHeight - 240
				}
			})
		},
		methods: {
			handClickOrderInfo(msg) {
				uni.navigateTo({
					url: '/pages/detailsPage/orderDetails?id=' + msg
				});
			},
			// 下拉回调函数
			downCallback(e) {
				this.mescroll.resetUpScroll();
			},
			// 上拉加载更多
			upCallback(e) {
				this.getTrainAddress(e.num);
			},
			getTrainAddress(msg = 1) {
				this.$request({
					url: '/train/address-list',
					method: "GET",
					data: {
						page: msg
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
						res = JSON.parse(res)
					// #endif
					if (res.code == 1) {
						if (msg == 1) {
							this.trainList = res.data;
						} else {
							this.trainList = this.trainList.concat(res.data);
						}
						this.mescroll.endSuccess(res.data.length, res.data.length >= 10 ? true : true);
					} else {
						this.mescroll.endSuccess(0, false);
					}
					this.value = null;
				});
			},
			handCheck(msg, state) {
				if (state == 0) {
					this.value = msg;
				}
			},
			// 数据提交
			submitData() {
				this.$request({
					url: '/train/enroll',
					data: {
						addressId: this.trainList[this.value].id
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
						res = JSON.parse(res)
					// #endif
					if (res.code == 1) {
						this.$interactive
							.ShowToast({
								title: '报名成功！'
							})
							.then(() => {
								this.getTrainAddress();
								uni.$emit('cancelEnrol');
							});
						this.$emit('resfTrainAddress');
					} else {
						this.$interactive.ShowToast({
								title: res.message
							},
							false
						);
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	/deep/.mescroll-uni {
		top: 0rpx !important;
		min-height: 0rpx !important;
	}

	.studyTrain-box {
		display: flex;
		flex-direction: column;
		height: 100%;
		min-height: 0rpx;
		overflow: hidden;

		.studyTrain-cont {
			padding: 0 32rpx;

			.studyTrain-cont-title {
				font-size: $font-my-size-28;
				font-weight: bold;
				color: #333333;
				padding: 40rpx 0 10rpx;
			}

			.studyTrain-cont-info {
				font-size: $font-my-size-26;
				font-weight: 400;
				color: #666666;
				line-height: 48rpx;
			}

			.tabs-box {
				padding: 100rpx 0 0rpx;

				.tabsCont {
					padding: 20rpx 36rpx;
					margin-bottom: 20rpx;
					border: 1rpx solid #bbbbbb;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					margin-right: 20rpx;
				}

				.tabsCont-styl {
					border-color: #0092fa;
					color: #0092fa;
				}
			}

			.studyTrain-footer {
				margin-top: 40rpx;
			}
		}

		.kong{
			width: 100%;
			min-height: 240rpx;
		}

		.footer-btn-fiexd {
			position: fixed;
			bottom: 0;
			width: 100%;
			min-height: 150rpx;
			padding: 30rpx 0;
		}
	}
</style>
