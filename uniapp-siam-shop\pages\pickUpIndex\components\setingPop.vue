<template>
	<u-popup v-model="show" mode="bottom" height="60%" @close="UpLine">
		<view class="order-main">
			<view class="order-title">门店管理</view>
			<view class="orderSet orderSet-l">
				<view class="orderSet-ls">
					<view class="flex flex-align-start">
						<text class="title-name">营业状态</text>
						<view :class="{'checked': !isOperating, 'check-box': true }" @click="isOperating=false">
							<text class="value-name">打烊</text>
						</view>
						<view :class="{'checked': isOperating, 'check-box': true }" @click="isOperating=true">
							<text class="value-name">营业</text>
						</view>
					</view>
				</view>
				<my-line></my-line>
				<view class="orderSet-ls">
					<view class="flex flex-align-start">
						<text class="title-name">配送费</text>
						<uni-easyinput class="name-cont" type="text" style="text-align: right;" v-model="deliveryFee" :inputBorder="false" placeholder="金额(元)"></uni-easyinput>
					</view>
				</view>
				<my-line></my-line>
				<view class="orderSet-ls">
					<view class="flex flex-align-start" @click="showStartTimePicker = true">
						<text class="title-name">开始时间</text>
						<text class="value-name" v-if="startTime">{{ startTime }}</text>
						<text class="value-name printer-placeholder" v-else>请选择开始时间</text>
						<uni-icons class="iconStyl" color="#999" type="forward"></uni-icons>
					</view>
				</view>
				<my-line></my-line>
				<view class="orderSet-ls">
					<view class="flex flex-align-start" @click="showEndTimePicker = true">
						<text class="title-name">结束时间</text>
						<text class="value-name" v-if="endTime">{{ endTime }}</text>
						<text class="value-name printer-placeholder" v-else>请选择结束时间</text>
						<uni-icons class="iconStyl" color="#999" type="forward"></uni-icons>
					</view>
				</view>
				<my-line></my-line>
				<view class="orderSet-ls">
					<view class="flex flex-align-start">
						<text class="title-name">配送范围</text>
						<view class="delivery-range">
							<view :class="{'checked': deliveryRange.includes('1'), 'check-box': true }" @click="toggleDeliveryRange('1')">
								<text class="value-name">北苑</text>
							</view>
							<view :class="{'checked': deliveryRange.includes('2'), 'check-box': true }" @click="toggleDeliveryRange('2')">
								<text class="value-name">滨江</text>
							</view>
							<view :class="{'checked': deliveryRange.includes('3'), 'check-box': true }" @click="toggleDeliveryRange('3')">
								<text class="value-name">新校区</text>
							</view>
						</view>
					</view>
				</view>
				<my-line></my-line>
				<view class="orderSet-ls">
					<view class="flex flex-align-start">
						<text class="title-name">配送方式</text>
						<view :class="{'checked': deliveryWay == 1, 'check-box': true }" @click="deliveryWay = 1">
							<text class="value-name">自配送</text>
						</view>
						<view :class="{'checked': deliveryWay == 2, 'check-box': true }" @click="deliveryWay = 2">
							<text class="value-name">平台配送</text>
						</view>
						<view :class="{'checked': deliveryWay == 3, 'check-box': true }" @click="deliveryWay = 3">
							<text class="value-name">平台+自配送</text>
						</view>
					</view>
				</view>
				<my-line></my-line>
				<view class="orderSet-ls">
					<view class="flex flex-align-start" @click="openPrinterSelector">
						<text class="title-name">绑定打印机</text>
						<text class="value-name printer-value" v-if="selectedPrinter && selectedPrinter.name">{{ selectedPrinter.name }}</text>
						<text class="value-name printer-placeholder" v-else>请选择打印机</text>
						<uni-icons class="iconStyl" color="#999" type="forward"></uni-icons>
					</view>
				</view>
				<!-- <view class="orderSet-ls">
					<view class="flex flex-align-start">
						<text class="title-name">听单设置</text>
						<text class="value-name">运费大于15元</text>
						<uni-icons class="iconStyl" color="#999" type="forward"></uni-icons>
					</view>
					<view class="trip-name">只有配送距离大于5公里的订单才会以"听单"形式提醒</view>
				</view>
				<my-line></my-line> -->
			</view>
		</view>
		<!-- 开始时间选择器 -->
		<u-picker 
			mode="time" 
			v-model="showStartTimePicker" 
			:params="timeParams" 
			@confirm="onStartTimeConfirm"
		></u-picker>
		
		<!-- 结束时间选择器 -->
		<u-picker 
			mode="time" 
			v-model="showEndTimePicker" 
			:params="timeParams" 
			@confirm="onEndTimeConfirm"
		></u-picker>
		
		<!-- 打印机选择弹窗 -->
		<printer-selector 
			:value="showPrinterSelector" 
			:current-printer="selectedPrinter"
			@confirm="onPrinterSelected"
			@cancel="this.showPrinterSelector = false"
		></printer-selector>
	</u-popup>
</template>

<script>
import { mapState } from 'vuex';
import CustomTabsSwiper from '../../../components/custom-tabs-page-linkge/tab-page-linkage.vue';
import PrinterSelector from './printerSelector.vue'; // 引入打印机选择组件

export default {
	props: {
	},
	components: {
		CustomTabsSwiper,
		PrinterSelector // 注册打印机选择组件
	},
	computed: {
		...mapState(['UserInfo', 'configInfo']),
	},
	data() {
		return {
			show: false,
			deliveryWay: 1,	// 配送方式 1=商家自配送 2=平台配送 3=商家+平台配送
			deliveryFee: 0,	// 配送费
			isOperating: false, //是否打烊
			startTime: '', // 开始时间
			endTime: '', // 结束时间
			deliveryRange: [], // 配送范围
			// 打印机相关数据
			showPrinterSelector: false,
			selectedPrinter: {},
			// 时间选择器相关
			showStartTimePicker: false,
			showEndTimePicker: false,
			timeParams: {
				hour: true,
				minute: true,
				second: false
			}
		};
	},
	methods: {
		init() {
			this.show = true;
			this.deliveryFee = this.UserInfo.deliveryFee;
			this.deliveryWay = this.UserInfo.deliveryWay;
			this.isOperating = this.UserInfo.isOperating;
			this.startTime = this.UserInfo.startTime || '';
			this.endTime = this.UserInfo.endTime || '';
			// 初始化配送范围
			if (this.UserInfo.deliveryRange) {
				this.deliveryRange = this.UserInfo.deliveryRange.split(',').filter(item => item);
			} else {
				this.deliveryRange = [];
			}
			
			// 初始化打印机信息
			this.selectedPrinter = {
				id: this.UserInfo.checkoutPrinterId || null,
				name: this.UserInfo.checkoutPrinterName || ''
			};
		},
		
		// 开始时间确认
		onStartTimeConfirm(e) {
			this.startTime = e.hour + ':' + e.minute;
		},
		
		// 结束时间确认
		onEndTimeConfirm(e) {
			this.endTime = e.hour + ':' + e.minute;
		},
		
		// 切换配送范围选项
		toggleDeliveryRange(value) {
			const index = this.deliveryRange.indexOf(value);
			if (index > -1) {
				this.deliveryRange.splice(index, 1);
			} else {
				this.deliveryRange.push(value);
			}
		},
		
		// 打开打印机选择器
		openPrinterSelector() {
			this.showPrinterSelector = true;
		},
		
		// 打印机选择确认回调
		onPrinterSelected(printerInfo) {
			// 查找选中的打印机名称
			this.selectedPrinter = printerInfo || {};
			this.showPrinterSelector = false;
		},
		
		UpLine() { // 店铺营业/打烊
			uni.$emit('RefreshUserInfo');
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				let id = this.UserInfo.shopId;
				this.$request({ 
					url: '/api-merchant/rest/merchant/shop/update', 
					data: { 
						"id": id, 
						"isOperating": this.isOperating, 
						deliveryFee: this.deliveryFee, 
						deliveryWay: this.deliveryWay,
						startTime: this.startTime,
						endTime: this.endTime,
						deliveryRange: this.deliveryRange.join(','),
						checkoutPrinterId : this.selectedPrinter.id // 添加打印机ID
					},
				}).then(res => {
					// #ifdef MP-WEIXIN
					res = JSON.parse(res)
					// #endif
					if (res.code === 200) {
						uni.$emit('RefreshUserInfo');
					} else {
						this.$interactive.ShowToast({ title: res.message }, false );
					}
				});
			} else {
				this.$interactive.ShowToast({ title: '请先进行实名认证！' }, false );
			}
		},
	}
};
</script>

<style lang="scss" scoped>
.order-main {
	background: $uni-bg-color-grey;
	
	.order-title {
		padding: 30rpx 0;
		background: #FFFFFF;
		text-align: center;
		font-size: $font-my-size-34;
	}

	.orderSet {
		background: #FFFFFF;
		margin-top: 20rpx;
		
		.orderSet-ls {
			padding: 24rpx 32rpx;

			.title-name {
				font-size: $font-my-size-32;
				font-weight: bold;
				color: $font-my-color-3;
				flex: 1;
			}
			.check-box {
				border: 1rpx solid $font-my-color-9;
				border-radius: 10rpx;
				padding: 5rpx 20rpx;
				margin-left: 20rpx;
			}
			.checked {
				border: 1rpx solid #fa7c25;
				.value-name {
					color: #fa7c25;
				}
			}

			.value-name {
				color: $font-my-color-3;
			}
			
			.printer-value {
				color: $font-my-color-3;
			}
			
			.printer-placeholder {
				color: $font-my-color-9;
			}

			.trip-name {
				color: $font-my-color-9;
				font-size: $font-my-size-24;
				margin-top: 20rpx;
			}

			.iconStyl {
				margin-top: 4rpx;
			}
			
			.delivery-range {
				display: flex;
			}
		}
	}
}
</style>