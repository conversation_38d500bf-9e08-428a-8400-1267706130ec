<template>
	<view class="chat-container">
    	<uni-status-bar></uni-status-bar>
		<!-- 顶部订单信息 -->
		<view class="order-info">
			<view class="order-title">订单号: {{ orderInfo.orderNo }}</view>
			<view class="order-status">{{ orderInfo.status }}</view>
		</view>
		
		<!-- 聊天记录区域 -->
		<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false }" top="-80">
			<view class="message-list">
				<view 
					v-for="(item, index) in messageList" 
					:key="index"
					:class="['message-item', item.sender === 'me' ? 'message-mine' : 'message-other']"
				>
					<!-- 对方消息 -->
					<template v-if="item.sender === 'other'">
						<u-avatar :src="item.avatar" size="mini" class="avatar"></u-avatar>
						<view class="message-content-wrapper">
							<text class="message-time">{{ item.time }}</text>
							<view class="message-content">
								<text class="message-text">{{ item.content }}</text>
							</view>
						</view>
					</template>
					
					<!-- 我的消息 -->
					<template v-else>
						<view class="message-content-wrapper">
							<text class="message-time me">{{ item.time }}</text>
							<view class="message-content">
								<text class="message-text">{{ item.content }}</text>
							</view>
						</view>
						<u-avatar :src="item.avatar" size="mini" class="avatar"></u-avatar>
					</template>
				</view>
			</view>
		</mescroll-uni>
		<!-- 输入区域 -->
		<view class="chat-input-area">
			<input 
				class="chat-input" 
				type="text" 
				v-model="inputMessage" 
				placeholder="请输入消息..."
				@confirm="sendMessage"
			/>
			<button class="send-button" @click="sendMessage">发送</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userId: '',
				username: '',
				scrollTop: 0,
				inputMessage: '',
				orderInfo: {
					orderNo: 'NO20230801001',
					status: '配送中'
				},
				messageList: [
					{
						sender: 'other',
						content: '您好，请问我的订单大概多久能送到？',
						time: '14:30',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'me',
						content: '您好，您的订单正在配送中，预计30分钟内送达。',
						time: '14:32',
						avatar: '/static/images/avatar2.jpg'
					},
					{
						sender: 'other',
						content: '好的，谢谢！',
						time: '14:33',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'other',
						content: '您好，请问我的订单大概多久能送到？',
						time: '14:30',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'me',
						content: '您好，您的订单正在配送中，预计30分钟内送达。',
						time: '14:32',
						avatar: '/static/images/avatar2.jpg'
					},
					{
						sender: 'other',
						content: '好的，谢谢！',
						time: '14:33',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'other',
						content: '您好，请问我的订单大概多久能送到？',
						time: '14:30',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'me',
						content: '您好，您的订单正在配送中，预计30分钟内送达。',
						time: '14:32',
						avatar: '/static/images/avatar2.jpg'
					},
					{
						sender: 'other',
						content: '好的，谢谢！',
						time: '14:33',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'other',
						content: '您好，请问我的订单大概多久能送到？',
						time: '14:30',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'me',
						content: '您好，您的订单正在配送中，预计30分钟内送达。',
						time: '14:32',
						avatar: '/static/images/avatar2.jpg'
					},
					{
						sender: 'other',
						content: '好的，谢谢！',
						time: '14:33',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'other',
						content: '您好，请问我的订单大概多久能送到？',
						time: '14:30',
						avatar: '/static/images/avatar1.jpg'
					},
					{
						sender: 'me',
						content: '您好，您的订单正在配送中，预计30分钟内送达。',
						time: '14:32',
						avatar: '/static/images/avatar2.jpg'
					},
					{
						sender: 'other',
						content: '好的，谢谢！',
						time: '14:33',
						avatar: '/static/images/avatar1.jpg'
					},
				]
			}
		},
		onLoad(options) {
			if (options.userId) {
				this.userId = options.userId;
			}
			if (options.username) {
				this.username = options.username;
			}
			
			// 设置顶部标题为聊天对象用户名
			uni.setNavigationBarTitle({
				title: this.username
			});
			
			// 页面加载完成后滚动到底部
			this.$nextTick(() => {
				this.scrollToBottom();
			});
		},
		methods: {
			// 发送消息
			sendMessage() {
				if (!this.inputMessage.trim()) {
					uni.showToast({
						title: '请输入消息内容',
						icon: 'none'
					});
					return;
				}
				
				const newMessage = {
					sender: 'me',
					content: this.inputMessage,
					time: this.getCurrentTime()
				};
				
				this.messageList.push(newMessage);
				this.inputMessage = '';
				
				// 发送消息后滚动到底部
				this.$nextTick(() => {
					this.scrollToBottom();
				});
			},
			
			// 初始化mescroll对象
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			// 滚动到底部
			scrollToBottom() {
				this.mescroll.scrollTo(99999, 0)	
			},
			
			// 获取当前时间（格式：HH:mm）
			getCurrentTime() {
				const now = new Date();
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			},
			
			// 监听滚动事件
			onScroll(e) {
				// 可以在这里处理滚动相关逻辑
			}
		}
	}
</script>

<style scoped>
	.chat-container {
		position: relative;
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #f5f5f5;
	}
	
	/* 顶部订单信息样式 */
	.order-info {
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #e0e0e0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		/* position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999; */
	}
	
	.order-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.order-status {
		font-size: 24rpx;
		color: #fa7c25;
	}
	
	/* 聊天记录区域样式 */
	.chat-messages {
		flex: 1;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
		padding-bottom: 120rpx;
		background-color: #f5f5f5;
	}
	
	.message-list {
		display: flex;
		flex-direction: column;
	}
	
	.message-item {
		display: flex;
		margin-bottom: 30rpx;
	}
	
	.message-other {
		justify-content: flex-start;
	}
	
	.message-mine {
		justify-content: flex-end;
	}
	
	.avatar {
		flex-shrink: 0;
		margin: 0 20rpx;
	}
	
	.message-content-wrapper {
		display: flex;
		flex-direction: column;
		max-width: 70%;
	}
	
	.message-time {
		font-size: 20rpx;
		color: #999999;
		margin-bottom: 10rpx;
	}
	
	.message-time.me {
		text-align: right;
	}
	
	.message-content {
		padding: 20rpx;
		border-radius: 10rpx;
		position: relative;
	}
	
	.message-other .message-content {
		background-color: #ffffff;
		border: 1rpx solid #e0e0e0;
	}
	
	.message-mine .message-content {
		background-color: #409EFF;
		color: #ffffff;
	}
	
	.message-text {
		font-size: 28rpx;
		word-wrap: break-word;
		word-break: break-all;
	}
	
	/* 输入区域样式 */
	.chat-input-area {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		padding: 20rpx;
		background-color: #ffffff;
		border-top: 1rpx solid #e0e0e0;
	}
	
	.chat-input {
		flex: 1;
		padding: 15rpx 20rpx;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
	
	.send-button {
		margin-left: 20rpx;
		background-color: #409EFF;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
</style>