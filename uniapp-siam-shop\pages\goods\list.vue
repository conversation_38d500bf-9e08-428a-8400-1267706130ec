<template>
  <view class="content">
    <view class="content-box">
      <!-- 左侧分类 -->
      <view class="left-menu" v-if="allGoodsList.length != 0">
        <scroll-view
          :scroll-y="true"
          :scroll-x="false"
          class="menu-scroll"
          :scroll-into-view="'menu' + activeMenuId"
        >
          <view
            v-for="(item, index) in menuList"
            :key="item.id"
            class="menu-item"
            :class="{ active: activeMenuId === item.id }"
            @click="selectMenu(index)"
            :id="'menu' + item.id"
          >
            <text class="menu-text">{{ item.name }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧商品列表 -->
      <view class="right-content">
        <mescroll-uni
          ref="mescrollRef"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @scroll="onScroll"
          :down="{ auto: true }"
          :fixed="false"
          :up="{ auto: false, empty: { tip: '暂无商品数据' } }"
        >
          <view class="contInfo">
            <template v-for="(item, index) in allGoodsList">
              <!-- 添加分组标题 -->
              <view
                v-if="
                  index === 0 || item.menuId !== allGoodsList[index - 1].menuId
                "
                class="category-title"
                :key="'category-' + item.menuId"
                :id="'category-' + item.menuId"
              >
                {{ item.menuName }}
              </view>
              <view class="goods-item margin-bottom" @click="viewGoods(item)">
                <view class="contInfo-title-box">
                  <view class="goods-image-wrapper">
                    <!-- 添加商品状态标签 -->
                    <view
                      v-if="item.goodsStatus"
                      class="status-tag"
                      :class="getStatusTagClass(item.goodsStatus)"
                    >
                      {{ getStatusTagText(item.goodsStatus) }}
                    </view>
                    <u-image
                      width="100%"
                      height="100%"
                      :src="getImageUrl(item.mainImage)"
                    ></u-image>
                  </view>
                  <view class="contInfo-title">
                    <text class="goods-desc" v-if="item.goodsName">{{
                      item.goodsName
                    }}</text>
                    <text class="contTitle-money">
                      ¥{{ item.goodsPrice }}
                      <text class="package-fee" v-if="item.packingCharges"
                        >包装费: ¥{{ item.packingCharges }}</text
                      >
                    </text>
                  </view>
                </view>
                <view class="footerBoten">
                  <view
                    class="action-btn edit-btn"
                    @click.stop="editGoods(item)"
                    >编辑</view
                  >
                  <view
                    class="action-btn spec-btn"
                    @click.stop="editGoodsSpec(item)"
                    >规格</view
                  >
                  <!-- <view
                    class="action-btn delete-btn"
                    @click.stop="toggleStatus(item)"
                    >删除</view
                  > -->
                </view>
              </view>
            </template>
          </view>
        </mescroll-uni>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="add-button-container">
      <button class="add-btn" @click="editCategory">商品栏目</button>
      <button class="add-button" @click="addGoods">添加商品</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      menuList: [],
      goodsList: [],
      allGoodsList: [], // 新增：存储所有商品
      activeMenuIndex: 0,
      activeMenuId: 0, // 新增：当前激活的菜单ID
      shopId: 0, // 需要根据实际情况获取shopId
      isManualScrolling: false, // 新增：标识是否为手动点击滚动
    };
  },
  onShow() {
    // 检查是否需要刷新列表
    const needRefresh = uni.getStorageSync("goodsListNeedRefresh");
    if (needRefresh) {
      // 清除标记
      uni.removeStorageSync("goodsListNeedRefresh");
      // 刷新列表
      this.refresh();
    }
  },
  onLoad(props) {
    this.shopId = props.shopId;
  },
  mounted() {
    // this.loadMenuList();
  },
  methods: {
    // 初始化mescroll对象
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
      // 监听滚动事件
      this.mescroll.optUp.onScroll = (e) => this.onScroll(e);
    },
    // 主动触发下拉刷新
    refresh() {
      if (this.mescroll) {
        this.mescroll.triggerDownScroll();
      }
    },
    // 加载分类列表
    async loadMenuList() {
      try {
        const res = await this.$request({
          url: "/api-goods/rest/menu/listWithGoodsByMerchant",
          method: "POST",
          data: { shopId: this.shopId },
        });

        if (res.code === 200) {
          this.menuList = res.data;

          // 修改为显示所有商品
          this.allGoodsList = [];
          this.menuList.forEach((menu) => {
            if (menu.goodsList && menu.goodsList.length > 0) {
              menu.goodsList.forEach((goods) => {
                // 为每个商品添加menuId和menuName属性
                goods.menuId = menu.id;
                goods.menuName = menu.name;
              });
              this.allGoodsList = this.allGoodsList.concat(menu.goodsList);
            }
          });

          // 初始化激活的菜单ID
          if (this.menuList.length > 0) {
            this.activeMenuId = this.menuList[0].id;
          }
          this.mescroll.endSuccess(this.allGoodsList.length, false);
        }
      } catch (error) {
        console.error("加载分类失败:", error);
        this.mescroll.endErr();
      }
    },
    // 选择分类
    selectMenu(index) {
      // 设置手动滚动标识，防止滚动过程中被自动更新覆盖
      this.isManualScrolling = true;
      this.activeMenuIndex = index;
      this.activeMenuId = this.menuList[index].id;

      // 滚动到对应的分类标题
      const targetElement = "category-" + this.menuList[index].id;
      this.$nextTick(() => {
        uni
          .createSelectorQuery()
          .select("#" + targetElement)
          .boundingClientRect((res) => {
            if (res) {
              // 计算滚动位置
              this.mescroll.scrollTo(res.top - 50, 300);

              // 滚动完成后重置手动滚动标识
              setTimeout(() => {
                this.isManualScrolling = false;
              }, 500); // 给滚动动画足够的时间完成
            }
          })
          .exec();
      });
    },
    // 滚动监听事件
    onScroll(e) {
      // 防抖处理
      clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.checkCategoryPosition(e.scrollTop);
      }, 100);
    },
    // 检查分类位置并更新激活状态
    checkCategoryPosition(scrollTop) {
      // 如果正在手动滚动，跳过自动更新
      if (this.isManualScrolling) {
        return;
      }

      // 获取所有分类标题的位置信息
      const query = uni.createSelectorQuery();
      this.menuList.forEach((menu) => {
        query.select("#category-" + menu.id).boundingClientRect();
      });

      query.exec((res) => {
        if (!res || res.length === 0) return;

        // 查找当前可视区域顶部的分类
        let currentMenuId = this.activeMenuId;
        for (let i = res.length - 1; i >= 0; i--) {
          const item = res[i];
          if (item && item.top <= 50) {
            // 50是误差范围
            currentMenuId = this.menuList[i].id;
            break;
          }
        }

        // 更新激活的菜单ID
        if (this.activeMenuId !== currentMenuId) {
          this.activeMenuId = currentMenuId;
          // 同步更新索引
          const index = this.menuList.findIndex(
            (menu) => menu.id === currentMenuId
          );
          if (index !== -1) {
            this.activeMenuIndex = index;
          }
        }
      });
    },
    upCallback(e) {
      this.loadMenuList();
    },
    // 下拉回调函数
    downCallback(e) {
      this.mescroll.resetUpScroll();
    },
    // 获取完整图片URL
    getImageUrl(imageUrl) {
      if (!imageUrl) {
        return "/static/icon/empty.png";
      }
      // 添加公共前缀
      return "https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/" + imageUrl;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: "待上架",
        1: "已上架",
        2: "已下架",
        3: "售罄",
      };
      return statusMap[status] || "未知";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: "status-pending",
        1: "status-online",
        2: "status-offline",
        3: "status-soldout",
      };
      return classMap[status] || "";
    },
    // 获取操作按钮文本
    getActionText(status) {
      const actionMap = {
        0: "上架",
        1: "下架",
        2: "上架",
        3: "上架",
      };
      return actionMap[status] || "操作";
    },
    // 查看商品详情
    viewGoods(item) {
      uni.navigateTo({
        url: `/pages/goods/detail?id=${item.goodsId}`,
      });
    },
    // 新增商品
    addGoods() {
      uni.navigateTo({
        url: "/pages/goods/edit",
      });
    },
    // 新增商品
    editCategory() {
      uni.navigateTo({
        url: "/pages/goods/category",
      });
    },
    // 编辑商品
    editGoods(item) {
      uni.navigateTo({
        url: `/pages/goods/edit?id=${item.goodsId}`,
      });
    },
    // 编辑商品规格
    editGoodsSpec(item) {
      uni.navigateTo({
        url: `/pages/goods/spec?id=${item.goodsId}&name=${encodeURIComponent(
          item.name
        )}`,
      });
    },
    // 切换商品状态
    toggleStatus(item) {
      uni.showModal({
        title: "提示",
        content: `确定要删除该商品吗？`,
        success: async (res) => {
          if (res.confirm) {
            const res = await this.$request({
              url: "/api-goods/rest/merchant/goods/delete",
              data: { id: item.goodsId },
            });
            if (res.code === 200) {
              uni.showToast({
                title: "操作成功",
                icon: "success",
              });
              this.refresh();
            } else {
              uni.showToast({
                title: res.message || "操作失败",
                icon: "none",
              });
            }
          }
        },
      });
    },
    // 更新商品状态
    async updateGoodsStatus(id, status) {
      try {
        const res = await this.$request({
          url: "/api-goods/rest/merchant/goods/updateStatus",
          data: { id, status },
        });
        if (res.code === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "success",
          });
          this.refresh();
        } else {
          uni.showToast({
            title: res.message || "操作失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },
    // 获取商品状态标签文本
    getStatusTagText(status) {
      const statusMap = {
        1: "待上架",
        2: "已上架",
        3: "已下架",
        4: "售罄",
      };
      return statusMap[status] || "未知";
    },
    // 获取商品状态标签样式类
    getStatusTagClass(status) {
      const classMap = {
        1: "tag-pending",
        2: "tag-online",
        3: "tag-offline",
        4: "tag-soldout",
      };
      return classMap[status] || "";
    },
  },
};
</script>

<style lang="scss" scoped>
$borderColor: rgba(0, 0, 0, 0.8);
.content {
  padding: 0;
  height: 100%;
  position: relative;
  background-color: $uni-bg-color-grey;

  .content-box {
    position: relative;
    flex: 1;
    max-height: 100vh;
    display: flex;
    flex-direction: row;
  }
}

// 左侧分类菜单
.left-menu {
  width: 200rpx;
  max-height: 100vh;
  background-color: #f5f5f5;
  border-right: 1rpx solid #e5e5e5;

  .menu-scroll {
    height: 100%;
  }

  .menu-item {
    padding: 30rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #e5e5e5;

    &.active {
      background-color: #ffffff;
      border-left: 6rpx solid #fa7c25;
      font-weight: bold;
    }

    .menu-text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}

.contInfo {
  height: auto;
  padding: 0 20rpx 120rpx;
}

.margin-bottom {
  margin-bottom: 20rpx;
}

.goods-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.status-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  z-index: 2;
}

.status-pending {
  background: #ff9500;
}

.status-online {
  background: #34c759;
}

.status-offline {
  background: #8e8e93;
}

.status-soldout {
  background: #ff3b30;
}

.contInfo-title-box {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.goods-image-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative;
}

.goods-image-wrapper image {
  width: 100%;
  height: 100%;
}

// 商品状态标签样式
.status-tag {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  color: #fff;
  border-radius: 0 0 8rpx 0;
  font-weight: 500;
}

.tag-pending {
  background: #ff9500;
}

.tag-online {
  background: #34c759;
}

.tag-offline {
  background: #8e8e93;
}

.tag-soldout {
  background: #ff3b30;
}

.contInfo-title {
  flex: 1;
  min-width: 0;
}

.contTitle {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contTitle-label {
  font-size: 24rpx;
  color: #8e8e93;
  margin-bottom: 12rpx;
}

.goods-desc {
  font-size: 26rpx;
  color: #000;
  font-weight: 500;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.contTitle-money {
  font-size: 32rpx;
  font-weight: 600;
  color: #fa7c25;
  margin-bottom: 12rpx;
}

.package-fee {
  font-size: 24rpx;
  color: #8e8e93;
  margin-left: 20rpx;
}

.goods-stats {
  font-size: 24rpx;
  color: #8e8e93;
}

.footerBoten {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  padding: 0 20rpx;
  height: 52rpx;
  line-height: 52rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
  font-weight: 500;
  border: 1px solid transparent;
}

.edit-btn {
  background: #007aff;
  color: #fff;
}

.spec-btn {
  color: #f90 !important;
  border-color: #fcbd71 !important;
  background-color: #fdf6ec !important;
}

.delete-btn {
  color: #fa3534 !important;
  border-color: #fab6b6 !important;
  background-color: #fef0f0 !important;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #8e8e93;
}

.add-button-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
  display: flex;
  justify-content: center;
  padding: 20rpx 20rpx 40rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  column-gap: 30rpx;
}

.add-button {
  flex: 1;
  background-color: #007aff;
  color: #ffffff;
  font-size: 32rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 10rpx;
}

/* 新增商品按钮 */
.add-btn {
  flex: 1;
  font-size: 32rpx;
  background: #fa7c25;
  display: flex;
  align-items: center;
  color: #fff;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(250, 124, 37, 0.4);
  z-index: 999;
  .add-icon {
    font-size: 68rpx;
    color: #fff;
    font-weight: 300;
    line-height: 1;
    position: absolute;
    top: 12rpx;
  }
}

// 添加分组标题样式
.category-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #000;
  padding: 20rpx 0;
  margin-top: 10rpx;
  border-bottom: 2rpx solid #eee;

  &:first-child {
    margin-top: 0;
  }
}
</style>
