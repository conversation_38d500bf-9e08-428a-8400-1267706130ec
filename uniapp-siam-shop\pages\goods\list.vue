<template>
  <view class="content">
    <view class="content-box">
      <!-- 左侧分类 -->
      <view class="left-menu" v-if="allGoodsList.length != 0">
        <scroll-view
          :scroll-y="true"
          :scroll-x="false"
          class="menu-scroll"
          :scroll-into-view="'menu' + activeMenuId"
        >
          <view
            v-for="(item, index) in menuList"
            :key="item.id"
            class="menu-item"
            :class="{ active: activeMenuId === item.id }"
            @click="selectMenu(index)"
            :id="'menu' + item.id"
          >
            <text class="menu-text">{{ item.name }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧商品列表 -->
      <view class="right-content">
        <!-- 筛选和排序区域 -->
        <view class="filter-sort-container">
          <view class="filter-section">
            <view class="filter-item" @click="showStatusFilter">
              <text>{{ statusFilterText }}</text>
              <uni-icons type="arrowdown" size="14"></uni-icons>
            </view>
            <view class="filter-item" @click="showPromotionFilter">
              <text>{{ promotionFilterText }}</text>
              <uni-icons type="arrowdown" size="14"></uni-icons>
            </view>
            <!-- <view class="filter-item" @click="showCategoryFilter">
              <text>{{ categoryFilterText }}</text>
              <uni-icons type="arrowdown" size="14"></uni-icons>
            </view> -->
          </view>
          <view class="sort-section">
            <view class="sort-item" @click="showSortOptions">
              <text>{{ sortText }}</text>
              <uni-icons type="arrowdown" size="14"></uni-icons>
            </view>
          </view>
        </view>
        
        <mescroll-uni
          ref="mescrollRef"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @scroll="onScroll"
          :down="{ auto: true }"
          :fixed="false"
          :up="{ auto: false, empty: { tip: '暂无商品数据' } }"
        >
          <view class="contInfo">
            <template v-for="(item, index) in filteredAndSortedList">
              <!-- 添加分组标题 -->
              <view
                v-if="
                  index === 0 || item.menuId !== filteredAndSortedList[index - 1].menuId
                "
                class="category-title"
                :key="'category-' + item.menuId"
                :id="'category-' + item.menuId"
              >
                {{ item.menuName }}
              </view>
              <view class="goods-item margin-bottom" @click="viewGoods(item)">
                <view class="contInfo-title-box">
                  <view class="goods-image-wrapper">
                    <!-- 添加商品状态标签 -->
                    <view
                      v-if="item.goodsStatus"
                      class="status-tag"
                      :class="getStatusTagClass(item.goodsStatus)"
                    >
                      {{ getStatusTagText(item.goodsStatus) }}
                    </view>
                    <u-image
                      width="100%"
                      height="100%"
                      :src="getImageUrl(item.mainImage)"
                    ></u-image>
                  </view>
                  <view class="contInfo-title">
                    <text class="goods-desc" v-if="item.goodsName">{{
                      item.goodsName
                    }}</text>
                    <text class="contTitle-money">
                      ¥{{ item.goodsPrice }}
                      <text class="package-fee" v-if="item.packingCharges"
                        >包装费: ¥{{ item.packingCharges }}</text
                      >
                    </text>
                    <view class="goods-sales">
                      <text class="sales-item monthly-sales">月销: {{ item.monthlySales || 0 }}</text>
                      <text class="sales-item total-sales">总销: {{ item.totalSales || 0 }}</text>
                    </view>
                  </view>
                </view>
                <view class="footerBoten">
                  <view
                    class="action-btn edit-btn"
                    @click.stop="editGoods(item)"
                    >编辑</view
                  >
                  <!-- <view
                    class="action-btn spec-btn"
                    @click.stop="editGoodsSpec(item)"
                    >规格</view
                  > -->
                  <view
                    class="action-btn status-btn"
                    :class="getStatusBtnClass(item.goodsStatus)"
                    @click.stop="toggleGoodsStatus(item)"
                    >{{ getStatusBtnText(item.goodsStatus) }}</view
                  >
                </view>
              </view>
            </template>
          </view>
        </mescroll-uni>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="statusFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="header-title">商品状态</text>
          <text class="confirm-btn" @click="confirmStatusFilter">确定</text>
        </view>
        <view class="popup-content">
          <view 
            class="filter-option" 
            v-for="option in statusFilterOptions" 
            :key="option.value"
            @click="selectStatusFilter(option.value)"
            :class="{ active: tempStatusFilter === option.value }"
          >
            <text>{{ option.text }}</text>
            <uni-icons 
              v-if="tempStatusFilter === option.value" 
              type="checkmarkempty" 
              size="20" 
              color="#007aff"
            ></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="promotionFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="header-title">促销状态</text>
          <text class="confirm-btn" @click="confirmPromotionFilter">确定</text>
        </view>
        <view class="popup-content">
          <view 
            class="filter-option" 
            v-for="option in promotionFilterOptions" 
            :key="option.value"
            @click="selectPromotionFilter(option.value)"
            :class="{ active: tempPromotionFilter === option.value }"
          >
            <text>{{ option.text }}</text>
            <uni-icons 
              v-if="tempPromotionFilter === option.value" 
              type="checkmarkempty" 
              size="20" 
              color="#007aff"
            ></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="categoryFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="header-title">商品类别</text>
          <text class="confirm-btn" @click="confirmCategoryFilter">确定</text>
        </view>
        <view class="popup-content">
          <view 
            class="filter-option" 
            v-for="option in categoryFilterOptions" 
            :key="option.value"
            @click="selectCategoryFilter(option.value)"
            :class="{ active: tempCategoryFilter === option.value }"
          >
            <text>{{ option.text }}</text>
            <uni-icons 
              v-if="tempCategoryFilter === option.value" 
              type="checkmarkempty" 
              size="20" 
              color="#007aff"
            ></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 排序弹窗 -->
    <uni-popup ref="sortPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="header-title">默认排序</text>
          <text class="confirm-btn" @click="confirmSort">确定</text>
        </view>
        <view class="popup-content">
          <view 
            class="filter-option" 
            v-for="option in sortOptions" 
            :key="option.value"
            @click="selectSort(option.value)"
            :class="{ active: tempSortBy === option.value }"
          >
            <text>{{ option.text }}</text>
            <uni-icons 
              v-if="tempSortBy === option.value" 
              type="checkmarkempty" 
              size="20" 
              color="#007aff"
            ></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 操作按钮 -->
    <view class="add-button-container">
      <button class="add-btn" @click="editCategory">商品栏目</button>
      <button class="add-button" @click="addGoods">添加商品</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      menuList: [],
      goodsList: [],
      allGoodsList: [], // 新增：存储所有商品
      activeMenuIndex: 0,
      activeMenuId: 0, // 新增：当前激活的菜单ID
      shopId: 0, // 需要根据实际情况获取shopId
      isManualScrolling: false, // 新增：标识是否为手动点击滚动
      
      // 筛选和排序相关数据
      statusFilter: '1', // all, selling, offshelves, soldout
      promotionFilter: '1', // all, promoted, notPromoted
      categoryFilter: 'all', // all, categoryId
      sortBy: '1', // default, monthlySales, totalSales
      
      statusFilterOptions: [
        { value: '1', text: '所有' },
        { value: '2', text: '已上架' },
        { value: '3', text: '已下架' },
        { value: '4', text: '已售完' }
      ],
      promotionFilterOptions: [
        { value: '1', text: '不限' },
        { value: '2', text: '有促销' },
        { value: '3', text: '无促销' }
      ],
      categoryFilterOptions: [
        { value: 'all', text: '不限类别' }
      ],
      sortOptions: [
        { value: '1', text: '默认排序' },
        { value: '2', text: '月销量' },
        { value: '3', text: '总销量' }
      ],
      
      tempStatusFilter: '1',
      tempPromotionFilter: '1',
      tempCategoryFilter: 'all',
      tempSortBy: '1'
    };
  },
  computed: {
    statusFilterText() {
      const option = this.statusFilterOptions.find(opt => opt.value === this.statusFilter);
      return option ? option.text : '商品状态';
    },
    promotionFilterText() {
      const option = this.promotionFilterOptions.find(opt => opt.value === this.promotionFilter);
      return option ? option.text : '促销状态';
    },
    categoryFilterText() {
      const option = this.categoryFilterOptions.find(opt => opt.value === this.categoryFilter);
      return option ? option.text : '商品类别';
    },
    sortText() {
      const option = this.sortOptions.find(opt => opt.value === this.sortBy);
      return option ? option.text : '默认排序';
    },
    filteredAndSortedList() {
      // 应用筛选条件
      let filteredList = this.allGoodsList.filter(item => {
        // 商品状态筛选
        if (this.statusFilter !== '1') {
          if (item.goodsStatus != this.statusFilter) return false;
        }
        
        // 促销状态筛选（这里假设促销状态通过某个字段判断，如promotionStatus）
        if (this.promotionFilter !== '1') {
          // 由于缺少促销状态字段，暂时留空
          // 可以根据实际字段进行判断
        }
        
        // 类别筛选
        if (this.categoryFilter !== 'all') {
          if (item.menuId !== this.categoryFilter) return false;
        }
        
        return true;
      });
      
      // 应用排序
      if (this.sortBy !== '1') {
        filteredList.sort((a, b) => {
          switch (this.sortBy) {
            case '2':
              return (b.monthlySales || 0) - (a.monthlySales || 0);
            case '3':
              return (b.totalSales || 0) - (a.totalSales || 0);
            default:
              return 0;
          }
        });
      }
      
      return filteredList;
    }
  },
  onShow() {
    // 检查是否需要刷新列表
    const needRefresh = uni.getStorageSync("goodsListNeedRefresh");
    if (needRefresh) {
      // 清除标记
      uni.removeStorageSync("goodsListNeedRefresh");
      // 刷新列表
      this.refresh();
    }
  },
  onLoad(props) {
    this.shopId = props.shopId;
  },
  mounted() {
    // this.loadMenuList();
  },
  methods: {
    // 筛选相关方法
    showStatusFilter() {
      this.tempStatusFilter = this.statusFilter;
      this.$refs.statusFilterPopup.open();
    },
    showPromotionFilter() {
      this.tempPromotionFilter = this.promotionFilter;
      this.$refs.promotionFilterPopup.open();
    },
    showCategoryFilter() {
      this.tempCategoryFilter = this.categoryFilter;
      this.$refs.categoryFilterPopup.open();
    },
    showSortOptions() {
      this.tempSortBy = this.sortBy;
      this.$refs.sortPopup.open();
    },
    
    selectStatusFilter(value) {
      this.tempStatusFilter = value;
    },
    selectPromotionFilter(value) {
      this.tempPromotionFilter = value;
    },
    selectCategoryFilter(value) {
      this.tempCategoryFilter = value;
    },
    selectSort(value) {
      this.tempSortBy = value;
    },
    
    confirmStatusFilter() {
      this.statusFilter = this.tempStatusFilter;
      this.$refs.statusFilterPopup.close();
      this.loadMenuList(); // 重新加载数据
    },
    confirmPromotionFilter() {
      this.promotionFilter = this.tempPromotionFilter;
      this.$refs.promotionFilterPopup.close();
      this.loadMenuList(); // 重新加载数据
    },
    confirmCategoryFilter() {
      this.categoryFilter = this.tempCategoryFilter;
      this.$refs.categoryFilterPopup.close();
      this.loadMenuList(); // 重新加载数据
    },
    confirmSort() {
      this.sortBy = this.tempSortBy;
      this.$refs.sortPopup.close();
      this.loadMenuList(); // 重新加载数据
    },
    
    // 初始化mescroll对象
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
      // 监听滚动事件
      this.mescroll.optUp.onScroll = (e) => this.onScroll(e);
    },
    // 主动触发下拉刷新
    refresh() {
      if (this.mescroll) {
        this.mescroll.triggerDownScroll();
      }
    },
    // 加载分类列表
    async loadMenuList() {
      try {
        const res = await this.$request({
          url: "/api-goods/rest/menu/listWithGoodsByMerchant",
          method: "POST",
          data: { 
            shopId: this.shopId,
            goodsStatus: this.statusFilter === '1' ? null : this.statusFilter,
            orderBy: this.sortBy === '1' ? null : this.sortBy,
            goodsType: this.promotionFilter === '1' ? null : this.promotionFilter
          },
        });

        if (res.code === 200) {
          this.menuList = res.data;

          // 修改为显示所有商品
          this.allGoodsList = [];
          this.menuList.forEach((menu) => {
            if (menu.goodsList && menu.goodsList.length > 0) {
              menu.goodsList.forEach((goods) => {
                // 为每个商品添加menuId和menuName属性
                goods.menuId = menu.id;
                goods.menuName = menu.name;
              });
              this.allGoodsList = this.allGoodsList.concat(menu.goodsList);
            }
          });
          
          // 更新类别筛选选项
          this.categoryFilterOptions = [
            { value: 'all', text: '不限类别' },
            ...this.menuList.map(menu => ({
              value: menu.id,
              text: menu.name
            }))
          ];

          // 初始化激活的菜单ID
          if (this.menuList.length > 0) {
            this.activeMenuId = this.menuList[0].id;
          }
          this.mescroll.endSuccess(this.allGoodsList.length, false);
        }
      } catch (error) {
        console.error("加载分类失败:", error);
        this.mescroll.endErr();
      }
    },
    // 选择分类
    selectMenu(index) {
      // 设置手动滚动标识，防止滚动过程中被自动更新覆盖
      this.isManualScrolling = true;
      this.activeMenuIndex = index;
      this.activeMenuId = this.menuList[index].id;

      // 滚动到对应的分类标题
      const targetElement = "category-" + this.menuList[index].id;
      this.$nextTick(() => {
        uni
          .createSelectorQuery()
          .select("#" + targetElement)
          .boundingClientRect((res) => {
            if (res) {
              // 计算滚动位置
              this.mescroll.scrollTo(res.top - 50, 300);

              // 滚动完成后重置手动滚动标识
              setTimeout(() => {
                this.isManualScrolling = false;
              }, 500); // 给滚动动画足够的时间完成
            }
          })
          .exec();
      });
    },
    // 滚动监听事件
    onScroll(e) {
      // 防抖处理
      clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.checkCategoryPosition(e.scrollTop);
      }, 100);
    },
    // 检查分类位置并更新激活状态
    checkCategoryPosition(scrollTop) {
      // 如果正在手动滚动，跳过自动更新
      if (this.isManualScrolling) {
        return;
      }

      // 获取所有分类标题的位置信息
      const query = uni.createSelectorQuery();
      this.menuList.forEach((menu) => {
        query.select("#category-" + menu.id).boundingClientRect();
      });

      query.exec((res) => {
        if (!res || res.length === 0) return;

        // 查找当前可视区域顶部的分类
        let currentMenuId = this.activeMenuId;
        for (let i = res.length - 1; i >= 0; i--) {
          const item = res[i];
          if (item && item.top <= 50) {
            // 50是误差范围
            currentMenuId = this.menuList[i].id;
            break;
          }
        }

        // 更新激活的菜单ID
        if (this.activeMenuId !== currentMenuId) {
          this.activeMenuId = currentMenuId;
          // 同步更新索引
          const index = this.menuList.findIndex(
            (menu) => menu.id === currentMenuId
          );
          if (index !== -1) {
            this.activeMenuIndex = index;
          }
        }
      });
    },
    upCallback(e) {
      this.loadMenuList();
    },
    // 下拉回调函数
    downCallback(e) {
      this.mescroll.resetUpScroll();
    },
    // 获取完整图片URL
    getImageUrl(imageUrl) {
      if (!imageUrl) {
        return "/static/icon/empty.png";
      }
      // 添加公共前缀
      return "https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/" + imageUrl;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: "待上架",
        1: "已上架",
        2: "已下架",
        3: "售罄",
      };
      return statusMap[status] || "未知";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: "status-pending",
        1: "status-online",
        2: "status-offline",
        3: "status-soldout",
      };
      return classMap[status] || "";
    },
    // 获取操作按钮文本
    getActionText(status) {
      const actionMap = {
        0: "上架",
        1: "下架",
        2: "上架",
        3: "上架",
      };
      return actionMap[status] || "操作";
    },
    // 查看商品详情
    viewGoods(item) {
      uni.navigateTo({
        url: `/pages/goods/detail?id=${item.goodsId}`,
      });
    },
    // 新增商品
    addGoods() {
      uni.navigateTo({
        url: "/pages/goods/edit",
      });
    },
    // 新增商品
    editCategory() {
      uni.navigateTo({
        url: "/pages/goods/category",
      });
    },
    // 编辑商品
    editGoods(item) {
      uni.navigateTo({
        url: `/pages/goods/edit?id=${item.goodsId}`,
      });
    },
    // 编辑商品规格
    editGoodsSpec(item) {
      uni.navigateTo({
        url: `/pages/goods/spec?id=${item.goodsId}&name=${encodeURIComponent(
          item.name
        )}`,
      });
    },
    // 切换商品状态
    toggleStatus(item) {
      uni.showModal({
        title: "提示",
        content: `确定要删除该商品吗？`,
        success: async (res) => {
          if (res.confirm) {
            const res = await this.$request({
              url: "/api-goods/rest/merchant/goods/delete",
              data: { id: item.goodsId },
            });
            if (res.code === 200) {
              uni.showToast({
                title: "操作成功",
                icon: "success",
              });
              this.refresh();
            } else {
              uni.showToast({
                title: res.message || "操作失败",
                icon: "none",
              });
            }
          }
        },
      });
    },
    // 更新商品状态
    async updateGoodsStatus(id, status) {
      try {
        const res = await this.$request({
          url: "/api-goods/rest/merchant/goods/update",
          data: { id, status },
        });
        if (res.code === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "success",
          });
          this.refresh();
        } else {
          uni.showToast({
            title: res.message || "操作失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },
    // 获取商品状态标签文本
    getStatusTagText(status) {
      const statusMap = {
        1: "待上架",
        2: "已上架",
        3: "已下架",
        4: "售罄",
      };
      return statusMap[status] || "未知";
    },
    // 获取商品状态标签样式类
    getStatusTagClass(status) {
      const classMap = {
        1: "tag-pending",
        2: "tag-online",
        3: "tag-offline",
        4: "tag-soldout",
      };
      return classMap[status] || "";
    },
    // 获取状态按钮文本
    getStatusBtnText(status) {
      const textMap = {
        1: "上架", // 待上架 -> 上架
        2: "下架", // 已上架 -> 下架
        3: "上架", // 已下架 -> 上架
        4: "上架", // 售罄 -> 上架
      };
      return textMap[status] || "上架";
    },
    // 获取状态按钮样式类
    getStatusBtnClass(status) {
      const classMap = {
        1: "status-online-btn", // 待上架 -> 绿色上架按钮
        2: "status-offline-btn", // 已上架 -> 灰色下架按钮
        3: "status-online-btn", // 已下架 -> 绿色上架按钮
        4: "status-online-btn", // 售罄 -> 绿色上架按钮
      };
      return classMap[status] || "status-online-btn";
    },
    // 切换商品状态
    toggleGoodsStatus(item) {
      const currentStatus = item.goodsStatus;
      let newStatus;
      let actionText;

      // 根据当前状态确定新状态
      if (currentStatus === 2) {
        // 已上架 -> 下架
        newStatus = 3;
        actionText = "下架";
      } else {
        // 其他状态 -> 上架
        newStatus = 2;
        actionText = "上架";
      }

      uni.showModal({
        title: "提示",
        content: `确定要${actionText}该商品吗？`,
        success: async (res) => {
          if (res.confirm) {
            await this.updateGoodsStatus(item.goodsId, newStatus);
          }
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
$borderColor: rgba(0, 0, 0, 0.8);
.content {
  padding: 0;
  height: 100%;
  position: relative;
  background-color: $uni-bg-color-grey;

  .content-box {
    position: relative;
    flex: 1;
    max-height: 100vh;
    display: flex;
    flex-direction: row;
  }
}

// 筛选和排序区域
.filter-sort-container {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  
  .filter-section {
    display: flex;
    align-items: center;
    
    .filter-item {
      display: flex;
      align-items: center;
      margin-right: 30rpx;
      font-size: 26rpx;
      color: #333;
    }
  }
  
  .sort-section {
    .sort-item {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #333;
    }
  }
}

// 筛选弹窗样式
.filter-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding: 20rpx;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #eee;
    
    .header-title {
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .confirm-btn {
      color: #007aff;
      font-size: 28rpx;
    }
  }
  
  .popup-content {
    padding: 20rpx 0;
    
    .filter-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      font-size: 28rpx;
      
      &.active {
        color: #007aff;
      }
    }
  }
}

// 左侧分类菜单
.left-menu {
  width: 200rpx;
  max-height: 100vh;
  background-color: #f5f5f5;
  border-right: 1rpx solid #e5e5e5;

  .menu-scroll {
    height: 100%;
  }

  .menu-item {
    padding: 30rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #e5e5e5;

    &.active {
      background-color: #ffffff;
      border-left: 6rpx solid #fa7c25;
      font-weight: bold;
    }

    .menu-text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}

.contInfo {
  height: auto;
  padding: 0 20rpx 120rpx;
}

.margin-bottom {
  margin-bottom: 20rpx;
}

.goods-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.status-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  z-index: 2;
}

.status-pending {
  background: #ff9500;
}

.status-online {
  background: #34c759;
}

.status-offline {
  background: #8e8e93;
}

.status-soldout {
  background: #ff3b30;
}

.contInfo-title-box {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.goods-image-wrapper {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative;
}

.goods-image-wrapper image {
  width: 100%;
  height: 100%;
}

// 商品状态标签样式
.status-tag {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  color: #fff;
  border-radius: 0 0 8rpx 0;
  font-weight: 500;
}

.tag-pending {
  background: #ff9500;
}

.tag-online {
  background: #34c759;
}

.tag-offline {
  background: #8e8e93;
}

.tag-soldout {
  background: #ff3b30;
}

.contInfo-title {
  flex: 1;
  min-width: 0;
}

.contTitle {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contTitle-label {
  font-size: 24rpx;
  color: #8e8e93;
  margin-bottom: 12rpx;
}

.goods-desc {
  font-size: 26rpx;
  color: #000;
  font-weight: 500;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.contTitle-money {
  font-size: 32rpx;
  line-height: 32rpx;
  font-weight: 600;
  color: #fa7c25;
  margin-bottom: 12rpx;
}

.package-fee {
  font-size: 24rpx;
  color: #8e8e93;
  margin-left: 20rpx;
}

.goods-stats {
  font-size: 24rpx;
  color: #8e8e93;
}

.goods-sales {
  display: flex;
  gap: 24rpx;
  margin-top: 8rpx;
}

.sales-item {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid transparent;
  font-weight: 500;
}

.monthly-sales {
  color: #007aff;
  background-color: #e8f4ff;
  border-color: #b3d9ff;
}

.total-sales {
  color: #34c759;
  background-color: #e8f5e8;
  border-color: #a8e6a3;
}

.footerBoten {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  padding: 0 20rpx;
  height: 52rpx;
  line-height: 52rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
  font-weight: 500;
  border: 1px solid transparent;
}

.edit-btn {
  background: #007aff;
  color: #fff;
}

.spec-btn {
  color: #f90 !important;
  border-color: #fcbd71 !important;
  background-color: #fdf6ec !important;
}

.delete-btn {
  color: #fa3534 !important;
  border-color: #fab6b6 !important;
  background-color: #fef0f0 !important;
}

.status-btn {
  font-weight: 500;
}

.status-online-btn {
  color: #34c759 !important;
  border-color: #7dd87f !important;
  background-color: #f0f9f0 !important;
}

.status-offline-btn {
  color: #fff !important;
  border-color: #fa3534 !important;
  background-color: #fa3534 !important;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #8e8e93;
}

.add-button-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
  display: flex;
  justify-content: center;
  padding: 20rpx 20rpx 40rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  column-gap: 30rpx;
}

.add-button {
  flex: 1;
  background-color: #007aff;
  color: #ffffff;
  font-size: 32rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 10rpx;
}

/* 新增商品按钮 */
.add-btn {
  flex: 1;
  font-size: 32rpx;
  background: #fa7c25;
  display: flex;
  align-items: center;
  color: #fff;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(250, 124, 37, 0.4);
  z-index: 999;
  .add-icon {
    font-size: 68rpx;
    color: #fff;
    font-weight: 300;
    line-height: 1;
    position: absolute;
    top: 12rpx;
  }
}

// 添加分组标题样式
.category-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #000;
  padding: 20rpx 0;
  margin-top: 10rpx;
  border-bottom: 2rpx solid #eee;

  &:first-child {
    margin-top: 0;
  }
}
</style>