<template>
	<view class="withdrawalSet">
		<view class="const-box">
<!-- 			<uni-status-bar background="transparent"></uni-status-bar>
			<my-nav-bar :status-bar="false" background="transparent" icon-color="#fff"></my-nav-bar> -->
			<view class="title-box">
				<view class="title-name">提现方式</view>
				<view class="title-trip">请先选择您提现的渠道方式（为了保证系统打款成功，请真实的填写以下相关信息）</view>
			</view>
			<view class="bgColor"></view>
		</view>
		<view class="form-box">
			<view class="form-box-type flex flex-align-center justify-space-center" v-if="configInfo">
				<my-icon class="icon-type" size="100" :color="formData.type == 1 ? '#00ab05' : '#C0C0C0'" @click="changType(1)" v-if="configInfo.channel.indexOf('1') >= 0">
					&#xe633;
				</my-icon>
				<my-icon class="icon-type" size="100" :color="formData.type == 2 ? '#03a4f1' : '#C0C0C0'" @click="changType(2)" v-if="configInfo.channel.indexOf('2') >= 0">
					&#xe665;
				</my-icon>
				<my-icon class="icon-type" size="100" :color="formData.type == 3 ? '#FBB41C' : '#C0C0C0'" @click="changType(3)" v-if="configInfo.channel.indexOf('3') >= 0">
					&#xe613;
				</my-icon>
			</view>
			<my-line></my-line>
			<view v-if="formData.type == 1">
				<view class="form-item flex flex-align-center">
					<text class="label-name">真实姓名</text>
					<input class="label-input" type="text" maxlength="11" v-model="formData.realName" placeholder="输入真实姓名" />
				</view>
				<view class="form-item flex flex-align-center">
					<text class="label-name">手机号</text>
					<input class="label-input" type="number" maxlength="11" v-model="formData.wxTel" placeholder="输入手机号" />
				</view>
				<my-line></my-line>
				<view class="form-item flex flex-align-center">
					<text class="label-name">微信收款码</text>
					<view class="photograph-btn" @click="UpLoadPic('wxImg')" v-if="!formData.wxImg"><uni-icons type="camera" size="60" color="#aaa"></uni-icons></view>
					<view class="photograph-pic-box" :class="{ handPreview: handPreviewShow }" v-else>
						<image class="photograph-pic" :src="formData.wxImg" mode="widthFix" @click="handPreview"></image>
						<uni-icons class="close-btn" size="40" type="close" @click="del('wxImg')"></uni-icons>
					</view>
				</view>
			</view>
			<view v-if="formData.type == 2">
				<view class="form-item flex flex-align-center">
					<text class="label-name">真实姓名</text>
					<input class="label-input" type="text" maxlength="11" v-model="formData.realName" placeholder="输入真实姓名" />
				</view>
				<view class="form-item flex flex-align-center">
					<text class="label-name">手机号</text>
					<input class="label-input" type="number" maxlength="11" v-model="formData.aliTel" placeholder="输入手机号" />
				</view>
				<view class="form-item flex flex-align-center">
					<text class="label-name">支付宝账号</text>
					<input class="label-input" type="text" maxlength="11" v-model="formData.aliTel" placeholder="输入手机号" />
				</view>
				<my-line></my-line>
				<view class="form-item flex flex-align-center">
					<text class="label-name">支付宝收款码</text>
					<view class="photograph-btn" @click="UpLoadPic('aliImg')" v-if="!formData.aliImg"><uni-icons type="camera" size="60" color="#aaa"></uni-icons></view>
					<view class="photograph-pic-box" :class="{ handPreview: handPreviewShow }" v-else>
						<image class="photograph-pic" :src="formData.aliImg" mode="widthFix" @click="handPreview"></image>
						<uni-icons class="close-btn" size="40" type="close" @click="del('aliImg')"></uni-icons>
					</view>
				</view>
			</view>
			<view v-if="formData.type == 3">
				<view class="form-item flex flex-align-center">
					<text class="label-name">真实姓名</text>
					<input class="label-input" type="text" v-model="formData.bankUserName" placeholder="请输入" />
				</view>
				<my-line></my-line>
				<view class="form-item flex flex-align-center">
					<text class="label-name">银行预留电话</text>
					<input class="label-input" type="number" maxlength="11" v-model="formData.bankTel" placeholder="输入银行预留电话" />
				</view>
				<my-line></my-line>
				<view class="form-item flex flex-align-center">
					<text class="label-name">银行卡号</text>
					<input class="label-input" type="number" v-model="formData.bankAccount" placeholder="输入银行卡号" />
				</view>
				<my-line></my-line>
				<view class="form-item flex flex-align-center">
					<text class="label-name">开户行</text>
					<input class="label-input" type="text" v-model="formData.bankName" placeholder="请输入" />
				</view>
				<my-line></my-line>
				<view class="form-item flex flex-align-center">
					<text class="label-name">所属支行</text>
					<input class="label-input" type="text" v-model="formData.subBank" placeholder="请输入" />
				</view>
			</view>
		</view>
		<view class="footer-btn"><my-button color="#fff" width="690" @confirm="submit" >确认绑定</my-button></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			type: 1,
			disabled: true,
			formData: {
				ident: 'finance',
				identName: '骑手提现',
				realName: '',
				type: 1,
				wxTel: '',
				wxImg: '',
				aliTel: '',
				aliImg: '',
				bankTel: '',
				bankName: '',
				subBank: '',
				bankUserName: '',
				bankAccount: ''
			},
			rules: {
				1: {
					realName: '输入真实姓名！',
					wxTel: '输入微信手机号！',
					wxImg: '微信收款码不能为空！'
				},
				2: {
					aliTel: '输入支付宝手机号！',
					aliImg: '支付宝收款码不能为空！'
				},
				3: {
					bankUserName: '输入开户人姓名！',
					bankTel: '预留手机号不能为空！',
					bankAccount: '银行卡号不能为空！',
					bankName: '输入开户行名称！',
					subBank: '输入所属支行！'
				}
			},
			configInfo: {
				channel: '1,2,3',
			},
			handPreviewShow: false
		};
	},
	onLoad() {
		this.getConfigInfo();
		this.getCashWith(1);
	},
	methods: {
		getConfigInfo() {
			this.$request({
				url: '/config/config',
				data: {
					ident: 'withdrawal'
				},
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.configInfo = res.data;
				}
			});
		},
		// 获取提现设置
		getCashWith(type) {
			this.$request({
				url: '/config/rider-config',
				data: { ident: 'finance' },
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					Object.assign(this.formData, res.data);
				}
			});
		},
		// 图片预览
		handPreview() {
			this.handPreviewShow = !this.handPreviewShow;
		},
		// 删除图片
		del(msg) {
			this.formData[msg] = '';
		},
		changType(msg) {
			uni.showLoading({
				mask: true,
				title: '加载中...'
			});
			this.formData.type = msg;
			setTimeout(() => {
				uni.hideLoading();
			}, 500);
		},
		UpLoadPic(key) {
			this.$common.file_select({ sourceType: ['album'], quality: 50 }).then(res => {
				this.$common.UpLoadFile(res).then(msg => {
					this.formData[key] = msg.toString();
				});
			});
		},
		submit() {
			this.$interactive.formIsEmpty(this.formData, this.rules[this.formData.type]).then(flag => {
				if (flag) {
					this.$request({
						url: '/config/rider-config',
						data: this.formData
					}).then(res => {
						// #ifdef MP-WEIXIN
							res = JSON.parse(res)
						// #endif
						if (res.code == 1) {
							this.$interactive
								.ShowToast({
									title: '提交成功！'
								})
								.then(() => {
									uni.navigateBack({
										delta: '1'
									});
								});
						} else {
							this.$interactive.ShowToast(
								{
									title: res.message
								},
								false
							);
						}
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.withdrawalSet {
	height: 100vh;
	padding-bottom: 50rpx;

	.const-box {
		position: relative;
		background: #ff6320;
		padding-bottom: 40rpx;

		.title-box {
			padding: 0 32rpx;

			.title-name {
				font-size: $font-my-size-40;
				color: #ffffff;
				padding: 20rpx 0;
			}

			.title-trip {
				font-size: $font-my-size-28;
				color: #ffffff;
			}
		}

		.bgColor {
			position: absolute;
			left: 0rpx;
			right: 0rpx;
			bottom: -300rpx;
			height: 300rpx;
			background: linear-gradient(#ff6320 50%, #fff 50%);
			z-index: 0;
		}
	}

	.form-box {
		position: relative;
		z-index: 1;
		background: #ffffff;
		margin: 0 30rpx;
		border-radius: 10rpx;
		padding: 20rpx 32rpx;
		box-shadow: 0 0 15rpx 0rpx rgba(0, 0, 0, 0.1);

		.form-box-type {
			margin: 0 auto;
			padding: 30rpx 0;

			.icon-type {
				transition: all 0.1s ease;

				&:nth-child(2) {
					margin: 0 100rpx;
				}
			}
		}

		.form-item {
			padding: 30rpx 0;

			.label-name {
				width: 200rpx;
				font-size: $font-my-size-30;
			}

			.label-input {
				flex: 1;
			}

			.photograph-btn {
				border: 1rpx dashed #aaa;
				width: 250rpx;
				height: 250rpx;
				line-height: 230rpx;
				text-align: center;
				border-radius: 10rpx;
			}

			.photograph-pic-box {
				position: relative;
				width: 250rpx;
				height: 250rpx;
				transition: all 0.1s ease;

				.photograph-pic {
					width: 100%;
					min-width: 100%;
					max-width: 100%;
					max-height: 100%;
				}

				.close-btn {
					position: absolute;
					top: -30rpx;
					right: -30rpx;
				}
			}

			.handPreview {
				position: fixed;
				top: 0rpx;
				bottom: 0rpx;
				left: 0rpx;
				right: 0rpx;
				background: #000;
				z-index: 999999;
				width: auto;
				height: auto;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}

			.photograph-pic {
				max-height: auto;
			}
		}
	}

	.footer-btn {
		position: fixed;
		left: 0rpx;
		right: 0rpx;
		bottom: 20rpx;
	}
}
</style>
