<template>
	<view class="accCors">
		<uni-status-bar background="#ff6320"></uni-status-bar>
		<view class="accCors-cont">
			<view class="accCors-cont-list flex flex-align-center justify-space-between">
				<uni-icons type="back" color="#fff" size="48" @click="$common.handBack"></uni-icons>
				<!-- /pages/accountCore/bondMoney -->
				<navigator class="accCors-cont-list-router" url="/pages/accountCore/bondMoney" hover-class="none">
					<my-icon class="accCors-cont-list-icon" color="#fff" size="32">&#xe641;</my-icon>
					<view class="bond-money">保证金</view>
				</navigator>
			</view>
			<view class="money-box flex flex-align-end justify-space-between">
				<view class="money-box-l">
					<text class="price">{{ formData ? formData.money : '0' }}</text>
					<view class="price-trip">
						<text>可提现金额(元)</text>
						<u-icon name="question-circle" color="#fff" size="28"></u-icon>
					</view>
				</view>
				<my-button class="cont-val" width="200" background="#fff" color="#ff6320" height="60" @confirm="handGoToGell(formData ? formData.money : '0.00')">去提现</my-button>
			</view>
		</view>
		<view class="mainCont">
			<view class="mainCont-modul">
				<view class="flex flex-align-center" style="width: 670rpx; padding-top: 20rpx; min-height: 108rpx;">
					<custom-tabs
						style="flex: 1; width: 0rpx;"
						name="hours"
						:list="timeList"
						:duration="duration"
						:background="false"
						:show-bar="false"
						:current="dataBarIndex"
						@change="dataBarClick"
					></custom-tabs>
					<view class="static-data">
						<navigator url="/pages/accountCore/staticForm" hover-class="<none></none>">
							<u-icon name="red-packet-fill" color="#333" size="36"></u-icon>
							<view class="cont-text">统计</view>
						</navigator>
					</view>
				</view>
				<view class="incoMoney">
					<view class="MoneyNum">{{ dataInfo ? dataInfo.todayIncome : '0.00' }}</view>
					<text>{{ dataBarIndex + 1 == timeList.length ? '今日' : timeList.length ? timeList[dataBarIndex].hours : '今日' }}收入(元)</text>
				</view>
				<view class="flex flex-align-end justify-space-between">
					<view class="incoMoney-two">
						<view class="money-num">{{ '0.00' }}</view>
						<text>奖励(元)</text>
					</view>
					<view class="incoMoney-two">
						<view class="money-num">{{'0.00' }}</view>
						<text>支出(元)</text>
						<view class="info-pop-up" @touchstart.stop="popUpShow = true" v-if="popUpShow">
							<u-section
								style="margin-bottom: 40rpx;"
								font-size="22"
								:bold="false"
								color="#FFE100"
								:show-line="false"
								sub-color="#FFE100"
								:arrow="false"
								title="总计"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="基础佣金"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="距离奖励"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="重量奖励"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="上门奖励"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="保价单奖励"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="大额单奖励"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="特殊时段补贴"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="准时配送奖励"
								sub-title="￥25"
							></u-section>
							<u-section
								style="padding: 5rpx 0rpx;"
								font-size="22"
								:bold="false"
								color="#FFFFFF"
								:show-line="false"
								sub-color="#FFFFFF"
								:arrow="false"
								title="准时上门奖励"
								sub-title="￥25"
							></u-section>
						</view>
					</view>
				</view>
			</view>
			<view class="mainCont-modul-two">
				<view class="mainCont-modul-two-title">收入明细</view>
				<custom-tabs
					class="custom-u-tabs"
					ref="tabs"
					:is-scroll="false"
					:list="list"
					:show-bar="false"
					:bold="false"
					:duration="0"
					font-size="30"
					:current="current"
					active-color="#fff"
					@change="changes"
				></custom-tabs>
			</view>
			<view class="order-list-box" v-if="dataInfo">
				<template v-if="dataInfo.list.length">
					<view class="order-list" v-for="(item, index) of dataInfo.list" :key="index" @click="routePage(item.id)">
						<view class="flex flex-align-center">
							<view style="flex: 1; padding-right: 20rpx;">
								<view class="order-time flex flex-align-center justify-space-between">
									<view style="flex: 1;">
										<view style="font-weight: bold; margin-right: 10rpx; color: #333; font-size: 32rpx;">
											<text>#</text>
											<text style="font-size: 52rpx;">{{ index + 1 }}</text>
										</view>
									</view>
									<text style="font-weight: bold;" :style="{ color: item.type == 2 ? '#18B566' : '#ff5722' }">
										{{ item.type == 2 ? '+' + item.actualMoney : item.actualMoney }}
									</text>
								</view>
								<view class="order-time flex flex-align-center justify-space-between">
									<view style="flex: 1;">
										<text>订单号：{{ item.outTradeNo }}</text>
									</view>
									<text :style="{ color: item.type == 2 ? '#999' : '#ff5722' }">{{ item.type == 2 ? '已入账' : '待入账' }}</text>
								</view>
							</view>
							<uni-icons type="forward" color="#999"></uni-icons>
						</view>
						<view class="flex flex-align-center">
							<view class="order-list-info">
								<view class="order-info-name">
									<view class="flex flex-align-center">
										<section class="spot"></section>
										<text class="order-info-name-title">{{ item.storeName }}</text>
									</view>
								</view>
								<view class="order-info-name">
									<view class="flex flex-align-center">
										<section class="spot spot-t"></section>
										<text class="order-info-name-title">{{ item.startDetail }}</text>
									</view>
									<view class="trips">{{ item.endUsername + '：' + item.endTel }}</view>
								</view>
							</view>
						</view>
					</view>
				</template>
				<custom-empty v-else></custom-empty>
			</view>
		</view>
		<withdraw-deposit ref="handClickOpen" @click="getTodayInfo"></withdraw-deposit>
	</view>
</template>

<script>
import WithdrawDeposit from './component/withdrawDeposit.vue';

export default {
	components: {
		WithdrawDeposit
	},
	data() {
		return {
			popUpShow: false,
			list: [
				{
					name: '全部'
				},
				{
					name: '待入账'
				},
				{
					name: '已入账'
				},
				{
					name: '扣款/违规'
				}
			],
			dataBarIndex: 0,
			current: 0,
			duration: 0,
			formData: null,
			timeList: [],
			paramsData: {
				day: this.$moment(new Date()).format('YYYY-MM-DD'),
				state: ''
			},
			dataInfo: null
		};
	},
	onLoad() {
		this.getTodayInfo();
		this.getDetaList();
	},
	methods: {
		routePage(id) {
			uni.navigateTo({
				url: '/pages/detailsPage/orderDetails?id=' + id
			});
		},
		// 时间切换
		dataBarClick(msg) {
			this.dataBarIndex = msg;
			this.paramsData.day = this.formData.bar[msg].hours;
			this.getDetaList();
		},
		// 类型切换
		changes(res) {
			this.current = res;
			this.paramsData.state = res == 0 ? '' : res;
			this.getDetaList();
		},
		// 打开弹出层
		handGoToGell(msg) {
			this.$nextTick(() => {
				this.$refs.handClickOpen.init(msg);
			});
		},
		// 获取金额信息
		getTodayInfo() {
			this.$request({
				url: '/rider-bill/rider-assets'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.formData = res.data;
					this.timeList = JSON.parse(JSON.stringify(res.data.bar));
					this.timeList.map(item => {
						item.hours = item.hours.substring(5, 12);
						return item;
					});
					this.dataBarIndex = res.data.bar.length - 1;
				}
			});
		},
		// 获取详细列表
		getDetaList() {
			this.$request({
				url: '/rider-bill/assets-list',
				method: 'GET',
				data: this.paramsData
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.dataInfo = res.data;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.accCors {
	display: flex;
	flex-direction: column;
	background-color: $uni-bg-color-grey;
	height: 100vh;
	overflow-x: hidden;

	.accCors-cont {
		position: relative;
		top: 0rpx;
		left: 0rpx;
		background: #ff6320;
		width: 750rpx;

		&::before {
			display: block;
			position: absolute;
			content: '';
			left: -100rpx;
			right: -100rpx;
			bottom: -150rpx;
			background: #ff6320;
			height: 200rpx;
			border-radius: 0rpx 0rpx 50% 50%;
		}

		.money-box {
			padding: 50rpx 32rpx 50rpx;
			transform: translateZ(1rpx);

			.money-box-l {
				flex: 1;

				.price {
					font-size: 80rpx;
					font-family: Source Han Sans CN;
					font-weight: bold;
					color: #ffffff;
				}

				.price-trip {
					font-size: 28rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
				}
			}
		}

		.accCors-cont-list {
			padding: 20rpx 30rpx 0;
			text-align: right;
			font-size: 30rpx;
			color: #fff;
			background: #ff6320;
			margin-top: 60rpx;

			.accCors-cont-list-router {
				display: block;
				width: 156rpx;
				text-align: center;

				.accCors-cont-list-icon {
					margin-left: 10rpx;
				}

				.bond-money {
					font-size: 24rpx;
					margin-left: 10rpx;
				}
			}
		}

		/deep/.u-cell__right-icon-wrap {
			color: #fff;
		}
		.cont-val {
			display: inline-block;
			font-size: 30rpx;
			font-family: Source Han Sans CN;
			letter-spacing: 0rpx !important;
		}
	}

	.mainCont {
		display: flex;
		flex-direction: column;
		min-height: 0rpx;
		flex: 1;
		padding: 0rpx 20rpx 32rpx;

		.mainCont-modul {
			position: relative;
			z-index: 1;
			padding: 0rpx 20rpx 40rpx;
			background: #fff;
			border-radius: 10rpx;
			background: #ffffff;

			.scroll-view-item {
				min-width: 120rpx;
				padding: 20rpx;
				text-align: center;

				.scroll-view-item-styl {
					font-size: 28rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #333333;

					&:nth-child(2) {
						font-size: $font-my-size-24;
					}
				}
			}

			.scroll-view-item-color {
				.scroll-view-item-styl {
					color: #f0ad4e;
				}
			}

			.scroll-x {
				max-width: 560rpx;
			}

			.static-data {
				position: relative;
				text-align: center;
				font-size: 28rpx;
				padding-left: 20rpx;

				&::before {
					content: '';
					position: absolute;
					height: 100%;
					top: 50%;
					width: 2rpx;
					box-shadow: -2rpx 0 10rpx 0 rgba(0, 0, 0, 0.3);
					border-radius: 50%;
					left: 0rpx;
					z-index: 1;
					transform: translateY(-50%);
				}

				&::after {
					content: '';
					position: absolute;
					left: 0rpx;
					width: 10rpx;
					top: 0rpx;
					bottom: 0rpx;
					background: #fff;
					z-index: 2;
				}
			}

			.incoMoney {
				text-align: center;
				padding: 45rpx 0 50rpx;
				font-size: 32rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333333;

				.MoneyNum {
					font-size: 80rpx;
				}
			}

			.incoMoney-two {
				position: relative;
				flex: 1;
				text-align: center;
				font-size: 26rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;

				.money-num {
					font-size: 50rpx;
					font-weight: bold;
				}

				.info-pop-up {
					position: absolute;
					top: 120rpx;
					left: 50%;
					transform: translate3d(-50%, 0, 0rpx);
					background: rgba(0, 0, 0, 0.7);
					box-shadow: -6px 8px 15px 9px #dddddd;
					width: 276rpx;
					padding: 0rpx 20rpx 20rpx;
					border-radius: 10rpx;

					&::before {
						display: block;
						margin: 0 auto;
						width: 0rpx;
						height: 0rpx;
						border-width: 10rpx;
						border-color: rgba(0, 0, 0, 0.7) transparent transparent rgba(0, 0, 0, 0.7);
						border-style: solid;
						content: '';
						transform: translateY(-10rpx) rotate(45deg);
					}

					&::after {
						position: absolute;
						display: block;
						width: 20rpx;
						height: 20rpx;
					}
				}
			}
		}

		.mainCont-modul-two {
			min-height: 0rpx;
			background: #fff;
			margin-top: 20rpx;
			overflow: hidden;
			border-radius: 10rpx;
			padding: 20rpx 32rpx;

			.mainCont-modul-two-title {
				font-size: $font-my-size-36;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333333;
				padding-bottom: 20rpx;
			}

			.mainCont-modul-box-list {
				flex: 1;
				min-height: 0rpx;
				overflow-y: scroll;
			}

			.mainCont-modul-two-list {
				padding: 0 32rpx;
			}

			.order-list-box {
				padding: 20rpx 0;

				&::after {
					content: '';
					display: block;
					height: 1rpx;
					margin: 40rpx 32rpx 0;
					background: #eee;
				}

				&:last-child {
					&::after {
						content: '';
						display: none;
					}
				}

				.order-list {
					padding: 0rpx 32rpx 20rpx;

					.list-title {
						color: #333;
						font-weight: bold;
						font-size: 30rpx;
					}
				}

				.mapPosition {
					padding: 0 32rpx;
					font-size: 26rpx;
					color: #999;
				}
			}
		}

		.order-list-box {
			padding: 0rpx 0rpx 20rpx;
			flex: 1;
			overflow-y: scroll;

			.order-list {
				background: #ffffff;
				padding: 20rpx 32rpx;
				margin-top: 20rpx;
				border-radius: 10rpx;

				.order-time {
					color: $font-my-color-9;
				}

				.order-list-info {
					flex: 1;
					margin-top: 20rpx;

					.order-info-name {
						padding-right: 20rpx;

						.order-info-name-title {
							font-size: $font-my-size-32;
							font-weight: bold;
							padding: 14rpx 0;
							color: $font-my-color-3;
						}

						.spot {
							margin-right: 10rpx;
							width: 14rpx;
							min-width: 14rpx;
							height: 14rpx;
							border-radius: 50%;
							background-color: #f9833b;
						}

						.spot-t {
							background-color: #1bb367;
						}

						.trips {
							padding-left: 24rpx;
							color: $font-my-color-9;
							font-size: $font-my-size-28;
						}
					}
				}
			}
		}
	}
}
</style>
