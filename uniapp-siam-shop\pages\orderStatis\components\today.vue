<template>
	<view class="today">
		<view class="today-box">
			<view class="today-cont">
				<view class="flex flex-align-center">
					<view class="today-cont-box">
						<view class="today-cont-money">
							<text class="money-num">{{ dataInfo.completeNum }}</text>
							<text>单</text>
						</view>
						<view class="today-cont-money">完成订单</view>
					</view>
					<view class="today-cont-box">
						<view class="today-cont-money">
							<text class="money-num">{{ dataInfo.cancelNum }}</text>
							<text>单</text>
						</view>
						<view class="today-cont-money">转单/取消单</view>
					</view>
					<view class="today-cont-box">
						<view class="today-cont-money">
							<text class="money-num">{{ dataInfo.distance }}</text>
							<text>Km</text>
						</view>
						<view class="today-cont-money">配送里程</view>
					</view>
				</view>
				<view class="today-cont-footer-text">收入相关信息请前往 “我的账户”查看</view>
			</view>
		</view>
		<view class="order-info">
			<view class="today-title">订单明细</view>
			<custom-tabs
				class="custom-u-tabs"
				ref="tabs"
				:is-scroll="true"
				:list="list"
				:show-bar="false"
				:bold="false"
				:duration="0"
				font-size="30"
				:current="current"
				active-color="#fff"
				@change="changes"
			></custom-tabs>
		</view>
		<view class="order-list-box">
			<template v-if="dataInfo.list.length">
				<view class="order-list" v-for="(item, index) of dataInfo.list" :key="index" @click="routePage(item.id)">
					<view class="order-time flex flex-align-center justify-space-between" >
						<view style="flex: 1;">
							<view style="font-weight: bold; margin-right: 10rpx; color: #333; font-size: 32rpx;">
								<text>#</text>
								<text style="font-size: 52rpx;">{{ index + 1 }}</text>
							</view>
							<text>订单号：{{ item.outTradeNo }}</text>
						</view>
						<text :style="{ color: item.state ? stateName[item.state].color : '#999',fontSize: '30rpx' }">{{ item.state ? stateName[item.state].title : "" }}</text>
						<uni-icons type="forward" color="#999"></uni-icons>
					</view>
					<view class="flex flex-align-center">
						<view class="order-list-info">
							<view class="order-info-name">
								<view class="flex flex-align-center">
									<section class="spot"></section>
									<text class="order-info-name-title">{{ item.storeName }}</text>
								</view>
							</view>
							<view class="order-info-name">
								<view class="flex flex-align-center">
									<section class="spot spot-t"></section>
									<text class="order-info-name-title">{{ item.endDetail }}</text>
								</view>
								<view class="trips">{{ item.endUsername + '：' + item.endTel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</view>
							</view>
						</view>
					</view>
				</view>
			</template>
			<custom-empty v-else></custom-empty>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			current: 0,
			dataInfo: {
				list: []
			},
			stateName:{
				2:{
					color: '#ee8131',
					title: '待取货'
				},
				4:{
					color: '#48b36b',
					title: '配送中'
				},
				5:{
					color: '#999',
					title: '已完成'
				},
				3:{
					color: '#ee8131',
					title: '转单/取消'
				},
				6:{
					color: '#ee8131',
					title: '转单/取消'
				}
			},
			list: [
				{
					name: '全部'
				},
				{
					name: '待取货'
				},
				{
					name: '配送中'
				},
				{
					name: '已完成'
				},
				{
					name: '转单/取消'
				}
			]
		};
	},
	created() {
		this.getToday('');
	},
	methods: {
		routePage(id){
			uni.navigateTo({
				url:"/pages/detailsPage/orderDetails?id=" + id
			})
		},
		changes(res) {
			this.current = res;
			if (res == 0) {
				this.getToday('');
			} else {
				this.getToday(res);
			}
		},
		getToday(msg) {
			this.$request({
				url: '/rider-bill/order-quantity',
				data: {
					state: msg
				},
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					Object.assign(this.dataInfo,res.data);
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.today {
	display: flex;
	flex-direction: column;
	padding: 0rpx 0rpx 0rpx;
	background: $uni-bg-color-grey;
	height: 100%;

	.today-box {
		background: #fff;
		padding: 40rpx 32rpx;
	}

	.today-title {
		font-size: $font-my-size-36;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		padding-bottom: 30rpx;
	}

	.today-cont {
		padding: 40rpx 40rpx 0rpx;
		background: #ff6320;
		border-radius: 10px;

		.today-cont-box {
			flex: 1;
			text-align: center;

			.today-cont-money {
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;

				.money-num {
					font-size: 52rpx;
					color: #ffffff;
				}
			}
		}

		.today-cont-footer-text {
			margin-top: 20rpx;
			padding: 20rpx 0rpx;
			text-align: center;
			font-size: 20rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			opacity: 0.5;
			border-top: 1rpx solid rgba(255, 255, 255, 0.7);
		}
	}

	.order-info {
		background: #fff;
		padding: 20rpx 32rpx;
		margin-top: 20rpx;
		box-shadow: 0 2rpx 4rpx 0 rgba(0,0,0,0.1);
		transform: translateZ(10rpx);
	}
	
	.order-list-box {
		padding: 0rpx 20rpx 20rpx;
		flex: 1;
		min-height: 0rpx;
		overflow-y: scroll;
	
		.order-list {
			background: #FFFFFF;
			padding: 20rpx 32rpx;
			margin-top: 20rpx;
			border-radius: 10rpx;
	
			.order-time {
				padding: 10rpx 0;
				color: $font-my-color-9;
			}
	
			.order-list-info {
				flex: 1;
	
				.order-info-name {
					padding-right: 20rpx;
	
					.order-info-name-title {
						font-size: $font-my-size-32;
						font-weight: bold;
						padding: 14rpx 0;
						color: $font-my-color-3;
					}
	
					.spot {
						margin-right: 10rpx;
						width: 14rpx;
						min-width: 14rpx;
						height: 14rpx;
						border-radius: 50%;
						background-color: #f9833b;
					}
	
					.spot-t {
						background-color: #1bb367;
					}
	
					.trips {
						padding-left: 24rpx;
						color: $font-my-color-9;
						font-size: $font-my-size-28;
					}
				}
			}
		}
	}
}
</style>
