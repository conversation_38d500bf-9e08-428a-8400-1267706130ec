.margin-bottom {
	position: relative;
	margin-bottom: 20rpx;
	background: #ffffff;
	overflow: hidden;
	border-radius: 20rpx 20rpx 10rpx 10rpx;
	padding-top: 20rpx;

	.contInfo-title-box {
		padding: 0 10rpx 20rpx 32rpx;

		.contInfo-title {
			flex: 1;
			padding: 0 30rpx;

			.contTitle {
				color: #333;
				font-weight: bold;
				font-size: 32rpx;

				.contTitle-label {
					font-size: 24rpx;
					color: #666;
					font-weight: 400;
					padding: 5rpx 0;
				}
			}

			.contTitle-money {
				color: #e44c4c;
				font-weight: bold;
				font-size: 32rpx;
				margin-bottom: 32rpx;
			}
		}
	}

	.line {
		background: $uni-bg-color-grey;
		height: 1rpx;
	}

	.grip {
		display: inline-block;
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		padding: 0rpx 10rpx;
		font-size: 24rpx;
		color: #ffffff;
		border-radius: 20rpx 0 20rpx 0;
		background: #ff5761;
		z-index: 0;
	}

	.togoGood {
		position: relative;
		margin-top: 30rpx;

		.togoods {
			position: relative;
			display: flex;
			align-items: flex-start;
			padding: 0 36rpx;

			&:last-child {
				margin-bottom: 0rpx;
			}

			.goodstitle {
				font-size: 22rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333;
				min-width: 100rpx;
				max-width: 100rpx;
				word-wrap: break-word;

				.goodstitle-name {
					font-weight: 400;
					color: #666;
				}
				
				.goodstitle-name-trip{
					color: #FFFFFF;
					width: 40rpx;
					height: 40rpx;
					line-height: 40rpx;
					background: #f9833b;
					border-radius: 50%;
					text-align: center;
					font-size: 22rpx;
					transform: rotate(0deg);
				}
				
				.goodstitle-name-trip-bg{
					background: #1bb367;
				}
			}

			.address {
				font-size: 32rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333;

				.address-label {
					font-size: 28rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #666666;
				}

				.tipsbox {
					padding: 18rpx 0 0rpx;

					.tipsbox-ls {
						// border: 0.5rpx solid #eee;
						box-shadow: 0 0 0 0.1rpx #eee;
						margin-right: 10rpx;
						padding: 2rpx 10rpx;
						font-size: 20rpx;
						color: #666;
						font-weight: 400;
						border-radius: 5rpx;
					}

					.tipsbox-ls-color {
						color: #dcac8a;
					}
				}
			}

			.togoGood-pic {
				position: absolute;
				right: 30rpx;
				bottom: -5rpx;
				width: 60rpx;
				height: 60rpx;
				z-index: 999;
			}
		}

		.leftLine {
			position: relative;
			top: 0rpx;
			left: 0rpx;
			padding-bottom: 50rpx;

			&::before {
				display: inline-block;
				height: 70rpx;
				position: absolute;
				top: 50%;
				left: 55rpx;
				content: '';
				width: 4rpx;
				background: #dedede;
			}
		}
	}

	.remarks {
		background: $uni-bg-color-grey;
		color: #666;
		padding: 10rpx 32rpx 10rpx;
		margin: 30rpx 32rpx 0;
		border-radius: 10rpx;
		letter-spacing: 3rpx;
		font-size: 24rpx;
	}
}
.loading-view {
	text-align: center;
	padding-bottom: 20rpx;
}
