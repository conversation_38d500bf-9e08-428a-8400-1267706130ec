<template>
	<view class="probleam">
		<view class="probleam-cont">
			<u-radio-group style="width: 100%;" v-model="formData.cancelId" @change="radioGroupChange">
				<template v-for="(item, index) of probleamList">
					<view class="pblmList flex flex-align-center justify-space-between" :key="index">
						<text>{{ item.title }}</text>
						<u-radio :name="item.id"></u-radio>
					</view>
				</template>
			</u-radio-group>
			<my-button :fiexd="true" margin-bottom="40" :disabled="!formData.cancelId" @confirm="confirm">确认</my-button>
		</view>
		<u-modal
			v-model="telShow"
			:title="config.title"
			:show-cancel-button="true"
			confirm-text="我在想想"
			:cancel-style="{ color: 'red' }"
			cancel-text="确认取消"
			:content="config.content"
			@cancel="submit"
		></u-modal>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	data() {
		return {
			telShow: false,
			formData: {
				cancelId: '',
				orderId: '',
				type: 'cancel'
			},
			probleamList: [],
			config: {
				title: '确认取消',
				content: ''
			}
		};
	},
	computed: {
		...mapState(['configInfo'])
	},
	onLoad(e) {
		this.formData.orderId = e.id;
		this.getCancelList();
	},
	methods: {
		getCancelList() {
			this.$request({
				url: '/config/notice',
				method: 'GET',
				data: {
					type: 5
				}
			}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.probleamList = res.data;
				}
			});
		},
		confirm() {
			this.config.content = `您的当前等级只有${this.configInfo.riderSet.cancelNum}次免费取消单机会，确认取消吗？`;
			this.telShow = true;
		},
		submit() {
			this.$request({
				url: '/rider-order/operate-order',
				data: this.formData
			}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.$interactive
						.ShowToast({
							title: '取消成功！'
						})
						.then(() => {
							uni.reLaunch({
								url: '/pages/index/index'
							});
						});
				} else {
					this.$interactive.ShowToast(
						{
							title: res.message
						},
						false
					);
				}
			});
		},
		radioGroupChange() {}
	}
};
</script>

<style lang="scss" scoped>
.probleam {
	display: flex;
	flex-direction: column;
	padding: 0rpx 32rpx;
	height: 100vh;
	padding: 20rpx;
	background: $uni-bg-color-grey;

	.probleam-cont {
		background: #ffffff;
		border-radius: 10rpx;
		padding: 0 32rpx;
		flex: 1;
		min-height: 0rpx;
		overflow-y: scroll;
		padding-bottom: 130rpx;

		.pblmList {
			width: 100%;
			font-size: 32rpx;
			padding: 20rpx 0;
		}
	}
}
</style>
