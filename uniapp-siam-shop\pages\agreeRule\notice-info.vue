<template>
	<view class="notice">
		<view class="notice-title">{{ title }}</view>
		<view class="notice-time" v-if="time">发布时间：{{ time }}</view>
		<u-parse :html="content"></u-parse>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: '',
			content: '',
			time: ''
		};
	},
	onLoad(e) {
		if (e.type == 4) {
			this.getNewsInfo(e.id);
			uni.setNavigationBarTitle({
				title: '消息详情'
			});
		} else {
			uni.getStorage({
				key: 'content',
				success: msg => {
					this.title = msg.data.title;
					this.time = msg.data.createdAt.substring(0, 16) || '';
					uni.setNavigationBarTitle({
						title: e.type == 1 ? '协议详情' : e.type == 2 ? '规则说明' : ''
					});
					this.content = `${msg.data.body}`;
				}
			});
		}
	},
	onUnload() {},
	methods: {
		getNewsInfo(id) {
			this.$request({
				url: '/config/notice-info',
				data: { id: id },
				method: 'GET'
			}).then(res => {
				// #ifdef MP-WEIXIN
					res = JSON.parse(res)
				// #endif
				if (res.code == 1) {
					this.title = res.data.title;
					this.time = res.data.createdAt ? this.$moment(res.data.createdAt * 1000).format('YYYY-MM-DD HH:mm') : '';
					this.content = res.data.body;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.notice {
	padding: 0rpx 32rpx;

	.notice-title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.notice-time {
		padding: 20rpx 0 60rpx;
		color: #999;
	}

	/deep/ p {
		width: 100% !important;
		max-width: 100% !important;
		white-space: pre-wrap !important;
		word-wrap: word-wrap !important;
	}

	/deep/ol,
	/deep/ul,
	/deep/div,
	/deep/a,
	/deep/h3,
	/deep/h4,
	/deep/h5,
	/deep/h6 {
		width: 100%;
		font-size: 32rpx !important;
		padding: 0rpx !important;
		margin: 0rpx !important;
		
		img{
			width: 100% !important;
			max-width: 100%;
			min-width: 0rpx;
		}

		li {
			font-size: 32rpx !important;
			outline: none;
			list-style: none;
		}
	}
}
</style>
