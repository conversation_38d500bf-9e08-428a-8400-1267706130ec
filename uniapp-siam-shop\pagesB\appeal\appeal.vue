<template>
	<view class="appeal">
		<my-nav-bar title="扣罚申诉">
			<template slot="right-cont">
				<view style="font-size: 30rpx; min-width: 150rpx;">扣罚规则</view>
			</template>
		</my-nav-bar>
		<m-tabs-swiper :list="list" :pageLength="3" tab-width="500" active-color="#ee8131">
			<template slot="swiper-item-1">
				<view>1</view>
			</template>
			<template slot="swiper-item-2">
				<view>2</view>
			</template>
			<template slot="swiper-item-3">
				<view>3</view>
			</template>
		</m-tabs-swiper>
	</view>
</template>

<script>
import MTabsSwiper from '@/components/custom-tabs-page-linkge/tab-page-linkage.vue';
export default {
	components: {
		MTabsSwiper
	},
	data() {
		return {
			list: [
				{
					name: '可申诉'
				},
				{
					name: '申诉成功'
				},
				{
					name: '已扣罚'
				}
			]
		}
	},
};
</script>

<style></style>
