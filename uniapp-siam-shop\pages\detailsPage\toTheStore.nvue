<template>
	<div class="to-the-store">
		<image class="image-pic" src="~@/static/icon/ddcg.png" mode="widthFix"></image>
		<text class="trip-info">{{ config.title }}</text>
		<button class="btn-top" type="default" color="#fff" :style="{ backgroundColor: config.topBtnBgColor }" @click="confirm"><text class="btn-top-text">{{ config.topBtnText }}</text></button>
		<button class="btn-top btn-bottom" type="default" @click="close">取消</button>
	</div>
</template>

<script>
import { mapState } from 'vuex';
export default {
	data() {
		return {
			config: {
				topBtnText: '确认到店',
				topBtnBgColor: '#48B36B',
				title: '确认到店！'
			}
		};
	},
	computed: {
		...mapState(['configInfo'])
	},
	onLoad() {
		uni.$on('getdistance', this.distance);
	},
	methods: {
		// 距离计算
		distance(res) {
			if (parseFloat(res) <= this.configInfo.arrivalDistanceLimit) {
				this.config.topBtnText = '确认到店';
				this.config.topBtnBgColor = '#48B36B';
				this.config.title = '确认到店！';
			} else {
				this.config.topBtnText = '强制到店';
				this.config.topBtnBgColor = '#fa4000';
				this.config.title = `系统检测到你离商家${(res / 1000).toFixed(2)}km,超出取货范围，确认强制到店操作吗？`;
			}
		},
		confirm() {
			const toTheStrot = uni.getSubNVueById('tothestore');
			toTheStrot.hide('zoom-in', 300);
			uni.$emit('callback');
		},
		close() {
			const toTheStrot = uni.getSubNVueById('tothestore');
			toTheStrot.hide('zoom-in', 300);
		}
	}
};
</script>

<style scoped>
.to-the-store {
	flex: 1;
	width: 600rpx;
	align-items: center;
	padding-bottom: 60rpx;
}

.image-pic {
	width: 500rpx;
}

.trip-info {
	flex: 1;
	font-size: 30rpx;
	padding-left: 40rpx;
	padding-right: 40rpx;
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}

.btn-top {
	width: 500rpx;
	height: 86rpx;
	border-radius: 50rpx;
	background-color: #ec1613;
	border-width: 0rpx;
}

.btn-top-text {
	font-size: 36rpx;
	color: #ffffff;
}

.btn-bottom {
	border-width: 1rpx;
	margin-top: 40rpx;
	background-color: transparent;
}
</style>
