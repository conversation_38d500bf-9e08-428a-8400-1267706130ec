<template>
	<view class="customer-analysis">
    <u-calendar v-model="showStartTime" mode="range" max-date="2050-10-1" @change="changeTime"></u-calendar>
		<view class="header">
			<!-- <view class="title">顾客分析</view> -->
			<view class="date-range">
				<text>{{startTime}} 至 {{endTime}} 消费顾客</text>
			</view>
			
			<view class="total-customers">
				<text class="number">{{totalCustomers}}</text>
			</view>
			
			<view class="customer-stats">
				<view class="stat-item">
					<text class="stat-number">{{newMemberCount}}</text>
					<text class="stat-label">新顾客</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{oldMemberCount}}</text>
					<text class="stat-label">回头客</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{averageConsumptionAmount}}</text>
					<text class="stat-label">平均消费</text>
				</view>
			</view>
		</view>
		
		<view class="date-selector">
			<view class="tab-group">
				<view 
					v-for="(item, index) in dateTabs" 
					:key="index" 
					class="tab-item" 
					:class="{active: currentTab === index}"
					@click="changeTab(index)"
				>
					{{item}}
				</view>
			</view>
		</view>
		
		<view class="date-range-picker" v-if="currentTab === 3">
			<view class="date-input" @click="openTimePicker">{{startTime}}</view>
			<view class="date-separator">至</view>
			<view class="date-input" @click="openTimePicker">{{endTime}}</view>
		</view>
		
		<view class="analysis-section">
			<view class="section-title">
				<view class="title-indicator"></view>
				<text>新老顾客占比</text>
			</view>
			
			<view class="chart-container">
				<!-- 顾客数量比例 -->
				<view class="ratio-item">
					<view class="ratio-label">顾客数量</view>
					<view class="ratio-bar">
						<view class="ratio-old" :style="{width: oldMemberRate + '%'}"></view>
						<view class="ratio-new" :style="{width: newMemberRate + '%'}"></view>
					</view>
					<view class="ratio-values">
						<view class="ratio-value">
							<text class="label">老顾客</text>
							<text class="value">{{oldMemberRate}}%</text>
						</view>
						<view class="ratio-value">
							<text class="label">新顾客</text>
							<text class="value">{{newMemberRate}}%</text>
						</view>
					</view>
				</view>
				
				<!-- 交易比数 -->
				<view class="ratio-item">
					<view class="ratio-label">交易比数</view>
					<view class="ratio-bar">
						<view class="ratio-old" :style="{width: oldMemberOrderRate + '%'}"></view>
						<view class="ratio-new" :style="{width: newMemberOrderRate + '%'}"></view>
					</view>
					<view class="ratio-values">
						<view class="ratio-value">
							<text class="label">老顾客</text>
							<text class="value">{{oldMemberOrderRate}}%</text>
						</view>
						<view class="ratio-value">
							<text class="label">新顾客</text>
							<text class="value">{{newMemberOrderRate}}%</text>
						</view>
					</view>
				</view>
				
				<!-- 交易金额 -->
				<view class="ratio-item">
					<view class="ratio-label">交易金额</view>
					<view class="ratio-bar">
						<view class="ratio-old" :style="{width: oldMemberAmountRate + '%'}"></view>
						<view class="ratio-new" :style="{width: newMemberAmountRate + '%'}"></view>
					</view>
					<view class="ratio-values">
						<view class="ratio-value">
							<text class="label">老顾客</text>
							<text class="value">{{oldMemberAmountRate}}%</text>
						</view>
						<view class="ratio-value">
							<text class="label">新顾客</text>
							<text class="value">{{newMemberAmountRate}}%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
  computed: {
		...mapState(['UserInfo'])
	},
	data() {
		return {
			// 日期相关
			startTime: '2025/08/10',
			endTime: '2025/08/17',
			dateTabs: ['7天', '30天', '1年', '自定义'],
			currentTab: 3, // 默认选中自定义
			
			// 顾客数据
			totalCustomers: 18,
			newMemberCount: 3,
			oldMemberCount: 15,
			averageConsumptionAmount: 1.06,
			
			// 比例数据
			oldMemberRate: 83.33,
			newMemberRate: 16.67,
			oldMemberOrderRate: 84.21,
			newMemberOrderRate: 15.79,
			oldMemberAmountRate: 81.62,
			newMemberAmountRate: 18.38,
      showStartTime: false,
		};
	},
	methods: {
		// 切换日期选项卡
		changeTab(index) {
			this.currentTab = index;
			
			// 根据选项卡设置日期范围
			const formatStr = 'yyyy/MM/DD';
			
			switch(index) {
				case 0: // 7天
					this.startTime = this.$moment().add(-7, 'day').format(formatStr);
					this.endTime = this.$moment().format(formatStr);
					break;
				case 1: // 30天
					this.startTime = this.$moment().add(-30, 'day').format(formatStr);
					this.endTime = this.$moment().format(formatStr);
					break;
				case 2: // 1年
					this.startTime = this.$moment().add(-1, 'year').format(formatStr);
					this.endTime = this.$moment().format(formatStr);
					break;
				case 3: // 自定义 - 这里保持当前日期不变
					// 自定义模式下不自动修改日期
          this.startTime = this.$moment().add(-7, 'day').format(formatStr);
					this.endTime = this.$moment().format(formatStr);
					break;
			}
			
			// 获取数据
			this.fetchCustomerData();
		},
		
		// 打开开始日期选择器
		openTimePicker() {
			if (this.currentTab !== 3) {
				// 如果不是自定义模式，先切换到自定义模式
				this.currentTab = 3;
			}
			this.showStartTime = true;
		},
    changeTime(e) {
      this.startTime = this.$u.timeFormat(new Date(e.startDate), 'yyyy/mm/dd');
      this.endTime = this.$u.timeFormat(new Date(e.endDate), 'yyyy/mm/dd');
      this.showStartTime = false;
      // 获取数据
			this.fetchCustomerData();
    },
		
		// 获取顾客数据的方法
		fetchCustomerData() {
			uni.showLoading({
				title: '加载中...'
			});
      console.log(this.UserInfo)
			
			// 构建请求参数
			const params = {
				startTime: this.startTime + ' 00:00:00',
				endTime: this.endTime + ' 23:59:59',
        merchantId: this.UserInfo.shopId
			};
			
			// 调用API获取数据
			this.$request({
        url: '/api-merchant/rest/merchant/merchantStatistics/memberAnalysis',
        data: params,
      }).then(res => {
				if (res.code === 200 && res.data) {
					const data = res.data;
					
					// 更新顾客数据
					this.totalCustomers = (data.newMemberCount + data.oldMemberCount) || 0;
					this.newMemberCount = data.newMemberCount || 0;
					this.oldMemberCount = data.oldMemberCount || 0;
					this.averageConsumptionAmount = data.averageConsumptionAmount || 0;
					
					// 更新比例数据
					this.oldMemberRate = (data.oldMemberRate || 0) * 100;
					this.newMemberRate = (data.newMemberRate || 0) * 100;
					this.oldMemberOrderRate = (data.oldMemberOrderRate || 0) * 100;
					this.newMemberOrderRate = (data.newMemberOrderRate || 0) * 100;
					this.oldMemberAmountRate = (data.oldMemberAmountRate || 0) * 100;
					this.newMemberAmountRate = (data.newMemberAmountRate || 0) * 100;
				} else {
					uni.showToast({
						title: res.message || '获取数据失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				console.error('获取顾客分析数据失败', err);
				uni.showToast({
					title: '网络异常，请稍后重试',
					icon: 'none'
				});
			}).finally(() => {
				uni.hideLoading();
			});
		}
	},
	onLoad() {
		// 页面加载时获取数据
		this.fetchCustomerData();
	}
};
</script>

<style lang="scss" scoped>
.customer-analysis {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #f5f5f5;
	
	.header {
		background-color: #FF6320;
		padding: 20rpx 32rpx 40rpx;
		color: #fff;
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}
		
		.date-range {
			font-size: 28rpx;
			margin-bottom: 16rpx;
		}
		
		.total-customers {
			text-align: center;
			margin-bottom: 16rpx;
			
			.number {
				font-size: 100rpx;
				font-weight: bold;
			}
		}
		
		.customer-stats {
			display: flex;
			justify-content: space-around;
			text-align: center;
			
			.stat-item {
				display: flex;
				flex-direction: column;
				
				.stat-number {
					font-size: 48rpx;
					font-weight: bold;
					margin-bottom: 10rpx;
				}
				
				.stat-label {
					font-size: 28rpx;
				}
			}
		}
	}
	
	.date-selector {
		background-color: #7878FF;
		padding: 20rpx 0;
		
		.tab-group {
			display: flex;
			justify-content: space-around;
			
			.tab-item {
				padding: 10rpx 30rpx;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.8);
				border-radius: 30rpx;
				
				&.active {
					background-color: #fff;
					color: #7878FF;
					font-weight: bold;
				}
			}
		}
	}
	
	.date-range-picker {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx;
		background-color: #fff;
		
		.date-input {
			padding: 10rpx 20rpx;
			border: 1px solid #ddd;
			border-radius: 8rpx;
			font-size: 28rpx;
		}
		
		.date-separator {
			margin: 0 20rpx;
			color: #666;
		}
	}
	
	.analysis-section {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 30rpx;
		
		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;
			
			.title-indicator {
				width: 8rpx;
				height: 32rpx;
				background-color: #FF6320;
				margin-right: 20rpx;
			}
			
			text {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
		}
		
		.chart-container {
			.ratio-item {
				margin-bottom: 40rpx;
				
				.ratio-label {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 20rpx;
          font-weight: 700;
				}
				
				.ratio-bar {
					height: 40rpx;
					display: flex;
					overflow: hidden;
					border-radius: 20rpx;
					margin-bottom: 20rpx;
					
					.ratio-old {
						background-color: #3B7FF3;
						height: 100%;
					}
					
					.ratio-new {
						background-color: #5ECDF9;
						height: 100%;
					}
				}
				
				.ratio-values {
					display: flex;
					justify-content: space-between;
					
					.ratio-value {
						display: flex;
						align-items: center;
						
						.label {
							font-size: 28rpx;
							color: #666;
							margin-right: 10rpx;
						}
						
						.value {
							font-size: 28rpx;
							color: #333;
							font-weight: bold;
						}
					}
				}
			}
		}
	}
}
</style>