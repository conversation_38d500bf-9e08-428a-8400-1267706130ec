<template>
  <view class="content">
    
    <view class="content-box">
      <!-- 搜索框 -->
      <!-- <view class="search-box">
        <view class="search-input">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <input type="text" v-model="searchKeyword" placeholder="搜索商品名称" @confirm="searchGoods" />
        </view>
        <view class="search-btn" @click="searchGoods">搜索</view>
      </view> -->
      
      <!-- 商品列表 -->
      <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }"
      :up="{ auto: false, empty: {tip: '暂无商品数据' } }" :fixed="false">
        <view class="goods-list">
          <view class="goods-item" v-for="(item, index) in goodsList" :key="index">
            <view class="goods-info">
              <view class="goods-image-wrapper">
                <u-image width="100%" height="100%" :src="getImageUrl(item.mainImage)"></u-image>
              </view>
              <view class="goods-detail">
                <view class="goods-name">{{ item.name }}</view>
                <view class="goods-category">{{ item.categoryName }}</view>
                <view class="goods-price">¥{{ item.price }}</view>
              </view>
            </view>
            <view class="goods-action">
              <u-checkbox v-model="item.checked" @change="checkboxChange(item)"></u-checkbox>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="selected-count">已选择 {{ selectedGoods.length }} 件商品</view>
      <view class="save-btn" @click="saveRelation">保存关联</view>
    </view>
  </view>
</template>

<script>
import { getImageUrl } from '@/utils'
export default {
  data() {
    return {
      getImageUrl,
      couponId: '',
      couponName: '',
      searchKeyword: '',
      goodsList: [],
      selectedGoods: [],
      alreadyLinkedGoodsIds: [] // 已关联的商品ID列表
    }
  },
  onLoad(options) {
    if (options.id) {
      this.couponId = options.id;
    }
    if (options.name) {
      this.couponName = decodeURIComponent(options.name);
    }
    
    // 加载已关联的商品
    this.loadLinkedGoods();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 初始化mescroll对象
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
    },
    
    // 下拉刷新
    downCallback() {
      this.mescroll.resetUpScroll();
    },
    
    // 上拉加载更多
    upCallback(e) {
      this.getGoodsList(e.num);
    },
    
    // 搜索商品
    searchGoods() {
      this.refresh();
    },
    
    // 刷新列表
    refresh() {
      if (this.mescroll) {
        this.mescroll.triggerDownScroll();
      }
    },
  
    
    // 加载已关联的商品
    async loadLinkedGoods() {
      try {
        uni.showLoading({ title: '加载中...' });
        
        const res = await this.$request({
          url: '/api-promotion/rest/merchant/coupons/selectById',
          method: 'POST',
          data: {
            id: this.couponId,
            // pageNo: 1,
            // pageSize: 1000
          }
        });
        
        if (res.code === 200 && res.data && res.data.goodsList) {
          // 保存已关联商品的ID列表
          this.alreadyLinkedGoodsIds = res.data.goodsList.map(item => item.id);
          this.selectedGoods = res.data.goodsList;
        }
      } catch (error) {
        console.error('加载已关联商品失败:', error);
        uni.showToast({ title: '加载已关联商品失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 获取商品列表
    async getGoodsList(page = 1) {
      try {
        const res = await this.$request({
          url: '/api-goods/rest/merchant/goods/list',
          method: 'POST',
          data: {"pageNo":1,"pageSize":20,"isDelete":0,"source":1}
        });
        
        if (res.code === 200) {
          let newGoods = res.data.records || [];
          
          // 标记已选中的商品
          newGoods = newGoods.map(item => {
            return {
              ...item,
              checked: this.alreadyLinkedGoodsIds.includes(item.id)
            };
          });
          
          if (page === 1) {
            this.goodsList = newGoods;
          } else {
            this.goodsList = [...this.goodsList, ...newGoods];
          }
          this.goodsList.forEach(i => {
            i.checked = this.selectedGoods.some(g => g.id === i.id);
          })
          
          this.mescroll.endSuccess(newGoods.length, newGoods.length >= 10);
        } else {
          this.mescroll.endSuccess(0, false);
          uni.showToast({ title: res.message || '获取商品列表失败', icon: 'none' });
        }
      } catch (error) {
        console.error('获取商品列表失败:', error);
        this.mescroll.endErr();
        uni.showToast({ title: '获取商品列表失败', icon: 'none' });
      }
    },
    
    // 复选框变化
    checkboxChange(item) {
      // 检查商品是否已在选中列表中
      const isSelected = this.selectedGoods.some(g => g.id == item.id);

      // 如果已在列表中，则移除；否则添加
      if (isSelected) {
        // 从已选择列表中移除
        this.selectedGoods = this.selectedGoods.filter(g => g.id != item.id);
        // 确保 UI 状态同步
        item.checked = false;
      } else {
        // 添加到已选择列表
        this.selectedGoods.push({
          id: item.id,
          goodsId: item.id,
          goodsName: item.name,
          couponId: this.couponId
        });
        // 确保 UI 状态同步
        item.checked = true;
      }
      console.log('选中商品数量:', this.selectedGoods.length)
      // debugger
    },
    
    // 保存关联
    async saveRelation() {
      if (this.selectedGoods.length === 0) {
        uni.showToast({ title: '请至少选择一个商品', icon: 'none' });
        return;
      }
      
      try {
        uni.showLoading({ title: '保存中...' });
        console.log(this.selectedGoods)
        
        // 再添加新的关联
        const res = await this.$request({
          url: '/api-promotion/rest/merchant/couponsGoodsRelation/insert',
          method: 'POST',
          data: {
            couponsId: this.couponId,
            goodsIdListStr: JSON.stringify(this.selectedGoods.map(i => i.id))
          }
        });
        
        if (res.code === 200) {
          uni.showToast({ title: '保存成功', icon: 'success' });
          // 设置标记，返回列表页时刷新数据
          uni.setStorageSync('couponsListNeedRefresh', true);
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({ title: res.message || '保存失败', icon: 'none' });
        }
      } catch (error) {
        console.error('保存关联失败:', error);
        uni.showToast({ title: '保存失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  width: 750rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-x: hidden;
  background-color: $uni-bg-color-grey;
}

.content-box {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  padding-bottom: 100rpx; /* 为底部操作栏留出空间 */
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  position: fixed;
  top: 88rpx; /* 导航栏高度 */
  left: 0;
  right: 0;
  z-index: 10;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
}

.search-input input {
  flex: 1;
  height: 60rpx;
  margin-left: 10rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  background-color: #fa7c25;
  color: #fff;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.goods-list {
  padding: 20rpx;
}

.goods-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.goods-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.goods-image-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.goods-detail {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-category {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.goods-price {
  font-size: 28rpx;
  color: #fa7c25;
  font-weight: bold;
}

.goods-action {
  padding-left: 20rpx;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.selected-count {
  font-size: 28rpx;
  color: #333;
}

.save-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: #fa7c25;
  color: #fff;
  border-radius: 35rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
}
</style>