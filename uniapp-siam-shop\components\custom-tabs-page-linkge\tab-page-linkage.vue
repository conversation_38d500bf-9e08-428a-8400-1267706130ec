<template>
	<view class="tab-page-linkage" :style="'height:' + windowheight + 'rpx' ">
		<uni-status-bar v-if="statusBarShow"></uni-status-bar>
		<view class="flex flex-align-center">
			<view class="left-icon" v-if="headIconShow" :style="{ background: BoxBackground }"
				@click="$common.handBack">
				<uni-icons :type="headLeftIcon" size="38"></uni-icons>
			</view>
			<view class="tabsStyl" :style="'width:' + style.width + ';' + 'background:' + style.background + ';'">
				<tabs-swiper ref="tabSwiper" :bar-height="barHeight" :duration="300" :bar-style="barStyle"
					:active-color="activeColor" :list="list" :is-scroll="isScroll" :font-size="fontSize"
					:current="current" :bold="bold" :offset="offset" :bg-color="BoxBackground"
					:inactive-color="inactiveColor" :active-item-style="activeItemStyle" :barWidth="barWidth"
					:barHeight="barHeight" :box-padding="boxPadding" @change="change"></tabs-swiper>
			</view>
			<view class="left-icon left-icon-r" v-if="headIconShow" :style="{ background: BoxBackground }"
				@click="_callback">
				<uni-icons :type="headRightIcon" size="38"></uni-icons>
			</view>
		</view>
		<MyLine v-if="showLine" :height="lineHeight" :background="lineBackground"></MyLine>
		<slot></slot>
		<swiper ref="swiperPage" class="swiper-page" style="flex: 1; max-height: 100%; width: 750rpx;" :duration="300"
			:current="swiperCurrent" @animationfinish="animationfinish" @transition="transition">

			<!--  #ifdef APP-PLUS -->
			<template v-for="(item, index) of new Array(list.length)">
				<swiper-item style="height: 100%; max-height: 100%;" :key="index">
					<slot :name="'swiper-item-' + (index + 1)"></slot>
				</swiper-item>
			</template>
			<!--  #endif -->
			<!-- #ifndef APP-PLUS -->
			<block v-for="(item, index) of list.length">
				<swiper-item style="height: 100%; max-height: 100%;" :key="index">
					<slot :name="'swiper-item-' + (index + 1)"></slot>
				</swiper-item>
			</block>
			<!-- #endif -->
		</swiper>
	</view>
</template>

<script>
	import TabsSwiper from './u-tabs-swiper/custom-tabs-swiper.vue';

	export default {
		components: {
			TabsSwiper
		},
		props: {
			list: Array,
			statusBarShow: {
				type: Boolean,
				default: false
			},
			headLeftIcon: {
				type: String,
				default: 'arrowleft'
			},
			headRightIcon: {
				type: String,
				default: ''
			},
			headIconShow: {
				type: Boolean,
				default: false
			},
			tabWidth: {
				type: [String, Number],
				default: '100%'
			},
			inactiveColor: {
				type: String,
				default: '#333'
			},
			activeColor: {
				type: String,
				default: '#ec1613'
			},
			fontSize: {
				type: [String, Number],
				default: 30
			},
			isScroll: {
				type: Boolean,
				default: false
			},
			showLine: {
				type: Boolean,
				default: true
			},
			lineHeight: {
				type: [String, Number],
				default: 1
			},
			lineBackground: {
				type: String,
				default: '#f8f8f8'
			},
			activeItemStyle: {
				type: Object,
				default: () => {
					return {};
				}
			},
			bold: {
				type: Boolean,
				default: false
			},
			offset: {
				type: Array,
				default: () => {
					return [5, 20];
				}
			},
			// 菜单底部移动的bar的宽度，单位rpx
			barWidth: {
				type: [Number, String],
				default: 40
			},
			// 移动bar的高度
			barHeight: {
				type: [Number, String],
				default: 6
			},
			barStyle: {
				type: Object,
				default: () => {
					return {};
				}
			},
			// #ifdef MP-WEIXIN
			boxBackground: {
				type: String,
				default: '#fff'
			},
			// #endif
			// #ifndef MP-WEIXIN
			BoxBackground: {
				type: String,
				default: '#fff'
			},
			// #endif
			boxPadding: {
				type: Array,
				default: () => {
					return [0, 0, 0, 0];
				}
			}
		},
		computed: {
			style() {
				const obj = {
					width: this.tabWidth.lastIndexOf('%') >= 0 ? this.tabWidth : this.tabWidth + 'rpx',
					// #ifdef MP-WEIXIN
					background: this.boxBackground
					// #endif
					// #ifndef MP-WEIXIN
					background: this.BoxBackground
					// #endif
				};
				return obj
			}
		},
		created() {
			uni.$on('changePage', this.change);
			uni.getSystemInfo({
				success: (res) => {
					this.windowheight = 2 * res.windowHeight
				}
			})
		},
		data() {
			return {
				scrollTop: 0,
				current: 0,
				swiperCurrent: 0,
				triggered: false,
				freshing: 0,
				freshingShow: true,
				windowheight: 0
			};
		},
		watch: {
			current() {
				this.$emit('changePage', this.current);
			}
		},
		methods: {
			// tab栏切换
			change(index) {
				this.swiperCurrent = index;
			},
			// 滚动每一步的回调
			transition(msg) {
				this.$refs.tabSwiper.setDx(msg.detail.dx);
			},
			// 全屏滚动结束
			animationfinish(msg) {
				this.$refs.tabSwiper.setFinishCurrent(msg.detail.current);
				this.swiperCurrent = msg.detail.current;
				this.current = msg.detail.current;
			},
			_callback() {
				this.$emit('click');
			}
		}
	};
</script>

<style lang="scss" scoped>
	/*  #ifdef  MP-WEIXIN  */
	.tab-page-linkage {
		flex: 1;
		display: flex;
		width: 100%;
		flex-direction: column;

		.left-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-start;
			height: 100%;
			min-height: 80rpx;
			width: 160rpx;
			padding-left: 28rpx;
		}

		.left-icon-r {
			align-items: flex-end;
			padding: 0rpx;
			padding-right: 28rpx;
		}

		.tabsStyl {
			flex: 1;
			min-width: 0rpx;
			margin: 0 auto;
		}

		.loading-view {
			text-align: center;
			padding-bottom: 20rpx;
		}
	}

	/*  #endif  */
	/*  #ifndef  MP-WEIXIN  */
	.tab-page-linkage {
		flex: 1;
		display: flex;
		width: 100%;
		flex-direction: column;
		max-height: 100%;
		height: 100%;

		.left-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-start;
			height: 100%;
			min-height: 80rpx;
			width: 160rpx;
			padding-left: 28rpx;
		}

		.left-icon-r {
			align-items: flex-end;
			padding: 0rpx;
			padding-right: 28rpx;
		}

		.tabsStyl {
			flex: 1;
			min-width: 0rpx;
			margin: 0 auto;
		}

		.loading-view {
			text-align: center;
			padding-bottom: 20rpx;
		}
	}

	/*  #endif  */
</style>
