<template>
	<view class="stepbar">
		<view class="flex flex-align-center">
			<view class="stepbar-cont stepbar-top-cont"></view>
			<view class="stepbar-cont stepbar-top-cont">￥150</view>
			<view class="stepbar-cont stepbar-top-cont">￥450</view>
			<view class="stepbar-cont stepbar-top-cont">￥840</view>
		</view>
		<view class="stepbar-line flex flex-align-center">
			<view class="stepbar-cont-spot"></view>
			<view class="stepbar-cont-spot"><view class="stepbar-spot"></view></view>
			<view class="stepbar-cont-spot"><view class="stepbar-spot"></view></view>
			<view class="stepbar-cont-spot"><view class="stepbar-spot"></view></view>
		</view>
		<view class="flex flex-align-center">
			<view class="stepbar-cont stepbar-bottom-cont">0单</view>
			<view class="stepbar-cont stepbar-bottom-cont">50单</view>
			<view class="stepbar-cont stepbar-bottom-cont">105单</view>
			<view class="stepbar-cont stepbar-bottom-cont">165单</view>
		</view>
	</view>
</template>

<script></script>

<style lang="scss" scoped>
.stepbar {
	margin-top: 70rpx;

	.stepbar-cont {
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		flex: 1;
		font-family: Source Han Sans CN;
		font-weight: 400;
	}
	.stepbar-top-cont {
		font-size: 24rpx;
		color: #666666;
	}
	.stepbar-bottom-cont {
		font-size: 28rpx;
		color: #333333;
	}
	.stepbar-line {
		height: 8rpx;
		background: #eeeeee;
		margin: 20rpx 0;

		.stepbar-cont-spot {
			height: 100%;
			position: relative;
			text-align: center;
			flex: 1;

			.stepbar-spot {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				display: inline-block;
				width: 20rpx;
				height: 20rpx;
				background: #eeeeee;
				border-radius: 50%;

				&::after {
					display: inline-block;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 8rpx;
					height: 8rpx;
					background: #ffffff;
					border-radius: 50%;
					content: '';
				}
			}
		}
	}
}
</style>
