<template>
	<view class="login">
		<uni-status-bar></uni-status-bar>
		<view class="login-box">
			<image class="login-pic" src="../../static/image/logotwo.png" mode="widthFix"></image>
			<custom-tabs-swiper :list="loginOptions" tab-width="400" font-size="34" :show-line="false" inactive-color="#666"
													bar-height="4" bar-width="54">
				<view class="ml1t">
					<text>欢迎登录</text>
				</view>
				<template slot="swiper-item-1">
					<view class="login-one">
						<view class="phone">
							<uni-easyinput class="phone-input" type="text" :inputBorder="false" v-model="formData.username"
														 placeholder="请输入账号/手机号"></uni-easyinput>
						</view>
						<view class="phone">
							<uni-easyinput class="phone-input" type="password" :inputBorder="false" v-model="formData.password"
														 placeholder="请输入密码"></uni-easyinput>
						</view>
						<my-button margin-top="60" height="86" width="626" :bold="true" color="#fff" font-size="28"
											 :disabled="false" @confirm="loginByUserNamePhoneAndPassword">
							登录
						</my-button>
						<my-button margin-top="60" height="86" width="626" :bold="false" color="rgb(236, 22, 19)" font-size="28" background="#fff0" border borderColor="rgb(236, 22, 19)"
											 :disabled="disShow" @confirm="register">
							注册
						</my-button>
					</view>
				</template>
				<template slot="swiper-item-2">
					<view class="login-one">
						<view class="phone">
							<uni-easyinput class="phone-input" type="text" :inputBorder="false" v-model="formData.mobile"
														 placeholder="请输入手机号"></uni-easyinput>
						</view>
						<view class="phone verification-code">
							<uni-easyinput class="phone-input verification-item" type="text" :inputBorder="false" v-model="formData.mobileCode"
														 placeholder="验证码"></uni-easyinput>
							<my-button class="" width="240" height="80" :bold="false" color="#fff" :disabled="sendVerificationDisabled" font-size="25"
												 @confirm="sendVerificationCode">
								{{sendVerificationDisabled ? timerCount + 's后可发送' : '发送验证码'}}
							</my-button>
						</view>
						<my-button margin-top="60" width="626" height="86" :bold="true" color="#fff" :disabled="false" font-size="34"
											 @confirm="verification('login')">
							登录
						</my-button>

						<my-button margin-top="60" height="86" width="626" :bold="false" color="rgb(236, 22, 19)" font-size="28" background="#fff0" border borderColor="rgb(236, 22, 19)"
											 :disabled="disShow" @confirm="register">
							注册
						</my-button>
					</view>
				</template>
			</custom-tabs-swiper>

			<!--	todo: login by phone OTP -->
			<!-- 	<view class="footer-cont-1 flex flex-direction-column flex-align-center justify-space-center"
					@click="verification('wxLogin')">
					<image style="width: 50rpx;height: 50rpx;margin-bottom: 10rpx;" src='/static/wxlogo.png'></image>
					<view>微信登录</view>
				</view> -->

			<!-- <my-button margin-top="60" height="86" width="626" :bold="false" color="#ccc" font-size="25"
								 :disabled="disShow" @confirm="register">
				注册
			</my-button> -->

			<view class="footer-cont">
				<view class="tirp-info" style="margin-bottom: 100rpx;">
					<view class="flex flex-align-center justify-space-center">
						<u-checkbox-group @change="changeRadio($event, 'agreementShow')" size="36">
							<u-checkbox v-model="agreementShow" icon-size="22" name="1">
								<text class="tirp-info-text">请先阅读</text>
								<text class="tirp-info-color "
											@click.stop="getAgreement('registrationAgreement')">《易达脉联平台注册协议》
								</text>
							</u-checkbox>
						</u-checkbox-group>
					</view>
					<view class="flex flex-align-center justify-space-center">
						<u-checkbox-group @change="changeRadio($event, 'agreementShowOne')" size="36">
							<u-checkbox v-model="agreementShowOne" icon-size="22" name="1">
								<text class="tirp-info-text">和</text>
								<text class="tirp-info-color"
											@click.stop="getAgreement('management')">《易达脉联平台服务管理规定》
								</text>
							</u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</view>
		</view>
		<!-- 		<uni-popup ref="RunDomainNameShow" type="center" height="500" border-radius="20">
			<view class="RunDomainNameShow">
				<view class="phone flex flex-align-center">
					<text class="phone-title">域名</text>
					<uni-easyinput class="phone-input" type="text" :inputBorder="false" clear-size="30"
						v-model="openDomNameObj.domain_url" placeholder="请输入数据域名,例:xxx.com"></uni-easyinput>
				</view>
				<view class="phone flex flex-align-center">
					<text class="phone-title">平台ID</text>
					<uni-easyinput class="phone-input" type="number" :inputBorder="false" maxlength="8" clear-size="30"
						v-model="systemIdObj.systemId" placeholder="请输入8位数平台ID"></uni-easyinput>
				</view>
				<view class="trip-text">备注：首次登录或注册账号需要先绑定数据域名和平台ID，获取方式请联系您的商户经理！</view>
				<my-button margin-top="60" height="92" :bold="true" color="#fff" font-size="34"
					:disabled="!openDomNameObj.domain_url || !systemIdObj.systemId" @confirm="bindingDomainName">
					立即绑定
				</my-button>
			</view>
		</uni-popup> -->
	</view>
</template>

<script>
import {mapActions, mapMutations} from 'vuex';
import CustomTabsSwiper from '../../components/custom-tabs-page-linkge/tab-page-linkage.vue';
import {base64ToPath} from "../../common/image-tools"; // 全屏联动
import encodeDecode from "@/utils/encodeDecode/index.js"

export default {
	components: {
		CustomTabsSwiper
	},
	data() {
		return {
			show: false,
			agreementShow: false,
			agreementShowOne: false,
			RunDomainNameShow: false,
			agreement: '',
			disShow: false,
			openDomName: false,
			loginOptions: [
				{name: '账号登录'},
				{name: '短信登录'}
			],
			tel: '',
			formData: {
				username: '',
				password: '',
				mobile: '',
				mobileCode: '',
				registrationId: ''
			},
			systemId: '',
			openDomNameObj: {
				domain_url: '',
				auth_type: 2
			},
			systemIdObj: {
				systemId: ''
			},
			openDomNameObjRuls: {
				domain_url: '请输入域名！',
				systemId: '请输入平台ID！'
			},
			account: uni.getStorageSync('account') || {},
			timerCount: 0,
			timer: null,
		};
	},
	computed: {
		sendVerificationDisabled() {
			return this.timerCount > 0;
		}
	},
	onLoad() {
		this.timerCount = 0;
		this.formData.registrationId = uni.getStorageSync('registrationID') || '';
	},
	methods: {
		...mapActions(["getLoginInfo"]),
		...mapMutations(['setAccount']),
		// funFilter(type, msg) {
		// 	const RunDomainName = uni.getStorageSync('RunDomainName');
		// 	this.$validate.isEmpty(RunDomainName).then(flag => {
		// 		if (flag) {
		// 			if (RunDomainName.systemId) {
		// 				this.RunDomainNameShow = false;
		// 			} else {
		// 				this.RunDomainNameShow = true;
		// 			}
		// 		} else {
		// 			this.RunDomainNameShow = true;
		// 			setTimeout(() => {
		// 				this.$refs.RunDomainNameShow.open();
		// 			}, 1500);
		// 		}
		// 	});
		// },
		register() {
			console.log('register is clicked');
			uni.navigateTo({url: '/pages/login/register'});
		},

		// 发送验证码
		sendVerificationCode() {
			if (!this.formData.mobile) {
				this.$interactive.ShowToast({title: '手机号未填写'}, false);
				return;
			}
			const data = {
				mobile: this.formData.mobile,
				type: "verification"
			};
			this.$request({url: '/api-util/rest/smsLog/sendMobileCode', data}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				this.$interactive.ShowToast({title: res.message}, false);

				// count down
				this.countdown(60);
			}).catch(err => {
				console.log(err);
			});
		},

		// count down
		countdown(count) {
			this.timerCount = count;
			this.timer = setInterval(() => {
				if (this.timerCount > 0) {
					this.timerCount--
				} else {
					clearInterval(this.timer)
					this.timer = null
				}
			}, 1000)
		},

		// 验证
		verification(key) {
			// if (this.RunDomainNameShow) {
			// 	this.$refs.RunDomainNameShow.open();
			// 	return;
			// }

			// for local test
			this.agreementShow = true;
			this.agreementShowOne = true;

			if (!this.agreementShow || !this.agreementShowOne) {
				this.$interactive.ShowToast({title: '请先阅读协议'}, false);
				return;
			}

			if (this.account.openid) {
				console.log('verification if..........')

				// #ifdef MP-WEIXIN
				uni.getUserProfile({
					desc: '用于获取用户头像', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: res => {
						let userInfo = res.userInfo
						if (key === 'wxLogin') {
							this.getUserProfile(userInfo)
						} else if (key === 'login') {
							this.getLoginInfo({loginType: 1}).then(res => {
								const account = res.data[0]
								this.login(account)
							})
						}
					},
					fail: e => {
						this.loading = false
						console.error('getUserProfile failed, ', e)
						this.$interactive.ShowToast({title: '登陆失败！'}, false);
					}
				})
				// #endif

				// #ifdef APP-PLUS
				this.login()
				// #endif
			} else {
				console.log('verification else..........')
				if (key === 'wxLogin') {
					this.getUserProfile()
				} else if (key === 'login') {
					this.login()
				}
			}
		},
		// 账号密码登录
		login(account) {
			console.log("login function")
			// todo: for test only
			// this.formData = {
			// 	mobile: '***********',
			// 	mobileCode: '111111',
			// 	registrationId: ''
			// }

			if (!this.formData.mobile) {
				this.$interactive.ShowToast({title: '手机号未填写'}, false);
			}
			if (!this.formData.mobileCode) {
				this.$interactive.ShowToast({title: '验证码未填写'}, false);
			}

			this.$request({url: '/api-merchant/rest/merchant/loginByMobile', data: this.formData}).then(res => {
				console.log(res)
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code === 200) {
					// token
					this.setAccount({
						authorization: res.data.token
					})

					uni.$emit('RefreshUserInfo');
					uni.$emit('IsOpenGps');
					this.$interactive.ShowToast({title: '登录成功！'}).then(() => {
						if (res.data.auditStatus == 1 || res.data.auditStatus == 2) {
              uni.reLaunch({url: '/pages/index/index'});
            }
            else {
              uni.reLaunch({url: '/pages/myInfo/shopInfo'});
            }
					});
				} else {
					this.$interactive.ShowToast({title: res.message}, false);
				}
			}).catch(err => {
				console.log(err);
			});

			/*// for local test only
			this.setAccount({ userId: 'userIdGjx', userName: 'userNameGjx', openid: 'openid-gjx' });
			uni.$emit('RefreshUserInfo');
			uni.$emit('IsOpenGps');
			this.$interactive.ShowToast({ title: '登录成功！' }).then(() => {
				uni.reLaunch({ url: '/pages/index/index' });
			});*/
		},

		// login by user name / phone number + passcode
		loginByUserNamePhoneAndPassword() {
			if (!this.formData.username) {
				this.$interactive.ShowToast({title: '手机号/用户名未填写'}, false);
			}
			if (!this.formData.password) {
				this.$interactive.ShowToast({title: '密码未填写'}, false);
			}

			const password = encodeDecode.wxStrToBase64(this.formData.password);
			this.$request({url: '/api-merchant/rest/merchant/login', data: {
        ...this.formData,
        password
      }}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSON.parse(res)
				// #endif
				if (res.code === 200) {
					// token
					this.setAccount({
						authorization: res.data.token
					})

					uni.$emit('RefreshUserInfo');
					uni.$emit('IsOpenGps');
					this.$interactive.ShowToast({title: '登录成功！'}).then(() => {
						if (res.data.auditStatus == 1 || res.data.auditStatus == 2) {
              uni.reLaunch({url: '/pages/index/index'});
            }
            else {
              uni.reLaunch({url: '/pages/myInfo/shopInfo'});
            }
					});
				} else {
					this.$interactive.ShowToast({title: res.message}, false);
				}
			}).catch(err => {
				console.log(err);
			});
		},

		//微信登录
		// wxLogin() {
		// 	console.log('++++++++++++++++++')
		// 	if (!this.account.openid) {
		// 		uni.getUserProfile({
		// 			desc: '用于获取用户头像', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
		// 			success: res => {
		// 				let userInfo = res.userInfo
		// 				this.getUserProfile(userInfo)
		// 			},
		// 			fail: e => {
		// 				this.$interactive.ShowToast({
		// 						title: '登陆失败！'
		// 					},
		// 					false
		// 				);
		// 			}
		// 		})
		// 	} else {
		// 		this.getUserProfile()
		// 	}
		// },
		async saveUser(userInfo, account) {
			await this.$request({
				url: '/login/save-user',
				data: {
					userName: userInfo.nickName,
					portrait: userInfo.avatarUrl,
					id: account.id
				}
			})
			this.setAccount({
				nickName: userInfo.nickName,
				portrait: userInfo.avatarUrl
			})
		},
		async getUserProfile(userInfo) {
			await this.getLoginInfo({
				loginType: 2
			}).then(res => {
				//上传用户昵称和头像
				if (userInfo) {
					this.saveUser(userInfo, res)
				}
				if (res.code == 1) {
					const account = res.data[0]
					// if (res.data.length > 1) {
					// 	// 绑定多平台 TODO
					// 	this.$interactive.ShowToast({
					// 			title: '暂不支持绑定多个平台'
					// 		},
					// 		false
					// 	);
					// 	return
					// } else if (res.data.length === 0) {
					// 	this.$interactive.ShowToast({
					// 			title: '该用户未绑定任何门店，请先联系管理员绑定'
					// 		},
					// 		false
					// 	);
					// 	return
					// }
					console.log('登陆成功', res)
					this.setAccount({
						//这是门店账号ID
						userId: account.id,
						userName: account.userName,
						// storeId: account.storeId,
						openid: account.openid
					})
					uni.$emit('RefreshUserInfo');
					this.$interactive
							.ShowToast({title: '登录成功！'})
							.then(() => {
								if (res.data.auditStatus == 1 || res.data.auditStatus == 2) {
                  uni.reLaunch({url: '/pages/index/index'});
                }
                else {
                  uni.reLaunch({url: '/pages/myInfo/shopInfo'});
                }
							});
				} else {
					this.$interactive.ShowToast({title: res.message}, false);
				}
			}).catch(e => {
			})
		},

		// 验证码登录 todo:
		loginCode() {
			if (this.tel.length === 11) {
				this.$request({
					url: '/login/get-code',
					data: {
						tel: this.tel,
						type: 2
					},
					IsGetStorg: false
				}).then(result => {
					// #ifdef MP-WEIXIN
					const res = JSON.parse(result)
					// #endif
					if (res.code == 1) {
						uni.navigateTo({
							url: '/pages/login/register-code?type=2&PageType=1&tel=' + this.tel
						});
					} else {
						this.$interactive.ShowToast({
							title: res.message
						}, false);
					}
				});
			} else {
				uni.showToast({
					icon: 'none',
					title: '请输入正确的手机号！'
				});
			}
		},

		// 绑定域名接口
		// bindingDomainName() {
		// 	const obj = {
		// 		systemId: this.systemIdObj.systemId,
		// 		domain_url: this.openDomNameObj.domain_url
		// 	};
		// 	this.$interactive.formIsEmpty(obj, this.openDomNameObjRuls).then(flag => {
		// 		if (flag) {
		// 			this.$request({
		// 				url: 'https://s.y-bei.cn/cloud/auth/checkDomain',
		// 				data: this.openDomNameObj,
		// 				IsGetStorg: false,
		// 				IsApiType: false
		// 			}).then(res => {
		// 				// #ifdef MP-WEIXIN
		// 				res = JSON.parse(res)
		// 				// #endif
		// 				if (res.code === 1) {
		// 					const obj = {
		// 						isDev: res.isDev,
		// 						RunDomainName: this.openDomNameObj.domain_url
		// 					};
		// 					uni.setStorage({
		// 						key: 'RunDomainName',
		// 						data: obj,
		// 						success: () => {
		// 							this.submitSystemId();
		// 						}
		// 					});
		// 				} else {
		// 					this.$interactive.ShowToast({
		// 						title: res.message
		// 					}, false);
		// 				}
		// 			});
		// 		}
		// 	});
		// },
		// 绑定平台ID
		// submitSystemId() {
		// 	return this.$request({
		// 		url: '/login/check-auth',
		// 		data: this.systemIdObj,
		// 		contentType: false,
		// 		IsGetStorg: false
		// 	}).then(item => {
		// 		// #ifdef MP-WEIXIN
		// 		item = JSON.parse(item)
		// 		// #endif
		// 		if (item.code == 1) {
		// 			console.log(item)
		// 			const RunDomainName = uni.getStorageSync('RunDomainName');
		// 			RunDomainName.systemId = this.systemIdObj.systemId;
		// 			RunDomainName.uniacid = item.data.uniacid;
		// 			uni.setStorage({
		// 				key: 'RunDomainName',
		// 				data: RunDomainName,
		// 				success: () => {
		// 					this.$interactive
		// 						.ShowToast({
		// 							title: '绑定成功！'
		// 						})
		// 						.then(() => {
		// 							this.RunDomainNameShow = false;
		// 							this.$refs.RunDomainNameShow.close();
		// 						});
		// 				}
		// 			});
		// 		} else {
		// 			this.$interactive.ShowToast({
		// 				title: item.msg
		// 			}, false);
		// 		}
		// 	});
		// },
		// // 下一页
		// navigatorPage(msg) {
		// 	if (this.RunDomainNameShow) {
		// 		this.$refs.RunDomainNameShow.open();
		// 		return;
		// 	} else {
		// 		if (msg == 1) {
		// 			uni.navigateTo({
		// 				url: '/pages/login/register'
		// 			});
		// 		} else if (msg == 2) {
		// 			uni.navigateTo({
		// 				url: '/pages/login/forget-passWord'
		// 			});
		// 		}
		// 	}
		// },
		// 勾选框数据变化
		changeRadio(msg, key) {
			if (msg.length > 0) {
				this[key] = true;
			} else {
				this[key] = false;
			}
		},
		// 获取协议信息
		getAgreement(msg) {
			// if (this.RunDomainNameShow) {
			// 	this.$refs.RunDomainNameShow.open();
			// 	return;
			// }
			uni.navigateTo({
				url: '/pages/login/agreement?agreementKey=' + msg
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.ml1t {
	display: flex;
	justify-content: center;
	margin-top: 100rpx;
	font-weight: bold;
	font-size: 60rpx;
}

.login {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #fff;

	/deep/ .tab-page-linkage {
		height: 800rpx !important;
	}

	.login-box {
		flex: 1;
		height: 100%;
		display: flex;
		flex-direction: column;

		.login-pic {
			display: block;
			margin: 0 auto 50rpx;
			width: 120rpx;
			height: 120rpx;
		}

		.login-one {
			padding-top: 60rpx;

			.verification-code {
				display: flex;
				.verification-item {
					flex: 1
				}
			}

			.phone {
				margin: 30rpx 62rpx;
				border-bottom: 1rpx solid $uni-bg-color-grey;

				.phone-input {
					padding: 20rpx 0;

					/deep/ .uni-input-input {
						font-size: 32rpx;
					}
				}

				.phone-input-code {
					padding: 32rpx 0;
				}
			}
		}

		.footer-cont {
			position: fixed;
			bottom: 0;
			left: 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100%;

			.footer-cont-1 {
				color: $font-my-color-6;
				margin-bottom: 30rpx;
			}

			.tirp-info {
				text-align: center;

				.tirp-info-text {
					display: inline-block;
					font-size: $font-my-size-28;
					color: $font-my-color-3;
					margin-bottom: 10rpx;
				}

				.tirp-info-color {
					color: #03a9f4;
					font-size: $font-my-size-28;
				}

				/deep/ .u-checkbox__icon-wrap {
					border-radius: 50% !important;
				}
			}
		}
	}

	.mask-box {
		position: fixed;
		top: 0rpx;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		background: rgba(0, 0, 0, 0.5);
		z-index: 998;
	}

	.RunDomainNameShow {
		background-color: #ffffff;
		padding: 20rpx 32rpx 60rpx;
		border-radius: 16rpx;
		max-width: 660rpx;

		.title-name {
			font-size: $font-my-size-40;
			font-weight: bold;
			padding: 20rpx 0rpx;
		}

		.phone {
			margin: 0 8rpx;
			border-bottom: 1rpx solid $uni-bg-color-grey;

			.phone-title {
				font-size: 34rpx;
				font-weight: bold;
				width: 108rpx;
			}

			.phone-input {
				padding: 20rpx 0;
				flex: 1;
			}

			/deep/ .uni-input-input {
				font-size: 32rpx;
			}

			/deep/ .uni-input-placeholder {
				color: #999;
				font-size: 30rpx;
			}

			/deep/ .uni-icons {
				font-size: 40rpx !important;
			}

			.phone-input-code {
				padding: 32rpx 0;
			}
		}

		.trip-text {
			padding: 20rpx 0 0;
			color: $font-my-color-9;
			font-size: 24rpx;
			margin-bottom: 30rpx;
		}
	}

	.RunDomainNameShow-hg {
		opacity: 1;
		transition: all 0.3s ease;
		transform: translateY(0%);
	}
}
</style>
