<template>
	<view class="setting">
		<view class="setting-cont">
			<navigator url="/pages/setting/account-set" hover-class="none">
				<view class="setting-cont-list line flex flex-align-center">
					<view class="setting-cont-list-title flex flex-direction-column flex-align-start"><text>账号安全</text>
					</view>
					<view class="setting-cont-list-icon">
						<uni-icons type="forward"></uni-icons>
					</view>
				</view>
			</navigator>
		</view>
		<view class="setting-cont">
			<!-- <view class="setting-cont-list line flex flex-align-center">
				<view class="setting-cont-list-title flex flex-direction-column flex-align-start"><text>意见收集</text></view>
				<view class="setting-cont-list-icon"><uni-icons type="forward"></uni-icons></view>
			</view> -->
<!-- 			<view class="setting-cont-list line flex flex-align-center" @click="appUpData">
				<view class="setting-cont-list-title flex flex-direction-column flex-align-start"><text>软件更新</text>
				</view>
				<view class="setting-cont-list-icon">
					<uni-icons type="forward"></uni-icons>
				</view>
			</view> -->
			<view class="setting-cont-list line">
				<navigator class="flex flex-align-center" url="/pages/setting/aboutWe" hover-class="none">
					<view class="setting-cont-list-title">
						<text>关于我们</text>
					</view>
					<view class="setting-cont-list-icon">
						<uni-icons type="forward"></uni-icons>
					</view>
				</navigator>
			</view>
		</view>
		<view class="setting-cont">
			<view class="setting-cont-list">
				<my-button background="transparent" color="#333" :bold="true" @confirm="showTwo = true">清除本地缓存
				</my-button>
			</view>
		</view>
		<view class="setting-cont">
			<view class="setting-cont-list">
				<my-button background="transparent" color="#F94B08" :bold="true" @confirm="logOutShow">退出登录</my-button>
			</view>
		</view>
		<u-modal v-model="show" title="退出登录" :show-cancel-button="true" confirm-text="确认退出" confirm-color="#f40000"
			content="确认退出当前登录!" @confirm="logOut"></u-modal>
		<u-modal v-model="showTwo" title="清除缓存" :show-cancel-button="true" confirm-text="确认清除" confirm-color="#f40000"
			content="确认清除当前缓存!" @confirm="delStorg"></u-modal>
<!-- 		<zy-upg ref="ZyUpg" :autocheckupdate="false"></zy-upg> -->
	</view>
</template>

<script>
	import {
		mapMutations
	} from 'vuex';
	export default {
		components: {
			// ZyUpg
		},
		data() {
			return {
				show: false,
				showTwo: false,
				checked: false
			};
		},
		methods: {
			...mapMutations(['delAccount']),
			// appUpData() {
			// 	uni.showLoading({
			// 		title: "检测中...",
			// 		mask: true
			// 	})
			// 	this.$refs.ZyUpg.check_update(true);
			// 	setTimeout(()=>{
			// 		uni.hideLoading()
			// 	},500)
			// },
			logOutShow() {
				this.show = true
			},
			logOut() {
				uni.showLoading()
				this.delAccount()
				this.$interactive.ShowToast({
					title: '退出成功！'
					}).then(res => {
						uni.hideLoading()
						uni.reLaunch({
							url: '/pages/login/login'
						});
					})
				
			},
			delStorg() {
				this.delAccount()
				this.$interactive.ShowToast({
					title: '清除成功，请重新登录！'
				}).then(() => {
					uni.reLaunch({
						url: "/pages/login/login"
					})
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.setting {
		background-color: $uni-bg-color-grey;
		height: 100vh;

		.setting-cont {
			padding-top: 20rpx;

			.setting-cont-list {
				background-color: $uni-bg-color;
				padding: 30rpx 32rpx 31rpx;

				.setting-cont-list-title {
					font-size: $font-my-size-32;
					color: $font-my-color-3;
					font-weight: 400;
					flex: 1;

					.title-label {
						font-size: $font-my-size-24;
					}
				}

				.setting-cont-list-icon {
					width: 150rpx;
					text-align: right;

					/deep/.uni-switch-input-checked {
						background: #f94b08 !important;
						border-color: #f94b08 !important;
					}
				}
			}

			.line {
				position: relative;
				top: 0rpx;
				left: 0rpx;

				&::after {
					position: absolute;
					left: 32rpx;
					right: 32rpx;
					bottom: 0rpx;
					height: 1rpx;
					background: $uni-bg-color-grey;
					content: '';
				}
			}
		}
	}
</style>
