<template>
	<view class="bill-l">
		<!--	for future use	-->
<!--		<u-dropdown style="background: #FFFFFF;">-->
<!--			<u-dropdown-item v-model="value1" :title="value1Info" :options="options1" @change="selecetType"></u-dropdown-item>-->
<!--			<u-dropdown-item v-model="value2" :title="value2Info" :options="options2" @change="selecetPayType"></u-dropdown-item>-->
<!--		</u-dropdown>-->
		<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false, empty: {tip: '无更多数据' } }">
			<view class="bill-cont">
				<view class="bill-cont-list">
					<!--				<view class="list-title">-->
					<!--					<view class="list-title-time">2021-01-14</view>-->
					<!--					<text class="list-title-label">收入￥0.00支出￥10.00</text>-->
					<!--				</view>-->
					<template v-for="(item, index) in riderBillList">
						<view class="list-cont" :key="index">
							<view class="cont-title flex flex-align-center justify-space-between">
								<text class="cont-title-tirp">{{typeToStr(item.type)}}</text>
								<text>{{ item.createTime && item.createTime.substring(2, item.createTime.length - 3) || '' }}</text>
								<text class="cont-title-money">{{coinTypeToStr(item.coinType)}}{{operateTypeToStr(item.operateType)}}{{item.number}}</text>
							</view>
							<view class="cont-label flex flex-align-center justify-space-between">
								<text>派单号:{{item.orderId || ''}}</text>
								<text>服务费:{{ item.serviceFee || ''}}</text>
							</view>
							<view class="cont-label flex flex-align-center justify-space-between">
								<text class="cont-title-tirp">备注:{{item.message || ''}}</text>
							</view>
							<my-line></my-line>
						</view>
					</template>
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
import JSONbig from 'json-bigint';
export default {
	data() {
		return {
			noMoreDataflag: false,
			key: '',
			value1: 1,
			value1Info: '全部类型',
			value2: 1,
			value2Info: '到账情况',
			options1: [
				{
					label: '默认排序',
					value: 1
				},
				{
					label: '距离优先',
					value: 2
				},
				{
					label: '价格优先',
					value: 3
				}
			],
			options2: [
				{
					label: '全部',
					value: 1
				},
				{
					label: '已到账',
					value: 2
				},
				{
					label: '未到账',
					value: 3
				}
			],
			riderBillList: []
		};
	},
	onLoad() {

	},
	methods:{
		mescrollInit(mescroll) {
		  this.mescroll = mescroll;
		},
		// 下拉回调函数
		downCallback(e) {
			this.mescroll.resetUpScroll();
		},
		// 上拉加载更多
		upCallback(e) {
			this.getRiderBillList(e.num)
		},
		getRiderBillList(page = 1) {
			this.$request({
                url: '/api-merchant/rest/merchant/merchantBillingRecord/list',
				data: {
					pageNo: page,
					pageSize: 10,
					coinType: 1
				}
			}).then(res => {
				// #ifdef MP-WEIXIN
				res = JSONbig.parse(res)
				// #endif
				if (res.code === 200) {
					const data = res.data;
					this.riderBillList = data.records;
					if (page === 1) {
						this.riderBillList = res.data.records;
					} else {
						this.riderBillList = this.riderBillList.concat(res.data.records);
					}
					if (this.riderBillList && this.riderBillList.length >= res.data.total) {
						this.noMoreDataflag = true;
					} else {
						this.noMoreDataflag = false;
					}
					this.mescroll.endSuccess(res.data.total, res.data.total >= 10 ? true : true);
				} else {
					this.mescroll.endSuccess(0, false);
					this.$interactive.ShowToast(
							{
								title: res.message
							},
							false
					);
				}
			});
		},
		/**
		 * 账单类型 1=外卖订单 2=商家提现 3=商家提现失败退回 4=活动奖励
		 */
		typeToStr(type) {
			let str = null;
			switch (type) {
				case 1:
					str = '外卖订单';
					break;
				case 2:
					str = '商家提现';
					break;
				case 3:
					str = '商家提现失败退回';
					break;
				case 4:
					str = '活动奖励';
					break;
                case 5:
                    str = '用户一分钟内取消订单';
                    break;
                case 6:
                    str = '用户申请退款';
                    break;
				default:
					str = '';
			}
			return str;
		},
		operateTypeToStr(operateType) {
			let str = null;
			switch (operateType) {
				case 1:
					str = '+';
					break;
				case 2:
					str = '-';
					break;
				default:
			}
			return str;
		},
		coinTypeToStr(coinType) {
			let str = null;
			switch (coinType) {
				case 1:
					str = '余额';
					break;
				case 2:
					str = '积分';
					break;
				default: '';
			}
			return str;
		},
		selecetType(e){
			this.value1Info = this.options1[e - 1].label
		},
		selecetPayType(e){
			this.value2Info = this.options2[e - 1].label
		}
	}
};
</script>

<style lang="scss" scoped>
.bill-l {
	height: 100%;
	background: $uni-bg-color-grey;

	.bill-cont {
		margin-bottom: 300rpx;

		.bill-cont-list {
			.list-title {
				padding: 20rpx 32rpx;

				font-size: 28rpx;

				.list-title-time {
					color: #333;
				}
				.list-title-label {
					color: #999;
					font-size: 24rpx;
				}
			}
			.list-cont {
				padding: 20rpx 32rpx;
				background: #ffffff;
				.cont-title {
					font-size: 32rpx;
					font-weight: bold;
					.cont-title-tirp {
						color: #333;
					}
					.cont-title-money {
						color: #f0ad4e;
					}
				}
				.cont-label {
					font-size: 28rpx;
					padding: 10rpx 0;
					color: #999;
				}
			}
		}
	}
}
</style>
