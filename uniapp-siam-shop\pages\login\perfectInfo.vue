<template>
	<view class="perfectInfo">
		<view class="register-title">完善个人资料</view>
		<view class="register-title-tips">请完善个人资料，请如实的填写下方相关信息</view>
		<view class="phone-box">
			<view class="phone flex flex-align-center">
				<text class="label-title">头像</text>
				<view class="addHeadPic" @click="addHeadPic" v-if="!imgSrc">
					<uni-icons class="addHeadPic-icon" type="plusempty"></uni-icons>
				</view>
				<image class="addHeadPic" :src="imgSrc" @click="addHeadPic" mode="widthFix" v-else></image>
			</view>
			<view class="phone flex flex-align-center">
				<text class="label-title">性别：</text>
				<u-radio-group class="studyTrain-footer-btn" v-model="fromData.sex">
					<u-radio name="1">男</u-radio>
					<u-radio name="2">女</u-radio>
				</u-radio-group>
			</view>
			<view class="phone flex flex-align-center">
				<text class="label-title">年龄：</text>
				<uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="fromData.age"
					placeholder="请输入您的真实年龄"></uni-easyinput>
			</view>
			<view class="phone flex flex-align-center">
				<text class="label-title">紧急联系人姓名：</text>
				<uni-easyinput class="phone-input" type="number" :inputBorder="false" v-model="fromData.urgentUsername"
					placeholder="紧急联系人姓名"></uni-easyinput>
			</view>
			<view class="phone flex flex-align-center">
				<text class="label-title">紧急联系人电话：</text>
				<uni-easyinput class="phone-input" type="number" :inputBorder="false" maxlength="11"
					v-model="fromData.urgentTel" placeholder="紧急联系人电话"></uni-easyinput>
			</view>
			<view class="phone flex flex-align-center justify-space-between" @click="openMap">
				<text class="label-title">家庭地址：</text>
				<text class="phone-input">{{ fromData.address ? fromData.address : '请选择您的家庭地址' }}</text>
				<uni-icons type="forward"></uni-icons>
			</view>
		</view>
		<my-button margin-top="60" height="92" :bold="true" color="#fff" font-size="32" @confirm="submitData">提交认证并审核
		</my-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				fromData: {
					headImg: '',
					sex: '1',
					age: '',
					urgentUsername: '',
					urgentTel: '',
					address: ''
				},
				imgSrc: '',
				paramsData: {},
				ruls: {
					headImg: '请先上传您的头像',
					age: '请输入您的年龄',
					urgentUsername: '请输入紧急联络人姓名',
					urgentTel: '请输入紧急联络人电话',
					address: '请输入详细地址'
				}
			};
		},
		onLoad(e) {
			Object.assign(this.fromData, JSON.parse(e.data));
		},
		methods: {
			openMap() {
				uni.chooseLocation({
					success: (res) => {
						this.fromData.address = res.address + '.' + res.name
					}
				})
			},
			// 上传头像
			addHeadPic() {
				this.$common
					.file_select({
						sourceType: ['album', 'camera']
					})
					.then(res => {
						this.imgSrc = res.toString();
						this.$common.UpLoadFile(res).then(uploadRes => {
							console.log(uploadRes)
							if (uploadRes && uploadRes.length >= 1) {
								this.fromData.headImg = uploadRes[0];
							} else {
								this.$interactive.ShowToast({ title: '上传失败' }, false );
							}
						}).catch(error => {
							this.$interactive.ShowToast({ title: '上传失败' }, false );
						});
					});
			},
			// 表单提交
			submitData() {
				Object.assign(this.fromData, this.paramsData);
				this.$interactive.formIsEmpty(this.fromData, this.ruls).then(flag => {
					if (flag) {
						this.$request({
							url: '/api-merchant/rest/merchant/fillVerificationInfo',
							data: this.fromData
						}).then(res => {
							res = JSON.parse(res)
							if (res.code === 200) {
								uni.$emit('RefreshUserInfo');
								this.$interactive
									.ShowToast({
										title: '提交审核成功！'
									})
									.then(() => {
										uni.navigateTo({
											url: '/pages/index/index'
										});
									});
							} else {
								this.$interactive.ShowToast({
										title: res.message
									},
									false
								);
							}
						});
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.perfectInfo {
		padding: 0 62rpx;

		.register-title {
			font-size: $font-my-size-30;
			font-weight: bold;
			padding: 40rpx 0rpx 0;
		}

		.register-title-tips {
			font-size: $font-my-size-28;
			color: $font-my-color-6;
			padding-top: 15rpx;
		}

		.phone-box {
			padding-top: 40rpx;

			.phone {
				padding: 20rpx 0;
				border-bottom: 1rpx solid $uni-bg-color-grey;

				.label-title {
					font-size: $font-my-size-32;
					color: $font-my-color-3;
					text-align: justify;
					min-width: 200rpx;
				}

				.phone-input {
					padding: 0rpx 0;
					flex: 1;
				}

				.addHeadPic {
					position: relative;
					border: 1rpx solid #999;
					width: 120rpx;
					height: 120rpx;
					border-radius: 10rpx;
					text-align: center;

					.addHeadPic-icon {
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
					}
				}
			}
		}
	}
</style>
