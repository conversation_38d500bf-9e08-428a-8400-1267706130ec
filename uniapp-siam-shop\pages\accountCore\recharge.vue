<template>
	<view class="recharge">
		<my-nav-bar title="充值"></my-nav-bar>
		<view class="recharge-box">
			<view class="recharge-cont">
				<view class="flex flex-align-start">
					<!-- <my-icon class="my-money-icon" size="34" color="#999">&#xe733;</my-icon> -->
					<view class="my-money">
						<view class="my-money-text">当前金额：<text style="font-size: 30rpx;">0</text></view>
						<input class="recharge-cont-money" type="text" placeholder="为确保顺利接单,建议充值到100元以上" placeholder-class="pal-styl"/>
					</view>
				</view>
				<my-line :margin="[0,0,10]"></my-line>
				<view class="recharge-money">已选择充值金额{{ moneyList[indexShow] }}</view>
				<view class="flex flex-align-center flex-wrap justify-space-between">
					<template v-for="(item,index) of moneyList">
						<view class="money-list" :class="{ 'money-list-bg': indexShow == index }" @click="changer(index)" :key="index">
							<text>{{ item }}</text>
						</view>
					</template>
				</view>
			</view>
			<my-button background="#262C3C" color="#fff" border-radius="5" width="" height="96">充值</my-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			moneyList: [100,200,300,400,500,600,700,800,900],
			indexShow: 0
		}
	},
	methods: {
		changer(msg){
			this.indexShow = msg;
		}
	}
};
</script>

<style lang="scss" scoped>
.recharge {
	background-color: $uni-bg-color-grey;
	
	.recharge-box{
		background: #FFFFFF;
		padding: 0rpx 40rpx 0;
	}
	
	.recharge-cont {
		padding-bottom: 90rpx;
		
		.my-money-icon {
			margin-right: 10rpx;
			margin-top: 6rpx;
		}

		.my-money {
			flex: 1;
			font-size: $font-my-size-32;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: $font-my-color-3;

			.my-money-text {
				color: $font-my-color-3;
			}
		}

		.recharge-cont-money {
			font-size: $font-my-size-30;
			padding: 10rpx 0 30rpx;
			font-weight: 400;
			flex: 1;
			
			.pal-styl{
				font-size: $font-my-size-24;
				font-weight: 400;
			}
		}
		
		.recharge-money{
			color: $font-my-color-3;
			text-align: center;
			font-size: $font-my-size-32;
			padding: 40rpx 0rpx 30rpx;
		}
		
		.money-list{
			background: #E9F2FB;
			width: 32.5%;
			text-align: center;
			padding: 45rpx 0;
			margin-bottom: 1.5%;
			border-radius: 5rpx;
			font-size: $font-my-size-30;
			font-weight: bold;
		}
		
		.money-list-bg{
			background: #4981CB;
			color: #FFFFFF;
		}
	}
}
</style>
