<template>
	<view class="modify-password">
		<view class="register-title">设置新密码</view>
		<view class="register-title-tips">最少8位字符，包含字母、数字、符号中的任意两项</view>
		<view class="phone-box">
			<view class="phone">
				<uni-easyinput class="phone-input" type="password" :inputBorder="false" v-model="password1" placeholder="请输入新密码"></uni-easyinput>
			</view>
			<view class="phone">
				<uni-easyinput class="phone-input" type="password" :inputBorder="false" v-model="password2" placeholder="请再次输入新密码"></uni-easyinput>
			</view>
		</view>
		<my-button margin-top="60" height="92" :bold="true" color="#fff" :disabled="!(password1 && password2)" font-size="40" @confirm="submitData">完成</my-button>
	</view>
</template>

<script>
export default {
	data() {
		return {
			password1: '',
			password2: '',
			formData: {
				password: '',
				tel: ''
			},
			callback: ''
		};
	},
	onLoad(e) {
		this.callback = e.callback;
		this.formData.tel = e.tel;
		if (e.callback == 'log') {
			uni.setNavigationBarTitle({
				title: '身份验证(3/3)'
			});
		} else if (e.callback == 'set') {
			uni.setNavigationBarTitle({
				title: '修改密码'
			});
		}
	},
	methods: {
		submitData() {
			if (this.password1 == this.password2) {
				this.formData.password = this.password2;
				this.$request({
					url: '/login/forget-password',
					data: this.formData,
					IsGetStorg: false
				}).then(res => {
					res = JSON.parse(res)
					if (res.code === 1) {
						uni.removeStorageSync('RunKey');
						this.$interactive
							.ShowToast({
								title: '修改成功,请重新登录！'
							})
							.then(() => {
								uni.reLaunch({
									url: '/pages/login/login'
								});
							});
					} else {
						this.$interactive.ShowToast(
							{
								title: res.message
							},
							false
						);
					}
				});
			} else {
				this.$interactive.ShowToast({
					title: '两次密码不一致！'
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.modify-password {
	padding: 0 62rpx;

	.register-title {
		font-size: $font-my-size-30;
		font-weight: bold;
		padding: 40rpx 0rpx 0;
	}
	.register-title-tips {
		font-size: $font-my-size-22;
		color: $font-my-color-6;
		padding-top: 30rpx;
	}

	.phone-box {
		padding-top: 40rpx;

		.phone {
			border-bottom: 1rpx solid $uni-bg-color-grey;

			.phone-input {
				padding: 20rpx 0;
			}
		}
	}
}
</style>
