<template>
	<view class="active-details-punching">
		<u-navbar title-size="36" :border-bottom="false" title="活动详情"></u-navbar>
		<image class="activeBanner" mode="scaleToFill" src="~@/static/image/<EMAIL>"></image>
		<u-cell-group :border="false">
			<u-cell-item title="首单奖" value="13小时后结束" :border-bottom="false" :arrow="false" :title-style="titleStyle" :value-style="rightStyle"></u-cell-item>
		</u-cell-group>
		<view class="firstOrder-box">
			<view class="flex flex-align-center">
				<view class="firstOrder-cont">
					<view class="firstOrder-num">
						<text class="num-styl">0</text>
						<text>单</text>
					</view>
					<view class="firstOrder-type">已完成</view>
				</view>
				<view class="firstOrder-cont">
					<view class="firstOrder-num">
						<text class="num-styl">0</text>
						<text>元</text>
					</view>
					<view class="firstOrder-type">已完成</view>
				</view>
			</view>
		</view>
		<view style="padding: 0 40rpx;"><step-bar></step-bar></view>
		<view class="punching-condition">
			<view class="punching-condition-title">奖励条件</view>
			<view class="punching-condition-cont">下单时间：00:00:00 - 23:59:59</view>
			<view class="punching-condition-cont">接单区域：取水楼众包商圈</view>
			<view class="punching-condition-cont">订单品类：水产</view>
			<view class="punching-condition-cont">距离区间：>=0km</view>
		</view>
		<view class="footer-list">
			<view class="title-name">活动说明</view>
			<view class="footer-list-cont">
				<text class="footer-list-cont-key">活动时间</text>
				<text class="footer-list-cont-value">2020.10.23~2020.10.23</text>
			</view>
			<view class="footer-list-cont">
				<text class="footer-list-cont-key">业务类型</text>
				<text class="footer-list-cont-value">急送B/急送C</text>
			</view>
			<view class="footer-list-cont">
				<text class="footer-list-cont-key">物流服务</text>
				<text class="footer-list-cont-value">全部</text>
			</view>
			<view class="footer-list-intro">
				<text class="footer-list-intro-styl">1.活动统计单量：运单接单时间在活动时间段内，且同时符合活动要求的订单类型的有效完成单才会计入活动单量;</text>
				<text class="footer-list-intro-styl">2.出勤天数:某日至少有一个接单时间在活动时间段内的完成单记为当日出勤;</text>
				<text class="footer-list-intro-styl">3.运单无效条件:若某运单命中了运单无效条件，将从活动单量中剔除;无效条件包含:订单超时、违规、客诉等;</text>
				<text class="footer-list-intro-styl">4.若活动规则有多个条件，需同时满足全部条件才可获得活动奖励;</text>
				<text class="footer-list-intro-styl">5.各项奖励不会累计获得;励预计在活动结束后2天内到账。</text>
			</view>
		</view>
	</view>
</template>

<script>
import StepBar from './compoents/stepBar.vue';
export default {
	components: {
		StepBar
	},
	data() {
		return {
			titleStyle: {
				color: '#333',
				fontSize: '32rpx',
				fontWeight: 'bold'
			},
			rightStyle: {
				color: '#F24E0E',
				fontSize: '32rpx'
			}
		};
	}
};
</script>

<style lang="scss" scoped>
.active-details-punching {

	.activeBanner {
		width: 750rpx;
		height: 480rpx;
		min-height: 480rpx;
	}

	.scrollY {
		flex: auto;
		overflow-y: scroll;
	}

	.firstOrder-box {
		padding: 65rpx 0 0rpx;
		border-top: 1rpx solid #eeeeee;

		.firstOrder-cont {
			flex: 1;
			text-align: center;

			.firstOrder-num {
				font-size: 28rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;

				.num-styl {
					font-size: 46rpx;
				}
			}

			.firstOrder-type {
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #999999;
			}
		}
	}

	.punching-condition {
		margin: 60rpx 70rpx 0rpx;
		padding: 20rpx 36rpx;
		background: #f7f7f7;
		border-radius: 10rpx;
		
		.punching-condition-title{
			height: 48rpx;
			line-height: 48rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #333333;
		}
		
		.punching-condition-cont{
			font-size: 24rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #666666;
		}
	}

	.footer-list {
		padding: 50rpx 40rpx 40rpx;

		.title-name {
			font-size: 32rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #333333;
			margin-bottom: 40rpx;
		}

		.footer-list-cont {
			padding: 10rpx 0rpx;

			.footer-list-cont-key {
				font-size: 28rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				margin-right: 40rpx;
			}

			.footer-list-cont-value {
				font-size: 28rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;
			}
		}

		.footer-list-intro {
			margin-top: 60rpx;

			.footer-list-intro-styl {
				display: block;
				font-size: 28rpx;
				line-height: 48rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #333333;
				line-height: 36px;
			}
		}
	}
}
</style>
