<template>
  <view class="fund-detail-container">
    <!-- 顶部标签切换 -->
    <!-- <view class="tab-container">
      <view class="tab-item" :class="{ active: activeTab === 'account' }" @click="switchTab('account')">
        <text>店铺账单</text>
      </view>
      <view class="tab-item" :class="{ active: activeTab === 'finance' }" @click="switchTab('finance')">
        <text>店铺财务</text>
      </view>
    </view> -->

    <u-calendar v-model="showTime" mode="date" @change="changeTime"></u-calendar>
    <u-select v-model="showSelect" :list="typeList" @confirm="changeSelect"></u-select>

    <!-- 日期和类型筛选 -->
    <view class="filter-container">
      <view class="date-filter" @click="showTime = true">
        <text>{{ selectedDate }}</text>
        <u-icon name="arrow-down" size="28" color="#666"></u-icon>
      </view>
      <view class="type-filter" @click="showSelect = true">
        <text>{{ selectedName    }}</text>
        <u-icon name="arrow-down" size="28" color="#666"></u-icon>
      </view>
    </view>

    <!-- 收支统计 -->
    <view class="summary-container">
      <view class="summary-item">
        <text class="label">支出 ¥</text>
        <text class="value expense">{{ expense }}</text>
      </view>
      <view class="summary-item">
        <text class="label">收入 ¥</text>
        <text class="value income">{{ income }}</text>
      </view>
    </view>

    <!-- 资金明细列表 -->
    <view class="mescroll-container">
      <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :fixed="false"
        :down="{ auto: true }" :up="{ auto: false, empty: { tip: '暂无资金明细数据' } }">
        <view class="detail-list">
          <view class="detail-item" v-for="(item, index) in detailList" :key="index">
            <view class="item-left">
              <view class="icon-wrapper" :class="item.operateType === 1 ? 'income-bg' : 'expense-bg'">
                <text class="icon-text">{{ item.operateType === 1 ? '收入' : '支出' }}</text>
              </view>
            </view>
            <view class="item-middle">
              <view class="item-title">{{ typeList.find(i => i.value === item.type).label }}</view>
              <view class="item-time">{{ item.createTime }}</view>
              <view class="item-order" v-if="item.message">{{ item.message }}</view>
            </view>
            <view class="item-right">
              <text :class="item.operateType === 1 ? 'income-text' : 'expense-text'">{{ item.operateType === 1 ? '+' :
                '-' }}{{ item.number }}</text>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
  computed: {
    ...mapState(['UserInfo'])
  },
  data () {
    return {
      typeList: [
        { "label": "用户下单", "value": 1 },
        { "label": "商家提现", "value": 2 },
        { "label": "商家提现失败退回", "value": 3 },
        { "label": "自取订单改为配送，收入减少", "value": 4 },
        { "label": "自取订单改为配送，收入增加", "value": 5 },
        { "label": "商家自配送-配送费收入", "value": 6 },
        { "label": "用户一分钟内取消订单", "value": 7 },
        { "label": "用户一分钟内取消订单-配送费退回", "value": 8 },
        { "label": "用户申请退款", "value": 9 },
        { "label": "用户申请退款-配送费退回", "value": 10 }
      ],
      mescroll: null, // mescroll实例对象
      activeTab: 'account', // 当前激活的标签：account-店铺账单，finance-店铺财务
      selectedDate: new Date(), // 选中的日期
      selectedType: '', // 选中的类型
      selectedName: '种类',
      expense: 0, // 支出金额
      income: 0, // 收入金额
      detailList: [],
      page: 1, // 当前页码
      pageSize: 10, // 每页数据条数
      formatStr: 'yyyy/MM/DD',
      showTime: false,
      showSelect: false
    };
  },
  onLoad () {
    // 页面加载时获取资金明细数据
    // 由mescroll自动触发
    this.selectedDate = this.$moment().format(this.formatStr);;
  },
  onShow () {
    // 检查是否需要刷新列表
    const needRefresh = uni.getStorageSync('fundDetailNeedRefresh');
    if (needRefresh) {
      // 清除标记
      uni.removeStorageSync('fundDetailNeedRefresh');
      // 刷新列表
      this.refresh();
    }
  },
  methods: {
    // 初始化mescroll对象
    mescrollInit (mescroll) {
      this.mescroll = mescroll;
    },

    // 主动触发下拉刷新
    refresh () {
      if (this.mescroll) {
        this.mescroll.triggerDownScroll();
      }
    },
    changeTime(e) {
      this.selectedDate = this.$u.timeFormat(new Date(e.result), 'yyyy/mm/dd');
      this.showTime = false;
      // 获取数据
			this.refresh();
    },
		changeSelect(e) {
      this.selectedType = e[0].value;
      this.selectedName = e[0].label;
       // 获取数据
			this.refresh();
    },

    // 切换标签
    switchTab (tab) {
      this.activeTab = tab;
      this.refresh();
    },

    // 下拉刷新的回调
    downCallback () {
      // 重置分页参数
      this.page = 1;
      // 重置列表数据
      this.detailList = [];
      // 重置上拉加载状态
      this.mescroll.resetUpScroll();
    },

    // 上拉加载的回调
    upCallback (page) {
      // 获取下一页数据
      this.fetchFundDetail(page.num);
    },

    // 获取资金明细数据
    async fetchFundDetail (page = 1) {
      // 这里应该调用接口获取数据
      uni.showLoading({
        title: '加载中'
      });

      const res = await this.$request({
        url: '/api-merchant/rest/merchant/merchantStatistics/billingRecordDetail',
        method: 'POST',
        data: {
          merchantId: this.UserInfo.shopId,
          startTime: this.selectedDate + ' 00:00:00',
          endTime: this.selectedDate + ' 23:59:59',
          typeList: this.selectedType ? [this.selectedType] : [],
          pageNo: page,
          pageSize: 10
        }
      });


      if (res.code === 200) {
        // 更新收支统计
        this.expense = res.data.expendAmount;
        this.income = res.data.incomeAmount;

        // 更新列表数据
        if (page === 1) {
          // 第一页数据直接覆盖
          this.detailList = res.data.page?.records || [];
        } else {
          // 追加数据
          this.detailList = this.detailList.concat(res.data.page?.records);
        }

        // 通知mescroll数据加载完成
        this.mescroll.endSuccess(res.data.page?.records.length, res.data.page?.records.length >= this.pageSize);
      } else {
        // 加载失败
        this.mescroll.endErr();
        uni.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }

      uni.hideLoading();
    }
  }
};
</script>

<style lang="scss" scoped>
.fund-detail-container {
  background-color: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;

  .tab-container {
    display: flex;
    background-color: #fff;
    border-bottom: 1px solid #eee;

    .tab-item {
      flex: 1;
      height: 88rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30rpx;
      color: #666;
      position: relative;

      &.active {
        color: #FF6320;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 120rpx;
          height: 4rpx;
          background-color: #FF6320;
        }
      }
    }
  }

  .filter-container {
    display: flex;
    padding: 20rpx 30rpx;
    background-color: #fff;
    margin-bottom: 20rpx;

    .date-filter,
    .type-filter {
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      margin-right: 20rpx;
      font-size: 26rpx;
      color: #333;
    }
  }

  .summary-container {
    display: flex;
    padding: 30rpx;
    background-color: #fff;
    margin-bottom: 20rpx;

    .summary-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 10rpx;
      }

      .value {
        font-size: 36rpx;
        font-weight: 500;

        &.expense {
          color: #333;
        }

        &.income {
          color: #FF6320;
        }
      }
    }
  }

  .detail-list {
    background-color: #fff;
    padding: 0 30rpx;

    .detail-item {
      display: flex;
      padding: 30rpx 0;
      border-bottom: 1px solid #f5f5f5;

      .item-left {
        margin-right: 20rpx;

        .icon-wrapper {
          width: 70rpx;
          height: 70rpx;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;

          &.income-bg {
            background-color: #1E90FF;
          }

          &.expense-bg {
            background-color: #999;
          }

          .icon-text {
            color: #fff;
            font-size: 28rpx;
          }
        }
      }

      .item-middle {
        flex: 1;

        .item-title {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .item-time,
        .item-order {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 4rpx;
        }
      }

      .item-right {
        display: flex;
        align-items: center;
        font-size: 32rpx;
        font-weight: 500;

        .income-text {
          color: #FF6320;
        }

        .expense-text {
          color: #333;
        }
      }
    }
  }

  .mescroll-container {
    flex: 1;

    .mescroll-upwarp {
      min-height: auto;
      padding: 0;
    }
  }
}

/deep/ .mescroll-upwarp,
::v-deep .mescroll-upwarp {
  min-height: auto !important;
  padding: 0 !important;
}
</style>
