<template>
	<u-popup v-model="popShow" mode="center" width="600" border-radius="10">
		<view class="orderStyl">
			<image class="pic" src="~@/static/icon/qd.png" mode="widthFix" v-if="!config.icon"></image>
			<image class="pic" v-else :src="config.icon" mode="widthFix"></image>
			<view class="tripInfo">{{ config.title }}</view>
			<my-button width="500" margin-top="20" :background="config.topBtnBgColor" @confirm="confirm">{{ config.topBtnText }}</my-button>
			<my-button width="500" margin-top="30" :background="config.botBtnBgColor" color="#333" :border="true" @confirm="cancel">{{ config.botBtnText }}</my-button>
		</view>
	</u-popup>
</template>

<script>
export default {
	props: {
		config: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			popShow: false
		};
	},
	methods: {
		open() {
			this.popShow = true;
		},
		cancel() {
			this.popShow = false;
			this.$emit('cancel');
		},
		confirm() {
			this.$emit('confirm');
		}
	}
};
</script>

<style lang="scss" scoped>
.orderStyl {
	padding: 60rpx 0;
	text-align: center;

	.pic {
		width: 400rpx;
	}

	.tripInfo {
		font-size: 30rpx;
		padding: 20rpx 40rpx;
	}
}
</style>
