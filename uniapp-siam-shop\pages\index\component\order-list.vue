<template>
	<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :up="{ auto: false }">
		<view class="contInfo">
			<template v-for="(item, index) in orderList">
				<view class="margin-bottom" :key="index" @click="handClickOrderInfo(item.id)">
					<!-- <view class="grip" v-if="index == 0 && item.status == 1">新</view> -->
					<view class="contInfo-title-box flex flex-align-center">
						<u-icon name="bell-fill" size="36" color="#666"></u-icon>
						<view class="contInfo-title flex flex-align-start justify-space-between">
							<!-- <view class="contTitle">
								<text>{{ $common.calcTime(item.createdAt, parseInt(item.arrivalMinute) + parseInt(item.deliveryMinute), 1) }}分钟内送达</text>
								<view class="contTitle-label">预计{{ $common.calcTime(item.createdAt, parseInt(item.arrivalMinute) + parseInt(item.deliveryMinute), 2) }}前</view>
							</view> -->
							<view class="contTitle">
								<text>取餐号：{{ item.queueNo }}号</text>
								<view class="contTitle-label">{{ item.description }}</view>
							</view>
							<text class="contTitle-money">￥{{ item.actualPrice }}</text>
						</view>
						<u-icon name="arrow-right" size="36" color="#666"></u-icon>
					</view>
					<view class="line"></view>
					<view class="togoGood">
						<view class="togoods leftLine">
							<!-- <view class="goodstitle">
								<view>
									<text>{{ item.takeDist }}</text>
									<text class="goodstitle-name">km</text>
								</view>
								<view class="goodstitle-name goodstitle-name-trip">取</view>
							</view> -->
							<view class="address">
								<view class="address-title">骑手：{{ item.riderName ? item.riderName : '待骑手接单' }}</view>
								<!-- <text class="address-label">{{ item.startAddress }}</text> -->
							</view>
						</view>
						<view class="togoods">
							<view class="goodstitle">
								<view>
									<text>{{ item.distance }}</text>
									<text class="goodstitle-name">km</text>
								</view>
								<view class="goodstitle-name goodstitle-name-trip goodstitle-name-trip-bg">达</view>
							</view>
							<view class="address">
								<view class="address-title">{{ item.contactProvince + item.contactCity + item.contactArea + item.contactStreet + item.contactHouseNumber }}</view>
								<text class="address-label">
									<text>{{ item.contactRealname ? item.contactRealname.replace(/[^]/, '*') : '***' }}</text>
									<text>{{ item.contactPhone ? ' ' + item.contactPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '***'}}</text>
								</text>
								<!-- <view class="tipsbox flex-align-center flex flex-wrap">
									<view class="tipsbox-ls">中小商家</view>
									<view class="tipsbox-ls">经济</view>
									<view class="tipsbox-ls tipsbox-ls-color">1kg生鲜</view>
									<view class="tipsbox-ls">+3等级分</view>
								</view> -->
							</view>
						</view>
						<view class="msg2" v-if="item.status == 7 || item.status == 9">
							订单状态：{{item.status == 7 ? '售后处理中' : '售后处理完成'}}<br/>
							退款类型：{{item.refundType == 1 ? '已支付订单1分钟内被取消' : '已支付订单24小时内申请退款'}}<br/>
							退款方式：{{item.refundWay == 1 ? '全额退款' : '部分退款'}}<br/>
							退款原因：{{ refundReasonName[item.refundReason] }}<br/>
							退款金额：{{item.refundAmount}}元<br/>
							退款状态：{{ refundStatusName[item.refundStatus] }}<br/>
						</view>
						<view class="remarks" v-if="item.remark.trim()">
							<text style="color: #30B983;">备注：</text>
							{{ item.remark }}
						</view>
					</view>
<!--					<view class="msg" v-show="item.emptyBarrelNum > 0">-->
<!--						该用户存在X{{item.emptyBarrelNum}}个空桶未退-->
<!--					</view>-->
					<template v-if="status == 4">
						<view class="footerBoten flex flex-align-center">
							<my-button
								v-if="item.riderId"
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="190"
								color="#333"
								font-size="30"
								@confirm="setDial(item.riderMobile, 2)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>骑手</text>
							</my-button>
							
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								color="#000"
								:margin="false"
								:bold="true"
								width="190"
								font-size="30"
								@confirm="setDial(item.contactPhone, 1)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>用户</text>
							</my-button>
						</view>
						
						<!-- <my-button
							background="linear-gradient(45deg,#EE8131,#EC1613)"
							color="#fff"
							border-radius="26"
							margin-top="40"
							margin-bottom="20"
							width="650"
							@confirm="receivingOrders(item)"
						>
							抢单
						</my-button> -->
					</template>
					<template v-else-if="status == 5">
						<view class="footerBoten flex flex-align-center">
							<my-button
								v-if="item.riderId"
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="190"
								color="#333"
								font-size="30"
								@confirm="setDial(item.riderMobile, 2)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>骑手</text>
							</my-button>
							
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								color="#000"
								:margin="false"
								:bold="true"
								width="190"
								font-size="30"
								@confirm="setDial(item.contactPhone, 1)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>用户</text>
							</my-button>
							
							<!-- <my-button border-radius="8" width="500" font-size="30" :bold="true" :margin="false" @confirm="receivingOrders(item)" background="#48B36B">
								上报到店
							</my-button> -->
						</view>
					</template>
					<template v-else-if="status == 6">
						<view class="footerBoten flex flex-align-center">
							<my-button
								v-if="item.riderId"
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="190"
								color="#333"
								font-size="30"
								@confirm="setDial(item.riderMobile, 2)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>骑手</text>
							</my-button>
							
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								color="#000"
								:margin="false"
								:bold="true"
								width="190"
								font-size="30"
								@confirm="setDial(item.contactPhone, 1)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>用户</text>
							</my-button>
							<!-- <my-button border-radius="8" width="500" font-size="30" :bold="true" :margin="false" @confirm="receivingOrders(item)" background="#48B36B">
								确认取货
							</my-button> -->
						</view>
					</template>
					<template v-else-if="status == -1">
						<view class="footerBoten flex flex-align-center">
							<my-button
								v-if="item.riderId"
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="190"
								color="#333"
								font-size="30"
								@confirm="setDial(item.riderMobile, 2)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>骑手</text>
							</my-button>
							
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								color="#000"
								:margin="false"
								:bold="true"
								width="190"
								font-size="30"
								@confirm="setDial(item.contactPhone, 1)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>用户</text>
							</my-button>

							<my-button border-radius="8" width="150" background="#2a75ed" font-size="30" color="#fff" :bold="true" :margin="false" @confirm="agreeRefund(item)" v-if="item.status == 7">
								同意
							</my-button>
							
							<my-button border-radius="8" width="190" background="linear-gradient(45deg, rgb(238, 129, 49), rgb(236, 22, 19))" font-size="30" color="#fff" :bold="true" :margin="false" @confirm="disagreeRefund(item)" v-if="item.status == 7">
								不同意
							</my-button>
						</view>
					</template>
					<template v-else-if="status == 2">
						<view class="footerBoten flex flex-align-center">
							<my-button
								v-if="item.riderId"
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="190"
								color="#333"
								font-size="30"
								@confirm="setDial(item.riderMobile, 2)"
							>
								<text>接单</text>
							</my-button>
							
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								color="#000"
								:margin="false"
								:bold="true"
								width="190"
								font-size="30"
								@confirm="setDial(item.contactPhone, 1)"
							>
								<text>拒绝</text>
							</my-button>
						</view>
					</template>
					
					
					
					
					
					<!-- <template v-else-if="status == 5">
						<view class="footerBoten flex justify-space-between flex-align-center">
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="200"
								color="#333"
								font-size="30"
								@confirm="setDial(item.contactPhone, 2)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>联系骑手</text>
							</my-button>
							
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								color="#000"
								:margin="false"
								:bold="true"
								width="300"
								font-size="30"
								@confirm="setDial(item.contactPhone, 1)"
							>
								联系用户
							</my-button>
							
							
							<my-button border-radius="8" width="500" background="#2a75ed" font-size="30" color="#fff" :bold="true" :margin="false" @confirm="receivingOrders(item)">
								处理
							</my-button>
						</view>
					</template>
					<template v-else-if="status == 6">
						<view class="footerBoten flex justify-space-between flex-align-center">
							<my-button
								background="transparent"
								:border="true"
								border-radius="8"
								:margin="false"
								:bold="true"
								width="200"
								color="#333"
								font-size="30"
								@confirm="setDial(item.contactPhone, 2)"
							>
								<uni-icons type="phone-filled"></uni-icons>
								<text>联系</text>
							</my-button>
							<my-button border-radius="8" width="500" background="#2a75ed" font-size="30" color="#fff" :bold="true" :margin="false" @confirm="receivingOrders(item)">
								确认送达
							</my-button>
						</view>
					</template> -->
				</view>
			</template>
		</view>
	</mescroll-uni>
</template>

<script>
import { mapState } from 'vuex';
import JSONbig from 'json-bigint';
export default {
	props: {
		status: {
			type: String,
			default: '1'
		},
		topNumShow: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			refundStatusName: {
				1: '退款申请已提交',
				2: '等待商家处理',
				3: '商家拒绝退款',
				4: '等待平台处理',
				5: '平台拒绝退款，退款已关闭',
				6: '退款已关闭',
				7: '退款成功',
			},
			refundReasonName: {
				1: '信息填写错误',
				2: '送达时间选错了',
				3: '买错了/买少了',
				4: '商家缺货',
				5: '商家联系我取消',
				6: '配送太慢',
				7: '骑手联系我取消',
				8: '我不想要了',
				9: '商家通知我卖完了',
				10: '商家沟通态度差',
				11: '骑手沟通态度差',
				12: '送太慢了，等太久了',
				13: '商品撒漏/包装破损',
				14: '商家少送商品',
				15: '商家送错商品',
				16: '口味不佳/个人感受不好',
				17: '餐品内有异物',
				18: '食用后引起身体不适',
				19: '商品变质/有异味',
				20: '其他'
			},
			orderShow: true,
			hours: new Date().getHours(),
			orderList: [
				// {
				// 	id: 1,
				// 	status: 1,
				// 	revenue: '123',
				// 	takeDist: '56',
				// 	shopName: 'shopName',
				// 	startAddress: 'startAddress',
				// 	distance: 'distance',
				// 	endAddress: 'endAddress',
				// 	endUsername: 'endUsername',
				// 	contactPhone: 'contactPhone',
				// 	remark: 'remark',
				// 	emptyBarrelNum: 1,
				// 	startTel: '123',
				// 	arrivalMinute: 0,
				// 	deliveryMinute: 0,
				// }, {
				// 	id: 2,
				// 	status: 2,
				// 	revenue: '123',
				// 	takeDist: '56',
				// 	shopName: 'shopName2',
				// 	startAddress: 'startAddress2',
				// 	distance: '22',
				// 	endAddress: 'endAddress2',
				// 	endUsername: 'endUsername2',
				// 	contactPhone: 'contactPhone',
				// 	remark: 'note2',
				// 	emptyBarrelNum: 0,
				// 	startTel: '222',
				// 	arrivalMinute: 0,
				// 	deliveryMinute: 0,
				// }
			]
		};
	},
	computed: {
		...mapState(['address', 'UserInfo', 'configInfo'])
	},
	created() {
		uni.$on('RefreshOrderlist', this.getOrderList);
	},
	onLoad() {

	},
	methods: {
		// 主动触发下拉刷新
		refresh() {
			this.mescroll.triggerDownScroll();
		},
		handClickOrderInfo(msg) {
			uni.navigateTo({
				url: '/pages/detailsPage/orderDetails?id=' + msg
			});
		},
		// 下拉回调函数
		downCallback(e) {
			this.getOrderList()
			this.mescroll.resetUpScroll();
		},
		// 上拉加载更多
		upCallback(e) {
			this.getOrderList(e.num)
		},
		/**
		 * @param page 分页参数
		 * */
		// 获取订单列表
		async getOrderList(page = 1) {
			try {
				// const location = await this.getLocationAsync();
				let url = '/api-order/rest/merchant/order/list';
				let shoppingWay = 2;
				if (this.status == -1) {
					url = '/api-order/rest/merchant/order/afterSalesList';
				}
				this.$request({
					url,
					data: {
						pageNo: page,
						status: this.status,
						shoppingWay: shoppingWay,
						// todo: for test only
						// riderLat: 28.232363,
						// riderLng: 112.885538
					}
				}).then(res => {
					// #ifdef MP-WEIXIN
					res = JSONbig.parse(res)
					// #endif
					if (res.code === 200) {
						if (page === 1) {
							this.orderList = res.data.records;
							console.log("成功")
							console.log(this.orderList)
						} else {
							this.orderList = this.orderList.concat(res.data.records);
						}
						this.mescroll.endSuccess(res.data.total, res.data.total >= 10 ? true : true);
					} else {
						this.mescroll.endSuccess(0, false);
					}
				}).catch(err => {
					this.mescroll.endSuccess(0, false);
				});
			} catch (e) {
				 console.error('error:', e);
			}
		},

		async getLocationAsync() {
			return new Promise((resolve, reject) => {
				uni.getLocation({
					type: 'wgs84',
					altitude: true,
					isHighAccuracy: true,
					highAccuracyExpireTime: 500,
					success: (res) => {
						resolve(res)
					},
					fail: (res) => {
						reject(res)
					}
				})
			})
		},
		// 订单操作
		receivingOrders(item) {
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				// if (this.UserInfo.isWork != '1') {
				// 	this.$interactive.ShowToast(
				// 		{
				// 			title: '请先上线！'
				// 		},
				// 		false
				// 	);
				// 	return;
				// }
			} else {
				this.$interactive.ShowToast(
					{
						title: '请先进行实名认证！'
					},
					false
				);
				return;
			}
			/**
			 * { status } == 1表示新订单、 == 2 表示已接单待到店取商品 、 == 3 表示已到店取到商品 、==4表示取到商品待送达，
			 * { pickUpType } status == 3 时判断取货方式，当 pickUpType==0时需要去详情页，具体取货方式在订单详情页面判断 ，
			 * 当 pickUpType ！=0时 直接弹窗操作取货，
			 * 当 status == 4时 表示已经取到商品待送达 ，这事判断 { serviceType } 的值，
			 * serviceType ！= 0时直接弹窗操作送达，== 0时 跳订单详情页，具体送达方式在详情页判断
			 * status == 1 || 2 时直接弹窗操作
			 *
			 * 注：当前提示语为默认值，具体提示语在 index.vue下面进行判断提示，如果有好的方法可以具体优化
			 * { this.$emit('openRobOrder', { obj: item, title: '请确保在指定时效内到店和完成配送，取货和送货时注意使用礼貌用语。' }) }
			*/
			if (item.status == 3) {
				if (this.configInfo.pickUpType != 0) {
					uni.navigateTo({
						url: '/pages/detailsPage/orderDetails?id=' + item.id
					});
				} else {
					this.$emit('openRobOrder', { obj: item, title: '请确保在指定时效内到店和完成配送，取货和送货时注意使用礼貌用语。' });
				}
			} else if (item.status == 4) {
				if (this.configInfo.serviceType != 0) {
					uni.navigateTo({
						url: '/pages/detailsPage/orderDetails?id=' + item.id
					});
				} else {
					this.$emit('openRobOrder', { obj: item, title: '请确保在指定时效内到店和完成配送，取货和送货时注意使用礼貌用语。' });
				}
			} else {
				this.$emit('openRobOrder', { obj: item, title: '请确保在指定时效内到店和完成配送，取货和送货时注意使用礼貌用语。' });
			}
		},
		// 订单操作
		agreeRefund(item) {
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				// if (this.UserInfo.isWork != '1') {
				// 	this.$interactive.ShowToast(
				// 		{
				// 			title: '请先上线！'
				// 		},
				// 		false
				// 	);
				// 	return;
				// }
			} else {
				this.$interactive.ShowToast(
					{
						title: '请先进行实名认证！'
					},
					false
				);
				return;
			}
			
			uni.showModal({
				title: '提示',
				content: '确定同意退款吗?',
				confirmText: '确定',
				cancelText: '取消',
				confirmColor: '#FF0000', // 确定按钮颜色（可选）
				success: (res) => {
				  if (res.confirm) { // 用户点击确定
						let data = {
							id: item.id,
							status: 1
						};
						let url = '/api-order/rest/merchant/order/auditAfterSalesOrder';
						this.$request({ url: url, data: data }).then(res => {
							// #ifdef MP-WEIXIN
							res = JSON.parse(res)
							// #endif
							if (res.code === 200) {
								// 刷新订单列表
								uni.$emit('RefreshOrderlist', 1);
							} else {
								this.$interactive .ShowToast({ title: res.message }) .then(() => {
									uni.hideLoading();
								});
							}
						});
				  } else if (res.cancel) { // 用户点击取消
				  }
				}
			});
		},
		// 订单操作
		disagreeRefund(item) {
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				// if (this.UserInfo.isWork != '1') {
				// 	this.$interactive.ShowToast(
				// 		{
				// 			title: '请先上线！'
				// 		},
				// 		false
				// 	);
				// 	return;
				// }
			} else {
				this.$interactive.ShowToast(
					{
						title: '请先进行实名认证！'
					},
					false
				);
				return;
			}
			
			uni.showModal({
				title: '提示',
				content: '确定拒绝退款吗?',
				confirmText: '确定',
				cancelText: '取消',
				confirmColor: '#FF0000', // 确定按钮颜色（可选）
				success: (res) => {
				  if (res.confirm) { // 用户点击确定
						let data = {
							id: item.id,
							status: 2,
							opinion: "拒绝退款",
						};
						let url = '/api-order/rest/merchant/order/auditAfterSalesOrder';
						this.$request({ url: url, data: data }).then(res => {
							// #ifdef MP-WEIXIN
							res = JSON.parse(res)
							// #endif
							if (res.code === 200) {
								// 刷新订单列表
								uni.$emit('RefreshOrderlist', 1);
							} else {
								this.$interactive .ShowToast({ title: res.message }) .then(() => {
									uni.hideLoading();
								});
							}
						});
				  } else if (res.cancel) { // 用户点击取消
				  }
				}
			});
		},
		// 拨打电话
		setDial(tel, type) {
			if(!tel){
				return;
			}
			if (this.UserInfo.auditStatus == 2) {
				// if (this.UserInfo.isWork != '1') {
				// 	this.$interactive.ShowToast(
				// 		{
				// 			title: '请先上线！'
				// 		},
				// 		false
				// 	);
				// 	return;
				// }
			} else {
				this.$interactive.ShowToast(
					{
						title: '请先进行实名认证！'
					},
					false
				);
				return;
			}
			this.$emit('setDial', tel, type);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '../style/common.scss';
.contInfo {
	height: 2000rpx;
	padding: 20rpx;

	.footerBoten {
		padding-top: 40rpx;
		padding-bottom: 10rpx;
		padding-left: 10rpx;
		padding-right: 10rpx;
		display: flex;
		flex-wrap: wrap; /* 允许换行 */
		align-items: center; /* 确保垂直居中 */
		gap: 10px; /* 按钮间距 */
	}
}
.msg {
	width: 90%;
	padding:20rpx 90rpx;
	border-radius: 10rpx;
	background-color: #f5f5f5;
	margin: auto;
	margin-top: 20rpx;
	color: #fa000a;
	font-weight: 700;
	font-size: 30rpx;
}
.msg2 {
	width: 90%;
	padding:20rpx 30rpx;
	border-radius: 10rpx;
	background-color: #f5f5f5;
	margin: auto;
	margin-top: 20rpx;
	color: #fa000a;
	font-weight: 700;
	font-size: 30rpx;
}
</style>
