<template>
  <view class="personal-center">
    <u-select v-model="showisOperating" :list="[{label: '营业', value: 1},{label: '打烊', value: 2}]" @confirm="onShopStatus"></u-select>
    <!-- 顶部状态栏 -->
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left">
          <!-- <u-image class="icon-menu" v-if="leftIcon" src="./my-footer/static/menu.png" mode="widthFix" /> -->
          <u-icon name="list"  size="36" @click="openPopPage"></u-icon>
        </view>
        <view class="nav-title">个人中心</view>
        <view class="nav-right" @click="navigateTo('/pages/message/index')">
          <uni-icons type="chat" size="40" color="#333"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 店铺信息 -->
    <view class="shop-info">
      <view class="shop-avatar">
        <!-- <image src="/static/image/head.png" mode="aspectFill"></image> -->
        <image class="head-pic" :src="getImageUrl(UserInfo.shopLogoImg) || '/static/image/head.png'" mode="widthFix"></image>
      </view>
      <view class="shop-details">
        <view class="shop-name">{{ UserInfo.shopName || '待设置' }}</view>
        <view class="shop-status" @click="showisOperating = true">
          <view class="status-dot" :style="{ background: UserInfo.isOperating ? 'rgb(27, 185, 58)' : '' }"></view>
          <text>{{ UserInfo.isOperating == true ? '营业' : '打烊' }}</text>
          <uni-icons class="handerTitle-name-icon" size="28" type="arrowright"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 收入统计 -->
    <view class="revenue-stats">
      <view class="stats-card">
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-value">￥{{ UserInfo.balance || '0.00' }}</text>
            <text class="stat-label">账户余额</text>
          </view>
          <!-- <view class="stat-item">
            <text class="stat-value">0.0</text>
            <text class="stat-label">财务可提现</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">0.0</text>
            <text class="stat-label">保证金</text>
          </view> -->
          <view class="withdraw-btn" @click="openWithdraw">
            <text>提现</text>
          </view>
        </view>
        
        <view class="daily-stats">
          <view class="daily-item">
            <text class="daily-value">{{ UserInfo.todaySumMerchantIncome || 0 }}</text>
            <text class="daily-label">今日收入(元)</text>
          </view>
          <view class="daily-item">
            <text class="daily-value">{{ UserInfo.todayCountCancel || 0 }}</text>
            <text class="daily-label">今日取消(单)</text>
          </view>
          <view class="daily-item">
            <text class="daily-value">{{ UserInfo.todayCountPaid || 0 }}</text>
            <text class="daily-label">今日单量(单)</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 基础信息 -->
    <view class="section">
      <view class="section-title">基础信息</view>
      <view class="menu-grid">
        <view class="menu-item" @click="navigateTo('/pages/persCenter/persCenter')">
          <view class="menu-icon" style="background: #FF9500;">
            <uni-icons type="shop" size="40" color="#fff"></uni-icons>
          </view>
          <text class="menu-text">店铺信息</text>
        </view>
         <view class="menu-item" @click="openShopSet">
          <view class="menu-icon" style="background: #34C759;">
            <u-icon name="home" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">门店管理</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/shopData/index')">
          <view class="menu-icon" style="background: #FF3B30;">
            <u-icon name="bag" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">门店数据</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/myInfo/my-account?money=' + (UserInfo.balance || ''))">
          <view class="menu-icon" style="background: #007AFF;">
            <uni-icons type="person" size="40" color="#fff"></uni-icons>
          </view>
          <text class="menu-text">我的账户</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/setting/setting')">
          <view class="menu-icon" style="background: #5856D6;">
            <u-icon name="setting" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">设置</text>
        </view>
      </view>
    </view>

    <!-- 业务能力 -->
    <view class="section">
      <view class="section-title">业务能力</view>
      <view class="menu-grid">
        <view class="menu-item" @click="navigateTo(`/pages/goods/list?shopId=${UserInfo.shopId}`)">
          <view class="menu-icon" style="background: #34C759;">
            <u-icon name="shopping-cart" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">商品管理</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/index/index')">
          <view class="menu-icon" style="background: #FF9500;">
            <u-icon name="car" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">外卖订单</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/pickUpIndex/index')">
          <view class="menu-icon" style="background: #00C7BE;">
            <u-icon name="order" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">自提订单</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/fullReductionRuleList/list')">
          <view class="menu-icon" style="background: #FF9500;">
            <u-icon name="coupon" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">满减活动</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/couponsList/list')">
          <view class="menu-icon" style="background: #5856D6;">
            <u-icon name="gift" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">优惠卷</text>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/printer/list')">
          <view class="menu-icon" style="background: #007AFF;">
            <u-icon name="camera" size="40" color="#fff"></u-icon>
          </view>
          <text class="menu-text">打印机</text>
        </view>
        
      </view>
    </view>


    <my-footer ref="myFooter" />
  </view>
</template>

<script>
import { mapState } from 'vuex';
import MyFooter from './my-footer/index.vue';
import { getImageUrl } from '@/utils';

export default {
  computed: {
		...mapState(['UserInfo'])
	},
  components: {
    MyFooter
  },
  data() {
    return {
      getImageUrl,
      showisOperating: false
    }
  },
  onLoad() {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
			uni.$emit('RefreshUserInfo');
		},
    openShopSet() {
      this.$refs.myFooter.openShopSet();
    },
    openPopPage() {
      this.$refs.myFooter.openPopPage();
    },
    openWithdraw() {
      this.navigateTo('/pages/myInfo/my-account?money=' + (this.UserInfo.balance || ''))
    },
    navigateTo(url) {
      uni.navigateTo({
        url: url
      })
    },
    onShopStatus(values) {
      // debugger
      const isOperating = values[0].value === 1;
      this.showisOperating = false;
      if (this.UserInfo.isOperating === isOperating) return;
      uni.$emit('RefreshUserInfo');
			if (parseInt(this.UserInfo.auditStatus) == 2) {
				let id = this.UserInfo.shopId;
				this.$request({ url: '/api-merchant/rest/merchant/shop/update', data: { "id": id, "isOperating": isOperating } }).then(res => {
					// #ifdef MP-WEIXIN
					res = JSON.parse(res)
					// #endif
					if (res.code === 200) {
						uni.$emit('RefreshUserInfo');
						this.$interactive.ShowToast({ title: values[0].value === 1 ? '已成功营业！' : '已成功打烊！' }, false );
					} else {
						this.$interactive.ShowToast({ title: res.message }, false );
					}
				});
			} else {
				this.$interactive.ShowToast({ title: '请先进行实名认证！' }, false );
			}
      
    }
  }
}
</script>

<style lang="scss" scoped>
.personal-center {
  background-color: #f5f5f5;
  min-height: 100%;
  padding-bottom: 120rpx;
}

.status-bar {
  background-color: #fff;
}

.nav-bar {
  background-color: #fff;
  padding: 0 32rpx;
  position: sticky;
  top: 0;
  left: 0;
  z-index: 1;
  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .icon-menu {
      width: 50rpx;
    }
    .nav-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
    
    .nav-right {
      display: flex;
      align-items: center;
    }
  }
}

.shop-info {
  background-color: #fff;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  
  .shop-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 16rpx;
    overflow: hidden;
    margin-right: 30rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .shop-details {
    flex: 1;
    
    .shop-name {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
    }
    
    .shop-status {
      display: flex;
      align-items: center;
      
      .status-dot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background-color: #8E8E93;
        margin-right: 12rpx;
      }
      
      text {
        font-size: 28rpx;
        color: #8E8E93;
      }
    }
  }
}

.revenue-stats {
  margin: 20rpx 30rpx;
  
  .stats-card {
    border-radius: 20rpx;
    overflow: hidden;
    .stats-row {
      display: flex;
      align-items: center;
      padding: 32rpx 32rpx 20rpx;
      background-color: #1A2E60;
      .stat-item {
        flex: 1;
        text-align: center;
        
        .stat-value {
          display: block;
          font-size: 36rpx;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8rpx;
        }
        
        .stat-label {
          font-size: 24rpx;
          color: #B79D80;
        }
      }
      
      .withdraw-btn {
        background-color: #FDD08D;
        padding: 8rpx 32rpx;
        border-radius: 32rpx;
        
        text {
          color: #714F25;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
    }
    
    .daily-stats {
      display: flex;
      padding: 32rpx;
      background: #fff;
      color: #333;
      .daily-item {
        flex: 1;
        text-align: center;
        
        .daily-value {
          display: block;
          font-size: 32rpx;
          font-weight: 500;
          // color: #fff;
          margin-bottom: 8rpx;
        }
        
        .daily-label {
          font-size: 22rpx;
          // color: #8E8E93;
        }
      }
    }
  }
}

.section {
  margin: 40rpx 30rpx;
  background-color: #fff;
   border-radius: 20rpx;
  padding: 30rpx;
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .menu-grid {
    display: flex;
    flex-wrap: wrap;
  
    .menu-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40rpx;
      
      .menu-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16rpx;
      }
      
      .menu-text {
        font-size: 28rpx;
        color: #333;
        text-align: center;
      }
    }
  }
}
</style>