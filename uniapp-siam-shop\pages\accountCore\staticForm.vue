<!-- 统计日 -->
<template>
	<view class="todayOrder">
		<my-nav-bar background="#292B37" icon-color="#fff" color="#fff" :title="title || '今日订单'" btn-width="210" topDistance="80">
			<template slot="right-cont">
				<view class="flex flex-align-center" style="color: #fff;" @click="searchTime">
					<uni-icons type="search" color="#fff" size="40"></uni-icons>
					<text class="right-time">{{ time }}</text>
					<uni-icons type="arrowdown" color="#fff" size="24"></uni-icons>
				</view>
			</template>
		</my-nav-bar>
		<custom-tabs-swiper :list="list" :is-scroll="false" box-background="#292B37" inactive-color="#aaa" :bold="true"
			active-color="#fff" :box-padding="[80, 0, 10, 0]" @changePage="changePage">
			<template slot="swiper-item-1">
				<today-order-list ref="orderLs1" state="0"></today-order-list>
			</template>
			<template slot="swiper-item-2">
				<today-order-list ref="orderLs2" state="2"></today-order-list>
			</template>
			<template slot="swiper-item-3">
				<today-order-list ref="orderLs3" state="3"></today-order-list>
			</template>
			<template slot="swiper-item-4">
				<today-order-list ref="orderLs4" state="4"></today-order-list>
			</template>
			<template slot="swiper-item-5">
				<today-order-list ref="orderLs5" state="5"></today-order-list>
			</template>
			<template slot="swiper-item-6">
				<today-order-list ref="orderLs6" state="6"></today-order-list>
			</template>
		</custom-tabs-swiper>
		<u-calendar v-model="show" mode="date" @change="change"></u-calendar>
	</view>
</template>

<script>
	import CustomTabsSwiper from '@/components/custom-tabs-page-linkge/tab-page-linkage.vue';
	import TodayOrderList from './components/today-order-list.vue';
	export default {
		components: {
			CustomTabsSwiper,
			TodayOrderList
		},
		data() {
			return {
				show: false,
				title: '',
				time: this.$moment(new Date()).format('MM-DD'),
				dateForRequest: this.$moment(new Date()).format('yy-MM-dd'),
				list: [
					{
						name: '全部',
						refreshFlag: false
					},
					{
						name: '待到店',
						refreshFlag: false
					},
					{
						name: '待取货',
						refreshFlag: false
					},
					{
						name: '配送中',
						refreshFlag: false
					},
					{
						name: '已完成',
						refreshFlag: false
					},
					{
						name: '取消',
						refreshFlag: false
					}
				],
				tabNo: 1
			};
		},
		onLoad() {},
		methods: {
			changePage(msg) {
				this.tabNo = msg + 1;
				if (this.list[this.tabNo - 1].refreshFlag === true) {
					this.list[this.tabNo - 1].refreshFlag = false;
					this.$refs['orderLs' + this.tabNo].changeTime(this.dateForRequest);
				}
			},
			searchTime() {
				this.show = true;
			},
			change(msg) {
				const timeStamp = msg.result.replace(/\-/g, "")
				const timeStampToday = this.$moment(new Date()).format('YYYYMMDD')
				this.time = `${ msg.month >= 10 ? msg.month : '0' + msg.month }-${msg.day>=10 ? msg.day : '0' + msg.day}`
				this.$refs['orderLs' + this.tabNo].changeTime(msg.result);
				if (timeStamp == timeStampToday) {
					this.title = ""
				} else {
					this.title = this.time + '订单'
				}

				this.list.forEach((item => item.refreshFlag = true));
				this.list[this.tabNo - 1].refreshFlag = false;
				this.dateForRequest = msg.result;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.todayOrder {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: $uni-bg-color-grey;

		.right-text {
			font-size: 28rpx;
			margin-left: 10rpx;
		}

		.right-time {
			font-size: 28rpx;
			margin-bottom: 4rpx;
			width: 86rpx;
			text-align: right;
		}

		.orderList {
			padding: 20rpx;
		}
	}
</style>
